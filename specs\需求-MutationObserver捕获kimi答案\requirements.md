# 需求文档 - MutationObserver捕获Kimi答案

## 项目概述
在EchoAIExtention项目中，需要在Kimi平台适配器中实现对AI答案的实时捕获功能，以便用户能够获取和存储Kimi平台的AI回答内容。

## 功能需求

### 1. 核心功能
**目标**: 在`content/adapters/kimi/KimiAnswerService.ts`中实现Kimi平台的答案捕获机制

**技术方案**: 
- 使用MutationObserver监听页面DOM变化
- 配置参数：`{childList: true, subtree: true}`

### 2. 页面状态检测

#### 2.1 页面形态识别
- **形态1 (欢迎页)**: 包含`class="home-page"`的页面，只有输入框
- **形态2 (聊天页)**: 包含`class="chat-page chat"`的页面，包含聊天框

#### 2.2 页面跳转监听
- 监听从欢迎页到聊天页的跳转
- 通过观察`class="main"`容器下的DOM结构变化
- 检测到跳转后开始答案监听

### 3. 聊天内容结构解析

#### 3.1 聊天容器定位
- **聊天标题**: `class="chat-header"`中的`h2`元素
- **聊天内容列表**: `class="chat-content-list"`

#### 3.2 消息类型识别
- **用户问题**: `class="chat-content-item chat-content-item-user"`
  - 问题文本位于: `class="user-content"`
- **AI答案**: `class="chat-content-item chat-content-item-assistant"`
  - 答案文本位于: `class="markdown"`


### 3.3 问题捕捉
  
   * 用户问题捕捉机制说明
   * 
   * 检测机制:
   * - 使用 MutationObserver 监听DOM变化
   * - 检测到新增的 class="chat-content-item chat-content-item-user" 元素
   * - 表示用户发送了新的问题
   * 
   * 问题文本提取:
   * - 从新增的用户消息元素中查找 class="user-content" 子元素
   * - 提取其中的文本内容作为用户问题
   * 
   * 触发时机:
   * - 当用户点击发送按钮或按下回车键提交问题后
   * - DOM中出现新的用户消息节点时触发捕获
   * 
   * 数据处理:
   * - 将捕获的问题文本发送到后台进行存储和同步
   * - 配合答案捕捉机制形成完整的问答对
   

### 4. 答案捕获机制

#### 4.1 答案完成检测
- 监听新增的`class="chat-content-item chat-content-item-assistant"`节点
- 检测`class="segment-assistant-actions-content"`节点的出现
- **答案完成标志**: `segment-assistant-actions-content`下出现`class="simple-button size-small"`元素
- 该元素存在表示答案加载完毕

#### 4.2 内容提取方式
- 通过模拟点击`class="simple-button size-small"`元素触发复制功能
- 使用不申请权限的方式实现剪贴板操作
- 捕获复制到剪贴板的内容(已为markdown格式)
- 无需手动递归解析HTML结构

### 5. 数据存储结构

#### 5.1 按对话标题分组
- **对话标题**: 从`class="chat-header-content"`获取
- **数据结构**: 每个标题下的问题答案列表为一组
```typescript
interface ConversationData {
  title: string;
  qaList: {
    question: string;
    answer: string;
    timestamp: number;
  }[];
}

private conversationMap: Map<string, ConversationData> = new Map();
```

#### 5.2 数据组织方式
- 以对话标题为key存储完整对话
- 每个对话包含多个问答对
- 支持同一标题下的增量更新

## 技术要求

### 1. 监听器管理
```typescript
// 页面跳转监听器和答案监听器分离管理
private pageObserver: MutationObserver | null = null;
private answerObserver: MutationObserver | null = null;
private conversationMap: Map<string, ConversationData> = new Map();

public initCapture(adapter: BaseAIAdapter) {
    // 立即启动页面跳转监听
    this.setupPageTransitionListener();
}

private setupPageTransitionListener(): void {
    // 监听页面状态变化
}

private setupAnswerListener(): void {
    // 仅在聊天页启动答案监听
}

public destroy(): void {
    if (this.pageObserver) {
        this.pageObserver.disconnect();
        this.pageObserver = null;
    }
    if (this.answerObserver) {
        this.answerObserver.disconnect();
        this.answerObserver = null;
    }
}
```

### 2. 页面状态判断
```typescript
private isWelcomePage(): boolean {
    return document.querySelector('.home-page') !== null;
}

private isChatPage(): boolean {
    return document.querySelector('.chat-page.chat') !== null;
}
```

### 3. 监听条件与剪贴板操作
- **页面监听**: 在`initCapture`中立即启动
- **答案监听**: 仅在检测到聊天页面后启动
- **剪贴板操作**: 使用不申请权限的方式
```typescript
private async simulateClickAndGetContent(buttonElement: Element): Promise<string> {
    try {
        // 模拟点击复制按钮
        (buttonElement as HTMLElement).click();
        
        // 使用不申请权限的方式获取剪贴板内容
        // 具体实现避免使用navigator.clipboard API
        return await this.getClipboardContent();
    } catch (error) {
        console.error('获取剪贴板内容失败:', error);
        return '';
    }
}
```

## 验收标准

### 1. 功能验收
- [ ] 能正确识别Kimi页面的两种形态
- [ ] 能在initCapture中立即启动页面跳转监听
- [ ] 能检测到从欢迎页到聊天页的跳转
- [ ] 能在聊天页启动答案监听逻辑
- [ ] 能识别答案完成标志(`simple-button size-small`)
- [ ] 能正确提取对话标题和问答内容
- [ ] 能通过模拟点击获取markdown格式的答案
- [ ] 能按对话标题组织数据结构

### 2. 技术验收
- [ ] MutationObserver正确配置和管理
- [ ] 监听器在destroy时正确清理
- [ ] 数据正确存储到map中
- [ ] 控制台输出调试信息

### 3. 性能要求
- [ ] 监听机制不影响页面性能
- [ ] 及时清理资源避免内存泄漏
- [ ] 准确识别答案完成状态

## 错误处理策略

### 1. 错误类型与处理
- **剪贴板操作失败**: 打印错误日志，不中断流程
- **DOM选择器失效**: 打印警告信息，记录失败原因
- **MutationObserver异常**: 打印错误并尝试重新初始化
- **页面结构变更**: 打印兼容性警告

### 2. 日志记录规范
```typescript
// 统一错误日志格式
private logError(operation: string, error: any): void {
    console.error(`[KimiAnswerService] ${operation} 失败:`, error);
}

private logWarning(message: string): void {
    console.warn(`[KimiAnswerService] ${message}`);
}

private logInfo(message: string): void {
    console.info(`[KimiAnswerService] ${message}`);
}
```

## 风险评估

### 1. 技术风险
- Kimi页面DOM结构变更可能导致选择器失效
- Vue页面的动态渲染可能影响监听时机
- 浏览器安全策略可能限制剪贴板访问

### 2. 兼容性风险
- 不同版本的Kimi界面可能存在差异
- 浏览器对MutationObserver的支持差异

## 实现优先级
1. **P0**: 基础监听器设置和页面状态检测
2. **P1**: 答案完成检测和内容提取
3. **P2**: 数据存储和日志输出
4. **P3**: 性能优化和错误处理

## 相关文件
- `extension/src/content/adapters/kimi/KimiAnswerService.ts` - 主实现文件
- `extension/src/content/adapters/BaseAIAdapter.ts` - 基础适配器
- `extension/src/content/utils/DOMUtils.ts` - DOM工具类