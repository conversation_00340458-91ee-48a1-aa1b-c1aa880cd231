import { dexieDatabase } from '../database/dexie'
import { PlatformEntity } from '@/common/types/database_entity'

/**
 * 平台数据访问对象
 * 只负责数据库CRUD操作，不包含业务逻辑
 */
export class PlatformDao {
  private static instance: PlatformDao

  public static getInstance(): PlatformDao {
    if (!PlatformDao.instance) {
      PlatformDao.instance = new PlatformDao()
    }
    return PlatformDao.instance
  }

  /**
   * 创建平台记录
   */
  async create(platform: Omit<PlatformEntity, 'id'>): Promise<PlatformEntity> {
    await dexieDatabase.initialize()
    
    const id = await dexieDatabase.platform.add({
      ...platform,
      is_delete: 0
    })
    
    const created = await dexieDatabase.platform.get(id)
    if (!created) {
      throw new Error('Failed to create platform')
    }
    
    return created
  }

  /**
   * 根据ID查找平台记录
   */
  async findById(id: number): Promise<PlatformEntity | null> {
    await dexieDatabase.initialize()
    
    const platform = await dexieDatabase.platform.get(id)
    return platform || null
  }

  /**
   * 根据名称查找平台记录
   */
  async findByName(name: string): Promise<PlatformEntity | null> {
    await dexieDatabase.initialize()
    
    const platform = await dexieDatabase.platform
      .where('name')
      .equals(name)
      .and(item => item.is_delete === 0)
      .first()
    
    return platform || null
  }


  /**
   * 查找所有平台记录
   */
  async findAll(options: {
    includeDeleted?: boolean
    limit?: number
    offset?: number
  } = {}): Promise<PlatformEntity[]> {
    await dexieDatabase.initialize()
    
    const { includeDeleted = false, limit = 50, offset = 0 } = options
    
    let query = dexieDatabase.platform.toCollection()
    
    if (!includeDeleted) {
      query = dexieDatabase.platform.where('is_delete').equals(0)
    }
    
    return await query
      .offset(offset)
      .limit(limit)
      .toArray()
  }

  /**
   * 查找所有活跃的平台记录
   */
  async findAllActive(): Promise<PlatformEntity[]> {
    await dexieDatabase.initialize()
    
    return await dexieDatabase.platform
      .where('is_delete')
      .equals(0)
      .toArray()
  }

  /**
   * 更新平台记录
   */
  async update(id: number, updates: Partial<PlatformEntity>): Promise<PlatformEntity> {
    await dexieDatabase.initialize()
    
    const existing = await dexieDatabase.platform.get(id)
    if (!existing) {
      throw new Error(`PlatformEntity with id ${id} not found`)
    }
    
    await dexieDatabase.platform.update(id, updates)
    
    const updated = await dexieDatabase.platform.get(id)
    if (!updated) {
      throw new Error('Failed to update platform')
    }
    
    return updated
  }

  /**
   * 软删除平台记录
   */
  async softDelete(id: number): Promise<boolean> {
    await dexieDatabase.initialize()
    
    const existing = await dexieDatabase.platform.get(id)
    if (!existing) {
      return false
    }
    
    await dexieDatabase.platform.update(id, { is_delete: 1 })
    return true
  }

  /**
   * 硬删除平台记录
   */
  async delete(id: number): Promise<boolean> {
    await dexieDatabase.initialize()
    
    await dexieDatabase.platform.delete(id)
    return true
  }

  /**
   * 统计平台记录数量
   */
  async count(options: {
    includeDeleted?: boolean
  } = {}): Promise<number> {
    await dexieDatabase.initialize()
    
    const { includeDeleted = false } = options
    
    if (includeDeleted) {
      return await dexieDatabase.platform.count()
    } else {
      return await dexieDatabase.platform
        .where('is_delete')
        .equals(0)
        .count()
    }
  }

  /**
   * 批量创建平台记录
   */
  async bulkCreate(platforms: Omit<PlatformEntity, 'id'>[]): Promise<PlatformEntity[]> {
    await dexieDatabase.initialize()
    
    const records = platforms.map(platform => ({
      ...platform,
      is_delete: 0
    }))
    
    const ids = await dexieDatabase.platform.bulkAdd(records, { allKeys: true })
    
    const created = await dexieDatabase.platform
      .where('id')
      .anyOf(ids as number[])
      .toArray()
    
    return created
  }

  /**
   * 搜索平台记录（按名称或URL）
   */
  async search(searchTerm: string, options: {
    limit?: number
  } = {}): Promise<PlatformEntity[]> {
    await dexieDatabase.initialize()
    
    const { limit = 50 } = options
    
    return await dexieDatabase.platform
      .where('is_delete')
      .equals(0)
      .filter(item => 
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.url.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .limit(limit)
      .toArray()
  }

  /**
   * 检查平台名称是否存在
   */
  async nameExists(name: string, excludeId?: number): Promise<boolean> {
    await dexieDatabase.initialize()
    
    let query = dexieDatabase.platform
      .where('name')
      .equals(name)
      .and(item => item.is_delete === 0)
    
    if (excludeId) {
      query = query.and(item => item.id !== excludeId)
    }
    
    const count = await query.count()
    return count > 0
  }

  /**
   * 检查平台URL是否存在
   */
  async urlExists(url: string, excludeId?: number): Promise<boolean> {
    await dexieDatabase.initialize()
    
    let query = dexieDatabase.platform
      .where('url')
      .equals(url)
      .and(item => item.is_delete === 0)
    
    if (excludeId) {
      query = query.and(item => item.id !== excludeId)
    }
    
    const count = await query.count()
    return count > 0
  }

  /**
   * 更新平台图标
   */
  async updateIcon(id: number, icon?: string, iconBase64?: string): Promise<PlatformEntity> {
    await dexieDatabase.initialize()
    
    const updates: Partial<PlatformEntity> = {}
    if (icon !== undefined) updates.icon = icon
    if (iconBase64 !== undefined) updates.icon_base64 = iconBase64
    
    return await this.update(id, updates)
  }

  /**
   * 批量更新平台记录
   */
  async bulkUpdate(updates: Array<{ id: number; data: Partial<PlatformEntity> }>): Promise<PlatformEntity[]> {
    await dexieDatabase.initialize()
    
    const results: PlatformEntity[] = []
    
    for (const update of updates) {
      const updated = await this.update(update.id, update.data)
      results.push(updated)
    }
    
    return results
  }

  /**
   * 恢复软删除的平台记录
   */
  async restore(id: number): Promise<boolean> {
    await dexieDatabase.initialize()
    
    const existing = await dexieDatabase.platform.get(id)
    if (!existing || existing.is_delete === 0) {
      return false
    }
    
    await dexieDatabase.platform.update(id, { is_delete: 0 })
    return true
  }

  /**
   * 获取平台使用统计（需要关联查询聊天历史）
   */
  async getUsageStats(): Promise<Array<{ platform: PlatformEntity; chatCount: number }>> {
    await dexieDatabase.initialize()

    const platforms = await this.findAllActive()
    const results = []

    for (const platform of platforms) {
      const chatCount = await dexieDatabase.chatHistory
        .where('platform_id')
        .equals(platform.id!)
        .and(item => item.is_delete === 0)
        .count()

      results.push({
        platform,
        chatCount
      })
    }

    return results
  }

  /**
   * 清空表中所有数据（硬删除）
   * 注意：清空平台表会影响其他表的外键关系
   */
  async clear(): Promise<number> {
    await dexieDatabase.initialize()

    const count = await dexieDatabase.platform.count()
    await dexieDatabase.platform.clear()
    return count
  }
}

// 导出单例实例
export const platformDao = PlatformDao.getInstance()
