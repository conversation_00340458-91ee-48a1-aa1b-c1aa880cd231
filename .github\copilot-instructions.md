# GitHub Copilot 项目指令

## 项目信息

- **项目名称**: EchoSync - AI提示词同步器
- **项目类型**: Chrome 扩展程序 (Manifest V3)
- **技术栈**: TypeScript + React + Vite
- **仓库**: hammercui/EchoAIExtention
- **当前分支**: feature/start

## 通用规则

### 1. 语言要求
**必须使用中文回答所有问题和生成所有代码注释。**

### 2. 文档生成规则
**不要在每次回答后自动生成总结文档。**

仅在以下情况下创建文档：
- ✅ 用户明确要求创建文档
- ✅ 重大架构变更需要记录
- ✅ 新功能的详细设计文档
- ✅ API 或接口变更说明

### 3. 编译和运行规则
**本项目使用开发模式，不需要手动编译。**

- ✅ 项目已运行 `npm run dev`，自动监听文件变化
- ❌ 不要在回答后执行 `npx vite build` 或 `npm run build`
- ❌ 不要在回答后执行 `npm run dev`（已经在运行中）
- ✅ 修改代码后，Vite 会自动重新编译
- ✅ Chrome 扩展需要手动点击"重新加载"按钮

**编译相关提示：**
修改完成后，只需提醒用户：
```
✅ 代码已修改完成
💡 请在 Chrome 扩展管理页面点击"重新加载"按钮以应用更改
```

### 4. 项目结构理解

#### 目录结构
```
extension/
├── src/
│   ├── background/       # Service Worker
│   ├── content/          # Content Scripts
│   │   ├── service/      # 业务服务层
│   │   ├── model/        # 数据模型
│   │   ├── inject/       # 注入组件
│   │   └── components/   # UI 组件
│   ├── popup/            # 弹窗页面
│   ├── options/          # 设置页面
│   └── common/           # 共享代码
│       ├── service/      # 通用服务
│       ├── base/         # 基础类
│       └── types/        # 类型定义
├── public/
│   ├── manifest.json     # 扩展清单
│   └── icons/            # 图标资源
docs/                     # 项目文档
specs/                    # 需求规格
website/                  # 官网 (Next.js)
```

#### 架构模式
- **单例模式**: 所有 Service 继承 `Singleton` 基类
- **代理模式**: Content Script ↔ Background 通信使用 Proxy 类
- **消息传递**: 统一使用 `MessagingService`
- **状态管理**: 使用 `SettingsStorage` (chrome.storage.sync)
- **本地存储**: 使用 `IndexedDB` (大数据) 或 `chrome.storage.local`

### 5. 代码风格

#### TypeScript
```typescript
// ✅ 使用中文注释
/**
 * 文件系统访问服务
 * 使用 File System Access API 管理目录访问和文件写入
 */
export class FileSystemService extends Singleton<FileSystemService> {
  // 实现逻辑
}

// ✅ 单例导出
export const fileSystemService = FileSystemService.getInstance()
```

#### 日志输出
```typescript
// ✅ 使用中文日志
console.log('[FileSystemService] 请求用户选择目录')
console.error('[FileSystemService] 文件写入失败', error)
```

#### 错误处理
```typescript
// ✅ 统一错误处理
try {
  await someOperation()
} catch (error) {
  console.error('[ServiceName] 操作失败', error)
  throw new CustomError(
    ErrorCode.OPERATION_FAILED,
    error instanceof Error ? error.message : '未知错误'
  )
}
```

### 6. Chrome 扩展特性

#### Manifest V3
- 使用 Service Worker 替代 Background Page
- 使用 Declarative Net Request API
- 遵循 CSP (Content Security Policy)

#### 消息传递
```typescript
// ✅ 使用 MessagingService
const response = await MessagingService.sendToBackground(
  MessageType.YOUR_TYPE,
  payload
)

// ❌ 不要直接使用
chrome.runtime.sendMessage(...)
```

#### 存储策略
- `chrome.storage.sync`: 跨设备同步的配置 (小数据)
- `chrome.storage.local`: 本地配置 (中等数据)
- `IndexedDB`: 大量数据、DirectoryHandle 等

### 7. 调试技巧

#### 查看日志
```
1. 打开 Chrome DevTools
2. Content Script 日志 → 页面控制台
3. Background 日志 → chrome://extensions → 查看视图 → Service Worker
4. Popup 日志 → 右键 Popup → 检查
```

#### 重新加载
```
修改代码后：
1. 访问 chrome://extensions
2. 找到 EchoSync 扩展
3. 点击"重新加载"按钮
4. 刷新测试页面
```

### 8. 常见任务

#### 添加新功能
1. 在 `specs/` 中创建需求文档（仅重大功能）
2. 在相应目录实现功能
3. 添加必要的类型定义
4. 测试功能
5. 提醒用户重新加载扩展

#### 修复 Bug
1. 定位问题（查看日志）
2. 修改代码
3. 验证修复
4. 提醒用户重新加载

#### 添加消息类型
1. 在 `common/types/enums.ts` 添加 `MessageType`
2. 在 `background/messageHandler.ts` 添加处理器
3. 在 `common/service/` 创建 Proxy 类（如需要）
4. 在业务代码中使用

### 9. 文件命名规范

- **Service**: `XxxService.ts` (如 `FileSystemService.ts`)
- **Proxy**: `XxxProxy.ts` (如 `SaveFileProxy.ts`)
- **Model**: `XxxModel.ts` (如 `AnswerModel.ts`)
- **Component**: `XxxComponent.tsx` (如 `ObsidianExportModal.tsx`)
- **Types**: `xxx.ts` (如 `enums.ts`, `interfaces.ts`)

### 10. Git 工作流

- **主分支**: `main`
- **开发分支**: `feature/start`
- **提交信息**: 使用中文，清晰描述变更

### 11. 响应模板

#### 修改代码后的标准响应
```markdown
## ✅ 修改完成

### 修改内容
- [文件名] - [修改说明]
- [文件名] - [修改说明]

### 测试步骤
1. 在 Chrome 扩展管理页面点击"重新加载"
2. [具体测试步骤]
3. [预期结果]

### 下一步
[如有需要，说明后续操作]
```

#### 回答问题的标准响应
```markdown
## 问题分析

[分析问题原因]

## 解决方案

[提供解决方案，使用中文]

## 代码示例

[如需要，提供代码示例]
```

## 项目特定知识

### File System Access API
- 本项目使用原生 File System Access API
- DirectoryHandle 存储在 IndexedDB
- 目录元信息存储在 SettingsStorage
- 首次使用引导用户选择目录
- 后续自动加载已保存的句柄

### Kimi.ai 集成
- 监听页面 DOM 变化捕获答案
- 使用 MutationObserver
- 解析 AI 回答内容
- 支持导出到 Obsidian

### 消息传递架构
```
Content Script
    ↓
Proxy Layer (如 SaveFileProxy)
    ↓
MessagingService
    ↓
Background (messageHandler)
    ↓
Chrome API
```

## 总结

**核心原则：**
1. ✅ 使用中文
2. ❌ 不自动生成文档
3. ❌ 不执行编译命令
4. ✅ 提醒重新加载扩展
5. ✅ 遵循项目架构模式
6. ✅ 保持代码风格一致

**记住：开发模式会自动编译，只需提醒用户重新加载扩展即可！**
