export interface OptionsSettings {
  syncEnabled: boolean
  autoSync: boolean
  saveHistory: boolean
  platforms: PlatformSetting[]
  shortcuts: ShortcutSettings
  theme: 'light' | 'dark' | 'system'
  language: 'zh' | 'en'
}

export interface PlatformSetting {
  id: string
  name: string
  enabled: boolean
  autoCapture: boolean
}

export interface ShortcutSettings {
  openPopup: string
  quickSync: string
}

export interface SettingSectionProps {
  title: string
  description?: string
  children: React.ReactNode
}