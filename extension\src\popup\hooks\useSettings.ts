import { useState, useEffect, useCallback } from 'react'
import { settingsStorage, type SyncSettings, type RuntimeDeviceInfo, type DeviceConfig } from '@/common/service/SettingsStorageService'
import { fileSelectorService } from '../services/FileSelector'

// 组合类型：同步设置 + 当前设备信息
type SettingsWithDevice = SyncSettings & { currentDevice: RuntimeDeviceInfo }

export function useSettings() {
  const [settings, setSettings] = useState<SettingsWithDevice | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 加载设置
  const loadSettings = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const loadedSettings = await settingsStorage.getSettings()
      setSettings(loadedSettings)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load settings')
      console.error('Failed to load settings:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  // 更新设备路径
  const updateDevicePath = useCallback(async (
    platform: keyof SyncSettings['platforms'], 
    deviceId: string, 
    path: string
  ) => {
    try {
      await settingsStorage.updateDevicePath(platform, deviceId, path)
      await loadSettings()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update device path')
      console.error('Failed to update device path:', err)
    }
  }, [loadSettings])

  // 添加设备
  const addDevice = useCallback(async (platform: keyof SyncSettings['platforms'], device: DeviceConfig) => {
    try {
      await settingsStorage.addDevice(platform, device)
      await loadSettings()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add device')
      console.error('Failed to add device:', err)
    }
  }, [loadSettings])

  // 删除设备
  const removeDevice = useCallback(async (platform: keyof SyncSettings['platforms'], deviceId: string) => {
    try {
      await settingsStorage.removeDevice(platform, deviceId)
      await loadSettings()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove device')
      console.error('Failed to remove device:', err)
    }
  }, [loadSettings])

  // 浏览文件夹
  const browseFolder = useCallback(async (platform: keyof SyncSettings['platforms'], deviceId: string) => {
    try {
      if (!settings) return

      // 获取当前路径
      const device = settings.platforms[platform].devices[deviceId]
      const currentPath = device?.path || ''

      console.log('Opening folder selector for device:', deviceId, 'current path:', currentPath)

      // 使用文件选择服务
      const result = await fileSelectorService.selectDirectory(currentPath)

      if (result.success && result.path) {
        // 验证路径格式
        const validation = fileSelectorService.validatePath(result.path)
        if (!validation.valid) {
          setError(validation.error || 'Invalid path format')
          return
        }

        console.log('Selected path:', result.path)
        console.log('Updating device path for platform:', platform, 'deviceId:', deviceId, 'path:', result.path)
        await updateDevicePath(platform, deviceId, result.path)
        console.log('Device path updated successfully')
      } else if (result.error) {
        console.log('Folder selection cancelled or failed:', result.error)
        // 不设置错误状态，因为用户取消是正常行为
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to browse folder')
      console.error('Failed to browse folder:', err)
    }
  }, [settings, updateDevicePath])

  // 初始化
  useEffect(() => {
    loadSettings()
  }, [loadSettings])

  // 监听设置变化
  useEffect(() => {
    const handleSettingsChanged = (newSettings: SettingsWithDevice) => {
      setSettings(newSettings)
    }

    settingsStorage.onSettingsChanged(handleSettingsChanged)
  }, [])

  return {
    settings,
    loading,
    error,
    updateDevicePath,
    addDevice,
    removeDevice,
    browseFolder,
    reload: loadSettings
  }
}