import React, { useState } from 'react'
import { ChevronDown, ChevronRight, Trash2 } from 'lucide-react'
import ConfirmDialog from './ConfirmDialog'

interface DataTableProps {
  data: any[]
  loading?: boolean
  className?: string
  tableName?: string
  onDeleteRow?: (rowData: any, index: number) => void
}

interface CellProps {
  value: any
  maxLength?: number
}

/**
 * 格式化值显示
 */
const formatValue = (value: any): { display: string; type: string } => {
  if (value === null || value === undefined) {
    return { display: 'null', type: 'null' }
  }

  if (typeof value === 'boolean') {
    return { display: value ? 'true' : 'false', type: 'boolean' }
  }

  if (typeof value === 'number') {
    // 检查是否是时间戳（10位或13位数字）
    if (value > 1000000000 && value < 9999999999999) {
      const date = new Date(value > 9999999999 ? value : value * 1000)
      return {
        display: `${value} (${date.toLocaleString()})`,
        type: 'timestamp'
      }
    }
    return { display: String(value), type: 'number' }
  }

  if (typeof value === 'object') {
    try {
      return { display: JSON.stringify(value, null, 2), type: 'object' }
    } catch {
      return { display: String(value), type: 'string' }
    }
  }

  const stringValue = String(value)
  // 检查是否包含换行符
  if (stringValue.includes('\n') || stringValue.includes('\r')) {
    return { display: stringValue, type: 'multiline-string' }
  }

  return { display: stringValue, type: 'string' }
}

/**
 * 表格单元格组件，支持长文本截断和展开
 */
const TableCell: React.FC<CellProps> = ({ value, maxLength = 50 }) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const { display, type } = formatValue(value)

  if (type === 'null') {
    return <span className="text-gray-400 italic">null</span>
  }

  const needsTruncation = display.length > maxLength

  // 根据数据类型设置不同的样式
  const getTypeStyles = () => {
    switch (type) {
      case 'boolean':
        return 'text-purple-600 font-mono'
      case 'number':
        return 'text-blue-600 font-mono'
      case 'timestamp':
        return 'text-green-600 font-mono text-xs'
      case 'object':
        return 'text-orange-600 font-mono text-xs'
      case 'multiline-string':
        return 'text-gray-900'
      default:
        return 'text-gray-900'
    }
  }

  if (!needsTruncation) {
    return (
      <span className={`break-words ${getTypeStyles()}`}>
        {(type === 'object' || type === 'multiline-string') ? (
          <pre className="whitespace-pre-wrap">{display}</pre>
        ) : (
          display
        )}
      </span>
    )
  }

  return (
    <div className="flex items-start gap-1">
      <span className={`break-words flex-1 ${getTypeStyles()}`}>
        {(type === 'object' || type === 'multiline-string') ? (
          <pre className="whitespace-pre-wrap">
            {isExpanded ? display : `${display.slice(0, maxLength)}...`}
          </pre>
        ) : (
          isExpanded ? display : `${display.slice(0, maxLength)}...`
        )}
      </span>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex-shrink-0 p-1 hover:bg-gray-100 rounded transition-colors"
        title={isExpanded ? '收起' : '展开'}
      >
        {isExpanded ? (
          <ChevronDown className="w-3 h-3 text-gray-500" />
        ) : (
          <ChevronRight className="w-3 h-3 text-gray-500" />
        )}
      </button>
    </div>
  )
}

/**
 * 数据表格组件
 */
export const DataTable: React.FC<DataTableProps> = ({
  data,
  loading = false,
  className = '',
  tableName,
  onDeleteRow
}) => {
  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean
    rowData: any
    position: { x: number; y: number }
  }>({
    isOpen: false,
    rowData: null,
    position: { x: 0, y: 0 }
  })

  // 处理删除按钮点击
  const handleDeleteClick = (rowData: any, event: React.MouseEvent) => {
    if (!onDeleteRow) return

    const rect = event.currentTarget.getBoundingClientRect()
    setConfirmDialog({
      isOpen: true,
      rowData,
      position: {
        x: rect.left + rect.width / 2,
        y: rect.top
      }
    })
  }

  // 确认删除
  const handleConfirmDelete = () => {
    if (onDeleteRow && confirmDialog.rowData) {
      onDeleteRow(confirmDialog.rowData, 0)
    }
    setConfirmDialog({ isOpen: false, rowData: null, position: { x: 0, y: 0 } })
  }

  // 取消删除
  const handleCancelDelete = () => {
    setConfirmDialog({ isOpen: false, rowData: null, position: { x: 0, y: 0 } })
  }
  if (loading) {
    return (
      <div className={`flex items-center justify-center py-12 ${className}`}>
        <div className="flex items-center gap-2 text-gray-500">
          <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
          <span>加载中...</span>
        </div>
      </div>
    )
  }
  
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center py-12 ${className}`}>
        <div className="text-center text-gray-500">
          <div className="text-4xl mb-2">📭</div>
          <div className="text-lg font-medium">暂无数据</div>
          <div className="text-sm">当前表中没有任何记录</div>
        </div>
      </div>
    )
  }
  
  // 获取表头（所有数据的键的并集）
  const headers = Array.from(
    new Set(data.flatMap(item => Object.keys(item)))
  ).sort()

  return (
    <div className={`overflow-hidden border border-gray-200 rounded-lg ${className}`}>
      {/* 表名显示 */}
      {tableName && (
        <div className="px-4 py-2 bg-blue-50 border-b border-blue-200">
          <h3 className="text-sm font-medium text-blue-900">
            表名: <span className="font-mono">{tableName}</span>
          </h3>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="w-full table-auto">
          <thead className="bg-gray-50 border-b border-gray-200">
            <tr>
              {headers.map((header) => (
                <th
                  key={header}
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  style={{ minWidth: '120px' }}
                >
                  {header}
                </th>
              ))}
              {onDeleteRow && (
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                  操作
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((row, index) => (
              <tr key={index} className="hover:bg-gray-50 transition-colors">
                {headers.map((header) => (
                  <td
                    key={header}
                    className="px-4 py-3 text-sm text-gray-900"
                    style={{ minWidth: '120px' }}
                  >
                    <TableCell value={row[header]} />
                  </td>
                ))}
                {onDeleteRow && (
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <button
                      onClick={(e) => handleDeleteClick(row, e)}
                      className="
                        inline-flex items-center gap-1 px-2 py-1
                        text-xs font-medium text-red-600
                        bg-red-50 border border-red-200 rounded-md
                        hover:bg-red-100 hover:border-red-300
                        transition-colors
                      "
                      title="删除这行数据"
                    >
                      <Trash2 className="w-3 h-3" />
                      删除
                    </button>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 确认删除对话框 */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        title="确认删除"
        message="确定要删除这条记录吗？此操作不可撤销。"
        confirmText="删除"
        cancelText="取消"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        position={confirmDialog.position}
        variant="danger"
      />
    </div>
  )
}

export default DataTable
