# 需求-MutationObserver捕获kimi答案

计划在content/adapter/kimi/KimiAnswerService.ts中实现对kimi的答案的捕获。

## 技术方案
1. 技术方案为使用MutationObserver监听页面的DOM变化，并解析出答案。
2. 在initCapture方法中增加一个监听器，destory方法中移除监听器。监听器的属性为childList: true,        subtree: true模式

## 页面内容
1. 监听的问题和答案为典型的流式聊天布局，但是页面有2中形态，包含class="home-page"为形态1即只有输入框的欢迎页。包含class="chat-page chat"为形态2即包含聊天框的聊天页。其中class="chat-header"为聊天框的标题，标题文本为h2。
2. chat-content-list为聊天框的聊天内容列表，class="chat-content-item chat-content-item-user"表示问题，class="chat-content-item chat-content-item-assistant"表示答案，依次排列，成对存在。
3. 问题的文本为class="user-content"
4. 答案的文本class="markdown"，子节点为文本，或者markdown内容，可以使用通用的递归解析，把html内容还原为markdown格式的文本。

## 需求

1. 当判断当前页面为kimi的欢迎页时，不进行监听。
2. 但是要捕捉到页面从欢迎页跳转到聊天页的过渡，并开始监听，通过观察dom结构发现，这是一个典型的vue开发的页面。他们都位于class="main"之下。
3. 当监听到新增class="chat-content-item chat-content-item-assistant"时，也要捕捉到class="segment-assistant-actions-content"节点，这个节点存在时，表明答案已经加载完毕了，点击这个节点，会触发复制答案功能，然后通过代码捕捉复制到粘贴板的内容，就是markdown，这种方案不需要再递归解析html了，拿到的就是markdown了。
4. 问题和答案的文本，都打印并存储到KimiAnswerService的map中。

请先分析我的需求，生成需求文档，specs/需求-MutationObserver捕获kimi答案/requirement.md中，跟我确认需求文档无误。