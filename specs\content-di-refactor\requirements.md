# 需求文档

## 介绍

改造content模块的注入方式，使用依赖注入（DI）框架替代直接实例化，解决模块间耦合问题，提高代码的可测试性和可维护性。

## 需求

### 需求 1 - 实现简单DI框架

**用户故事：** 作为开发者，我希望有一个简单的依赖注入框架，能够管理content模块中的依赖关系，使模块间解耦。

#### 验收标准

1. When 创建DI容器时，系统应当提供注册和解析依赖的能力。
2. When 注册依赖时，系统应当支持单例和瞬态两种生命周期管理。
3. When 解析依赖时，系统应当自动注入所需的依赖项。
4. When 依赖不存在时，系统应当抛出明确的错误信息。

### 需求 2 - 改造InputCapture模块

**用户故事：** 作为开发者，我希望InputCapture通过依赖注入获取InputModel，而不是直接实例化。

#### 验收标准

1. When 创建InputCapture实例时，系统应当通过构造函数注入InputModel依赖。
2. When InputCapture需要使用InputModel时，系统应当使用注入的实例而不是直接调用getInstance()。
3. When 进行单元测试时，系统应当能够轻松注入mock的InputModel实例。

### 需求 3 - 改造ArchiveButtonInject模块

**用户故事：** 作为开发者，我希望ArchiveButtonInject通过依赖注入获取InputModel，而不是直接实例化。

#### 验收标准

1. When 创建ArchiveButtonInject实例时，系统应当通过构造函数注入InputModel依赖。
2. When ArchiveButtonInject需要使用InputModel时，系统应当使用注入的实例而不是直接调用getInstance()。
3. When 进行单元测试时，系统应当能够轻松注入mock的InputModel实例。

### 需求 4 - 更新BaseAIAdapter

**用户故事：** 作为开发者，我希望BaseAIAdapter能够配置和使用DI容器来创建子模块。

#### 验收标准

1. When BaseAIAdapter初始化时，系统应当配置DI容器并注册所有必要的依赖。
2. When 创建子模块时，系统应当通过DI容器解析依赖并创建实例。
3. When 销毁适配器时，系统应当清理DI容器中的资源。

### 需求 5 - 保持向后兼容

**用户故事：** 作为开发者，我希望改造后的代码能够保持现有功能不变。

#### 验收标准

1. When 用户使用插件时，系统应当保持所有现有功能正常工作。
2. When 输入框聚焦时，系统应当正常触发相关事件。
3. When 点击存档按钮时，系统应当正常执行存档功能。
4. When 发送消息时，系统应当正常生成chatUid并触发相关事件。
