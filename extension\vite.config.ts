import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { crx } from '@crxjs/vite-plugin'
import path from 'path'
import manifest from './public/manifest.json'

export default defineConfig(({ command, mode }) => ({
  plugins: [
    react({
      babel: {
        parserOpts: {
          plugins: ['decorators-legacy', 'classProperties']
        }
      }
    }),
    crx({
      manifest,
      contentScripts: {
        injectCss: true
      },
      browser: 'chrome'
    })
  ],
  esbuild: {
    target: 'es2018'
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    minify: mode === 'production',
    sourcemap: command === 'serve',
    rollupOptions: {
      output: {
        inlineDynamicImports: false,
        manualChunks: undefined
      }
    }
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(mode === 'production' ? 'production' : 'development')
  },
  server: {
    port: 5173,
    strictPort: true,
    hmr: {
      port: 5174
    },
    cors: {
      origin: true,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization', 'Cache-Control']
    },
    headers: {
      'Access-Control-Allow-Origin': '*',
      
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control'
    }
  },
  configureServer(server) {
    server.middlewares.use((req, res, next) => {
      // 处理Chrome扩展的预检请求
      if (req.method === 'OPTIONS') {
        res.setHeader('Access-Control-Allow-Origin', '*')
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control')
        res.setHeader('Access-Control-Max-Age', '86400')
        res.statusCode = 200
        res.end()
        return
      }
      next()
    })
  }
}))
