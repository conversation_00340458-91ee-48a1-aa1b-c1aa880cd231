import React from 'react'
import type { SettingSectionProps } from '../types/settings'

export const SettingSection: React.FC<SettingSectionProps> = ({
  title,
  description,
  children
}) => {
  return (
    <div className="bg-white rounded-lg shadow p-6 mb-6">
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
        {description && (
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        )}
      </div>
      <div className="space-y-4">
        {children}
      </div>
    </div>
  )
}