/**
 * Tag 标签选择器组件
 * 支持添加、删除标签，显示标签列表
 */
export class TagSelector {
    private container: HTMLElement;
    private tags: string[] = [];
    private inputElement: HTMLInputElement | null = null;
    private tagListElement: HTMLElement | null = null;
    private onChangeCallback: ((tags: string[]) => void) | null = null;

    constructor() {
        this.container = this.createContainer();
    }

    /**
     * 创建容器
     */
    private createContainer(): HTMLElement {
        const container = document.createElement('div');
        container.className = 'tag-selector';
        
        // 创建输入框包装器
        const inputWrapper = document.createElement('div');
        inputWrapper.className = 'tag-input-wrapper';
        
        // 创建输入框
        this.inputElement = document.createElement('input');
        this.inputElement.type = 'text';
        this.inputElement.className = 'tag-input';
        this.inputElement.placeholder = '输入标签，回车添加';
        this.inputElement.addEventListener('keydown', this.handleKeyDown.bind(this));
        
        inputWrapper.appendChild(this.inputElement);
        
        // 创建标签列表
        this.tagListElement = document.createElement('div');
        this.tagListElement.className = 'tag-list';
        
        container.appendChild(inputWrapper);
        container.appendChild(this.tagListElement);
        
        return container;
    }

    /**
     * 处理键盘事件
     */
    private handleKeyDown(event: KeyboardEvent): void {
        if (event.key === 'Enter') {
            event.preventDefault();
            this.addTagFromInput();
        }
    }

    /**
     * 从输入框添加标签
     */
    private addTagFromInput(): void {
        if (!this.inputElement) return;
        
        const value = this.inputElement.value.trim();
        if (value) {
            this.addTag(value);
            this.inputElement.value = '';
        }
    }

    /**
     * 添加标签
     */
    public addTag(tag: string): void {
        const trimmedTag = tag.trim();
        
        // 验证标签
        if (!trimmedTag) {
            console.warn('[TagSelector] 标签不能为空');
            return;
        }
        
        // 检查重复
        if (this.tags.includes(trimmedTag)) {
            console.warn('[TagSelector] 标签已存在:', trimmedTag);
            return;
        }
        
        // 添加到数组
        this.tags.push(trimmedTag);
        
        // 渲染标签
        this.renderTag(trimmedTag);
        
        // 触发回调
        this.notifyChange();
    }

    /**
     * 渲染单个标签
     */
    private renderTag(tag: string): void {
        if (!this.tagListElement) return;
        
        const tagElement = document.createElement('span');
        tagElement.className = 'tag';
        tagElement.setAttribute('data-tag', tag);
        
        // 标签文本
        const textSpan = document.createElement('span');
        textSpan.className = 'tag-text';
        textSpan.textContent = tag;
        
        // 删除按钮
        const removeButton = document.createElement('button');
        removeButton.className = 'tag-remove';
        removeButton.textContent = '×';
        removeButton.setAttribute('aria-label', `删除标签 ${tag}`);
        removeButton.addEventListener('click', () => this.removeTag(tag));
        
        tagElement.appendChild(textSpan);
        tagElement.appendChild(removeButton);
        
        this.tagListElement.appendChild(tagElement);
    }

    /**
     * 删除标签
     */
    public removeTag(tag: string): void {
        // 从数组中移除
        const index = this.tags.indexOf(tag);
        if (index > -1) {
            this.tags.splice(index, 1);
        }
        
        // 从 DOM 中移除
        if (this.tagListElement) {
            const tagElement = this.tagListElement.querySelector(`[data-tag="${tag}"]`);
            if (tagElement) {
                tagElement.remove();
            }
        }
        
        // 触发回调
        this.notifyChange();
    }

    /**
     * 设置标签列表
     */
    public setTags(tags: string[]): void {
        // 清空现有标签
        this.clearTags();
        
        // 添加新标签
        tags.forEach(tag => this.addTag(tag));
    }

    /**
     * 获取当前标签列表
     */
    public getTags(): string[] {
        return [...this.tags];
    }

    /**
     * 清空所有标签
     */
    public clearTags(): void {
        this.tags = [];
        if (this.tagListElement) {
            this.tagListElement.innerHTML = '';
        }
        this.notifyChange();
    }

    /**
     * 设置变化回调
     */
    public onChange(callback: (tags: string[]) => void): void {
        this.onChangeCallback = callback;
    }

    /**
     * 通知标签变化
     */
    private notifyChange(): void {
        if (this.onChangeCallback) {
            this.onChangeCallback(this.getTags());
        }
    }

    /**
     * 渲染组件
     */
    public render(): HTMLElement {
        return this.container;
    }

    /**
     * 聚焦输入框
     */
    public focus(): void {
        if (this.inputElement) {
            this.inputElement.focus();
        }
    }

    /**
     * 销毁组件
     */
    public destroy(): void {
        if (this.inputElement) {
            this.inputElement.removeEventListener('keydown', this.handleKeyDown);
        }
        this.container.remove();
    }
}
