import { ObsidianExportButton } from './components/ObsidianExportButton';
import { ObsidianExportModal } from './components/ObsidianExportModal';
import SelectorManager from '../configs/SelectorManager';
import { DOMUtils } from '../utils/DOMUtils';

/**
 * Obsidian 导出注入器
 * 监听答案完成事件，在每个答案中注入导出按钮
 */
export class ObsidianExportInject {
    private buttons: Map<Element, ObsidianExportButton> = new Map();
    private isActive: boolean = false;

    constructor() {
        this.setupEventListeners();
    }

    /**
     * 设置事件监听
     */
    private setupEventListeners(): void {
        // 监听答案提取完成事件
        document.addEventListener('ANSWER_EXTRACTED', this.handleAnswerExtracted.bind(this));
        console.info('[ObsidianExportInject] 事件监听器已设置');
    }

    /**
     * 处理答案提取完成事件
     */
    private handleAnswerExtracted(event: Event): void {
        if (!this.isActive) {
            return;
        }

        const customEvent = event as CustomEvent;
        const { answerElement, answerIndex } = customEvent.detail;

        console.info('[ObsidianExportInject] 收到答案提取事件', { answerIndex });

        // 注入导出按钮
        this.injectButton(answerElement, answerIndex);
    }

    /**
     * 注入导出按钮
     */
    private injectButton(answerElement: Element, answerIndex: number): void {
        try {
            const selectors = SelectorManager.getSelector();
            if (!selectors) {
                console.warn('[ObsidianExportInject] 选择器配置未找到');
                return;
            }

            // 查找 answerCompletion 组件
            const completionElement = DOMUtils.findElementInContainer(
                answerElement,
                selectors.answerCompletion
            );

            if (!completionElement) {
                console.warn('[ObsidianExportInject] 未找到 answerCompletion 组件');
                return;
            }

            // 检查是否已注入
            if (this.buttons.has(answerElement)) {
                console.info('[ObsidianExportInject] 按钮已存在，跳过注入');
                return;
            }

            // 创建按钮组件
            const button = new ObsidianExportButton(answerIndex);
            
            // 设置点击回调
            button.onClick((index) => {
                this.handleButtonClick(index);
            });

            // 查找复制按钮的父容器
            const copyButton = DOMUtils.findElementInContainer(
                completionElement,
                selectors.copyButton
            );

            if (!copyButton || !copyButton.parentElement) {
                console.warn('[ObsidianExportInject] 未找到复制按钮或其父容器');
                return;
            }

            // 将 Obsidian 按钮插入到复制按钮旁边
            const buttonElement = button.render();
            copyButton.parentElement.insertBefore(buttonElement, copyButton.nextSibling);

            // 存储按钮实例
            this.buttons.set(answerElement, button);

            console.info('[ObsidianExportInject] 导出按钮注入成功', { answerIndex });

        } catch (error) {
            console.error('[ObsidianExportInject] 注入按钮失败', error);
        }
    }

    /**
     * 处理按钮点击
     */
    private handleButtonClick(answerIndex: number): void {
        console.info('[ObsidianExportInject] 按钮被点击', { answerIndex });

        try {
            // 创建并显示 Modal
            const modal = new ObsidianExportModal(answerIndex);
            modal.show();
        } catch (error) {
            console.error('[ObsidianExportInject] 显示 Modal 失败', error);
        }
    }

    /**
     * 启动注入器
     */
    public start(): void {
        if (this.isActive) {
            console.warn('[ObsidianExportInject] 注入器已经启动');
            return;
        }

        this.isActive = true;
        console.info('[ObsidianExportInject] 注入器已启动');
    }

    /**
     * 停止注入器
     */
    public stop(): void {
        if (!this.isActive) {
            return;
        }

        this.isActive = false;

        // 清理所有按钮
        this.buttons.forEach((button) => {
            button.destroy();
        });
        this.buttons.clear();

        console.info('[ObsidianExportInject] 注入器已停止');
    }

    /**
     * 销毁注入器
     */
    public destroy(): void {
        this.stop();
        document.removeEventListener('ANSWER_EXTRACTED', this.handleAnswerExtracted);
        console.info('[ObsidianExportInject] 注入器已销毁');
    }
}
