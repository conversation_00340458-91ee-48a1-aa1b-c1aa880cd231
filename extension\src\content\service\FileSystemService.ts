/**
 * 文件系统访问服务
 * 使用 File System Access API 管理目录访问和文件写入
 * DirectoryHandle 存储在 IndexedDB（浏览器原生支持）
 * 目录信息（名称、路径）存储在 SettingsStorage（用于验证和显示）
 * 支持多平台：obsidian, notion, markdown
 */

import { contentSettingsService, type NotePlatform } from './ContentSettingsService'

// 扩展 Window 接口以支持 File System Access API
declare global {
    interface Window {
        showDirectoryPicker(options?: {
            mode?: 'read' | 'readwrite';
            startIn?: 'desktop' | 'documents' | 'downloads' | 'music' | 'pictures' | 'videos';
        }): Promise<FileSystemDirectoryHandle>;
    }

    interface FileSystemHandle {
        queryPermission(descriptor?: { mode?: 'read' | 'readwrite' }): Promise<PermissionState>;
        requestPermission(descriptor?: { mode?: 'read' | 'readwrite' }): Promise<PermissionState>;
    }
}

// IndexedDB 配置（用于存储多平台的 DirectoryHandle）
const DB_NAME = 'echosync_filesystem_db';
const DB_VERSION = 1;
const STORE_NAME = 'directory_handles';

// 生成平台专用的存储键
const getPlatformKey = (platform: NotePlatform): string => `${platform}_directory`;

export class FileSystemService {
    private static instance: FileSystemService;
    private db: IDBDatabase | null = null;
    // 内存缓存改为 Map，支持多平台
    private directoryHandles: Map<NotePlatform, FileSystemDirectoryHandle> = new Map();

    private constructor() {}

    static getInstance(): FileSystemService {
        if (!FileSystemService.instance) {
            FileSystemService.instance = new FileSystemService();
        }
        return FileSystemService.instance;
    }

    /**
     * 初始化 IndexedDB
     */
    private async initDB(): Promise<void> {
        if (this.db) return;

        return new Promise((resolve, reject) => {
            const request = indexedDB.open(DB_NAME, DB_VERSION);

            request.onerror = () => {
                console.error('[FileSystemService] IndexedDB 打开失败', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('[FileSystemService] IndexedDB 初始化成功');
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = (event.target as IDBOpenDBRequest).result;
                if (!db.objectStoreNames.contains(STORE_NAME)) {
                    db.createObjectStore(STORE_NAME);
                    console.log('[FileSystemService] 创建 ObjectStore:', STORE_NAME);
                }
            };
        });
    }

    /**
     * 保存目录句柄
     * 1. 保存到 IndexedDB（DirectoryHandle 对象）
     * 2. 保存到 SettingsStorage（目录名称和路径信息，用于验证）
     */
    private async saveDirectoryHandle(platform: NotePlatform, handle: FileSystemDirectoryHandle): Promise<void> {
        // 1. 保存到 IndexedDB（使用平台专用键）
        await this.initDB();
        if (!this.db) throw new Error('IndexedDB not initialized');

        const platformKey = getPlatformKey(platform);

        await new Promise<void>((resolve, reject) => {
            const transaction = this.db!.transaction([STORE_NAME], 'readwrite');
            const store = transaction.objectStore(STORE_NAME);
            const request = store.put(handle, platformKey);

            request.onsuccess = () => {
                console.log(`[FileSystemService] ${platform} DirectoryHandle 已保存到 IndexedDB`);
                resolve();
            };

            request.onerror = () => {
                console.error(`[FileSystemService] 保存 ${platform} DirectoryHandle 失败`, request.error);
                reject(request.error);
            };
        });

        // 2. 保存目录信息到 SettingsStorage
        try {
            await contentSettingsService.saveDirectoryInfo(
                platform,
                handle.name  // 目录名称
            );
            console.log(`[FileSystemService] ${platform} 目录信息已保存到 SettingsStorage:`, handle.name);
        } catch (error) {
            console.error(`[FileSystemService] 保存 ${platform} 目录信息失败`, error);
            // 不抛出错误，因为 IndexedDB 保存成功就可以正常工作
        }
    }

    /**
     * 从 IndexedDB 读取目录句柄，并与 SettingsStorage 中的信息进行验证
     */
    private async loadDirectoryHandle(platform: NotePlatform): Promise<FileSystemDirectoryHandle | null> {
        // 1. 从 IndexedDB 加载 DirectoryHandle（使用平台专用键）
        await this.initDB();
        if (!this.db) return null;

        const platformKey = getPlatformKey(platform);

        const handle = await new Promise<FileSystemDirectoryHandle | null>((resolve, reject) => {
            const transaction = this.db!.transaction([STORE_NAME], 'readonly');
            const store = transaction.objectStore(STORE_NAME);
            const request = store.get(platformKey);

            request.onsuccess = () => {
                const result = request.result as FileSystemDirectoryHandle | undefined;
                resolve(result || null);
            };

            request.onerror = () => {
                console.error(`[FileSystemService] 读取 ${platform} DirectoryHandle 失败`, request.error);
                reject(request.error);
            };
        });

        if (!handle) {
            console.log(`[FileSystemService] IndexedDB 中未找到 ${platform} DirectoryHandle`);
            return null;
        }

        // 2. 从 SettingsStorage 获取保存的路径（即目录名称）进行验证
        try {
            const savedPath = await contentSettingsService.getDirectoryName(platform);
            
            if (savedPath && savedPath !== handle.name) {
                console.warn(`[FileSystemService] ${platform} 目录名称不匹配`, {
                    savedPath: savedPath,
                    handleName: handle.name
                });
                // 路径不匹配，可能是用户选择了不同的目录，清除旧的 handle
                await this.clearDirectoryHandle(platform);
                return null;
            }

            console.log(`[FileSystemService] ${platform} DirectoryHandle 验证通过:`, handle.name);
            return handle;
        } catch (error) {
            console.error(`[FileSystemService] 验证 ${platform} 目录信息失败`, error);
            // 出错时仍然返回 handle，允许继续使用
            return handle;
        }
    }

    /**
     * 请求用户选择目录
     */
    async requestDirectoryAccess(platform: NotePlatform): Promise<FileSystemDirectoryHandle> {
        try {
            console.log(`[FileSystemService] 请求用户选择 ${platform} 目录`);
            
            // 显示目录选择器
            const handle = await window.showDirectoryPicker({
                mode: 'readwrite',
                startIn: 'documents'
            });

            console.log(`[FileSystemService] 用户已选择 ${platform} 目录:`, handle.name);

            // 保存到 IndexedDB
            await this.saveDirectoryHandle(platform, handle);

            // 缓存到内存
            this.directoryHandles.set(platform, handle);

            return handle;
        } catch (error) {
            if (error instanceof Error && error.name === 'AbortError') {
                console.log(`[FileSystemService] 用户取消了 ${platform} 目录选择`);
                throw new Error('用户取消了目录选择');
            }
            console.error(`[FileSystemService] 请求 ${platform} 目录访问失败`, error);
            throw error;
        }
    }

    /**
     * 获取目录句柄（如果没有则请求用户选择）
     */
    async getDirectoryHandle(platform: NotePlatform, forceRequest: boolean = false): Promise<FileSystemDirectoryHandle> {
        // 如果强制请求，直接显示选择器
        if (forceRequest) {
            return this.requestDirectoryAccess(platform);
        }

        // 1. 检查内存缓存
        const cachedHandle = this.directoryHandles.get(platform);
        if (cachedHandle) {
            try {
                // 验证权限是否仍然有效
                const permission = await cachedHandle.queryPermission({ mode: 'readwrite' });
                if (permission === 'granted') {
                    console.log(`[FileSystemService] 使用缓存的 ${platform} 目录句柄`);
                    return cachedHandle;
                }
            } catch (error) {
                console.warn(`[FileSystemService] 缓存的 ${platform} 句柄无效`, error);
                this.directoryHandles.delete(platform);
            }
        }

        // 2. 尝试从 IndexedDB 加载
        const savedHandle = await this.loadDirectoryHandle(platform);
        if (savedHandle) {
            try {
                // 验证权限
                const permission = await savedHandle.queryPermission({ mode: 'readwrite' });
                
                if (permission === 'granted') {
                    console.log(`[FileSystemService] 使用已保存的 ${platform} 目录句柄`);
                    this.directoryHandles.set(platform, savedHandle);
                    return savedHandle;
                }

                // 权限被撤销，尝试重新请求权限
                if (permission === 'prompt') {
                    const newPermission = await savedHandle.requestPermission({ mode: 'readwrite' });
                    if (newPermission === 'granted') {
                        console.log(`[FileSystemService] ${platform} 权限重新授予`);
                        this.directoryHandles.set(platform, savedHandle);
                        return savedHandle;
                    }
                }
            } catch (error) {
                console.warn(`[FileSystemService] 已保存的 ${platform} 句柄无效`, error);
            }
        }

        // 3. 没有有效句柄，请求用户选择
        console.log(`[FileSystemService] 需要用户选择新的 ${platform} 目录`);
        return this.requestDirectoryAccess(platform);
    }

    /**
     * 写入文件
     */
    async writeFile(platform: NotePlatform, filename: string, content: string): Promise<void> {
        try {
            // 获取目录句柄
            const dirHandle = await this.getDirectoryHandle(platform);

            console.log(`[FileSystemService] 写入 ${platform} 文件:`, filename);

            // 获取或创建文件
            const fileHandle = await dirHandle.getFileHandle(filename, { create: true });

            // 创建可写流
            const writable = await fileHandle.createWritable();

            // 写入内容
            await writable.write(content);

            // 关闭流
            await writable.close();

            console.log(`[FileSystemService] ${platform} 文件写入成功:`, filename);
        } catch (error) {
            console.error(`[FileSystemService] ${platform} 文件写入失败`, error);
            throw error;
        }
    }

    /**
     * 检查是否有目录访问权限
     * 优先检查 SettingsStorage，然后验证 IndexedDB 中的 DirectoryHandle
     */
    async hasDirectoryAccess(platform: NotePlatform): Promise<boolean> {
        try {
            // 1. 检查 SettingsStorage 中是否有配置（path 即为目录名称）
            const savedPath = await contentSettingsService.getDirectoryName(platform);
            if (!savedPath) {
                console.log(`[FileSystemService] ${platform} SettingsStorage 中无目录配置`);
                return false;
            }

            // 2. 尝试加载并验证 DirectoryHandle
            const savedHandle = await this.loadDirectoryHandle(platform);
            if (!savedHandle) {
                console.log(`[FileSystemService] ${platform} IndexedDB 中无 DirectoryHandle`);
                return false;
            }

            // 3. 检查权限状态
            const permission = await savedHandle.queryPermission({ mode: 'readwrite' });
            const hasPermission = permission === 'granted';
            
            console.log(`[FileSystemService] ${platform} 权限检查结果:`, {
                savedPath,
                handleName: savedHandle.name,
                permission,
                hasPermission
            });
            
            return hasPermission;
        } catch (error) {
            console.error(`[FileSystemService] ${platform} 检查权限失败`, error);
            return false;
        }
    }

    /**
     * 清除保存的目录句柄
     */
    async clearDirectoryHandle(platform: NotePlatform): Promise<void> {
        await this.initDB();
        if (!this.db) return;

        const platformKey = getPlatformKey(platform);

        return new Promise((resolve, reject) => {
            const transaction = this.db!.transaction([STORE_NAME], 'readwrite');
            const store = transaction.objectStore(STORE_NAME);
            const request = store.delete(platformKey);

            request.onsuccess = () => {
                console.log(`[FileSystemService] ${platform} 目录句柄已清除`);
                this.directoryHandles.delete(platform);
                resolve();
            };

            request.onerror = () => {
                console.error(`[FileSystemService] 清除 ${platform} 目录句柄失败`, request.error);
                reject(request.error);
            };
        });
    }
}

// 导出单例实例
export const fileSystemService = FileSystemService.getInstance();
