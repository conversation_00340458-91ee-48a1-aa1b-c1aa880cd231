/**
     * 日志记录 - 信息级别
     */
    export function logInfo(message: string, data?: any): void {
        const timestamp = new Date().toISOString();
        const dataStr = data ? ` | Data: ${JSON.stringify(data)}` : '';
        console.info(`[${timestamp}] [KimiAnswerController] ${message}${dataStr}`);
    }

    /**
     * 日志记录 - 警告级别
     */
    export function  logWarning(message: string, data?: any): void {
        const timestamp = new Date().toISOString();
        const dataStr = data ? ` | Data: ${JSON.stringify(data)}` : '';
        console.warn(`[${timestamp}] [KimiAnswerController] ⚠️ ${message}${dataStr}`);
    }

    /**
     * 日志记录 - 错误级别
     */
    export function logError(message: string, error?: any): void {
        const timestamp = new Date().toISOString();
        const errorStr = error ? ` | Error: ${error.toString()}` : '';
        console.error(`[${timestamp}] [KimiAnswerController] ❌ ${message}${errorStr}`);
        
        // 如果是 Error 对象，输出堆栈信息
        if (error && error.stack) {
            console.error(`[${timestamp}] [KimiAnswerController] Stack: ${error.stack}`);
        }
    }

    /**
     * 日志记录 - 调试级别
     */
    export function logDebug(message: string, data?: any): void {
        if (process.env.NODE_ENV === 'development') {
            const timestamp = new Date().toISOString();
            const dataStr = data ? ` | Data: ${JSON.stringify(data)}` : '';
            console.debug(`[${timestamp}] [KimiAnswerController] 🔍 ${message}${dataStr}`);
        }
    }