import { chatHistoryDao } from '../dao/ChatHistoryDao'
import { chatPromptDao } from '../dao/ChatPromptDao'
import { platformDao } from '../dao/PlatformDao'
import { ChatHistoryEntity, ChatPromptEntity, PlatformEntity } from '@/common/types/database_entity'
import {
  ChatPromptListResp,
  CreateChatHistoryReq,
} from '@/common/types/content_vo'
import {
  DatabaseResult,
} from '@/common/types/comm_vo'
import { Singleton } from '../base'
import { tr } from 'date-fns/locale'

/**
 * 聊天历史服务
 * 负责业务逻辑处理和Entity到DTO的转换
 */
export class ChatHistoryService extends Singleton<ChatHistoryService> {

  /**
   * 创建聊天历史记录
   */
  async createChatHistory(data: CreateChatHistoryReq): Promise<DatabaseResult<ChatHistoryEntity>> {
    try {
      // 检查平台是否存在
      const platform = await platformDao.findById(data.platform_id)
      if (!platform) {
        return {
          success: false,
          error: `Platform with ID ${data.platform_id} not found`
        }
      }

      // 根据chat_group_id+platform_id+chat_sort检查是否存在重复记录
      const existingRecord = await chatHistoryDao.findUniqueHistory(data.chat_group_id, data.platform_id, data.chat_sort)
      if(existingRecord){
        return {
          success: true,
          data: existingRecord
        }
      }

      // 创建聊天历史记录
      const chatHistory = await chatHistoryDao.create({
        prompt_uid: data.prompt_uid,
        platform_id: data.platform_id,
        chat_answer: data.chat_answer,
        chat_group_name: data.chat_group_name,
        chat_sort: data.chat_sort,
        chat_group_id: data.chat_group_id,
        create_time: data.create_time || Date.now(),
        is_synced: 0,
        is_answered: 1,
        is_delete: 0
      })

      console.log('【ChatHistoryService】Chat history created successfully:', chatHistory)
      return {
        success: true,
        data: chatHistory
      }
    } catch (error) {
      console.error('【ChatHistoryService】Create chat history error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据prompt_uid删除所有相关记录
   */
  async deleteByPromptUid(promptUid: string): Promise<DatabaseResult<number>> {
    try {
      const count = await chatHistoryDao.softDeleteByPromptUid(promptUid)
      return {
        success: true,
        data: count
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取统计信息
   */
  async getStats(): Promise<DatabaseResult<{
    total: number
    byPlatform: { [platformName: string]: number }
  }>> {
    try {
      const total = await chatHistoryDao.count()
      const platforms = await platformDao.findAllActive()
      const byPlatform: { [platformName: string]: number } = {}

      for (const platform of platforms) {
        const count = await chatHistoryDao.count({ platformId: platform.id })
        byPlatform[platform.name] = count
      }

      return {
        success: true,
        data: {
          total,
          byPlatform
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
  
}

// 导出单例实例
export const chatHistoryService = ChatHistoryService.getInstance()
