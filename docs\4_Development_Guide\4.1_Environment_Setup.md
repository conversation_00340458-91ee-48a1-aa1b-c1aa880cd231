# 4.1 开发环境设置

本指南将帮助你快速搭建 EchoSync 项目的本地开发环境。

## 1. 系统要求

在开始之前，请确保你的系统满足以下要求：

-   **Node.js**: `v18.0.0` 或更高版本。
-   **npm**: `v9.0.0` 或更高版本 (通常随 Node.js 一起安装)。
-   **Git**: 用于版本控制。
-   **浏览器**: 最新版本的 Google Chrome (用于插件调试)。

你可以通过以下命令检查你的 Node.js 和 npm 版本：
```bash
node -v
npm -v
```

## 2. 项目初始化

```bash
# 1. 克隆项目仓库
git clone https://github.com/your-username/EchoAIExtention.git

# 2. 进入项目根目录
cd EchoAIExtention

# 3. 安装所有依赖
# 这条命令会根据根目录的 package.json 安装所有依赖,
# 并自动链接 `extension` 和 `website` 两个工作区。
npm install
```

## 3. 启动开发服务器

项目提供了便捷的顶层命令来同时管理插件和网站的开发流程。

```bash
# 同时以开发模式启动 Chrome 插件和官方网站
# 插件代码会进行监听和热重载
# 网站会运行在 http://localhost:3000
npm run dev
```

你也可以选择单独启动某一个项目：

```bash
# 仅启动 Chrome 插件的开��服务器 (Vite)
npm run dev:extension

# 仅启动官方网站的开发服务器 (Next.js)
npm run dev:website
```

## 4. 在 Chrome 中加载开发版插件

要调试插件，你需要将其作为“未打包的扩展”加载到浏览器中。

1.  打开 Chrome 浏览器，在地址栏输入 `chrome://extensions` 并回车。
2.  在页面右上角，确保 **“开发者模式”** 的开关是打开的。
3.  点击左上角的 **“加载已解压的扩展程序”** 按钮。
4.  在弹出的文件选择框中，定位到你的项目目录，并选择 `extension/dist` 文件夹。
    -   **注意**: 是选择 `dist` 目录，而不是 `extension` 目录。`dist` 目录包含了 Vite 编译后的最终产物。

加载成功后，你就可以在 Chrome 的扩展程序列表中看到 "EchoSync"，并且可以在浏览器工具栏上看到它的图标。

## 5. 环境变量配置 (仅网站需要)

官方网站的开发需要连接到 Supabase 和 Stripe 等后端服务，因此需要配置环境变量。

```bash
# 1. 进入 website 目录
cd website

# 2. 复制环境变量模板文件
cp .env.example .env.local

# 3. 编辑 .env.local 文件
# 根据你自己的 Supabase 和 Stripe 项目信息，填入对应的 Key 和 Secret。
# 这些信息需要你到相应的官网注册并创建项目后才能获取。
```
```.env
# .env.local

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

完成以上步骤后，你的本地开发环境就完全准备好了。
