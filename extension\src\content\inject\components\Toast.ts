export interface ToastOptions {
  duration?: number
  position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
  showIcon?: boolean
  closable?: boolean
}

export type ToastType = 'success' | 'error' | 'info' | 'warning'

export class Toast {
  private static instance: Toast | null = null
  private container: HTMLElement | null = null
  private toasts: Map<string, HTMLElement> = new Map()
  private toastCounter = 0

  constructor() {
    this.createContainer()
    this.addStyles()
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): Toast {
    if (!Toast.instance) {
      Toast.instance = new Toast()
    }
    return Toast.instance
  }

  /**
   * 创建Toast容器
   */
  private createContainer(): void {
    this.container = document.createElement('div')
    this.container.id = 'echosync-toast-container'
    this.container.className = 'echosync-toast-container'
    
    Object.assign(this.container.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      zIndex: '10003',
      pointerEvents: 'none',
      display: 'flex',
      flexDirection: 'column',
      gap: '8px',
      maxWidth: '400px'
    })

    document.body.appendChild(this.container)
  }

  /**
   * 添加样式
   */
  private addStyles(): void {
    if (document.getElementById('echosync-toast-styles')) return

    const style = document.createElement('style')
    style.id = 'echosync-toast-styles'
    style.textContent = `
      .echosync-toast {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        border-radius: 8px;
        color: white;
        font-size: 14px;
        font-weight: 500;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(100%);
        opacity: 0;
        pointer-events: auto;
        cursor: pointer;
        min-width: 200px;
        max-width: 400px;
        word-wrap: break-word;
      }

      .echosync-toast.show {
        transform: translateX(0);
        opacity: 1;
      }

      .echosync-toast.hide {
        transform: translateX(100%);
        opacity: 0;
      }

      .echosync-toast-success {
        background-color: #10b981;
      }

      .echosync-toast-error {
        background-color: #ef4444;
      }

      .echosync-toast-info {
        background-color: #3b82f6;
      }

      .echosync-toast-warning {
        background-color: #f59e0b;
      }

      .echosync-toast-icon {
        flex-shrink: 0;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .echosync-toast-content {
        flex: 1;
        line-height: 1.4;
      }

      .echosync-toast-close {
        flex-shrink: 0;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.2s ease;
      }

      .echosync-toast-close:hover {
        opacity: 1;
      }

      @keyframes echosync-toast-slide-in {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }

      @keyframes echosync-toast-slide-out {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(100%);
          opacity: 0;
        }
      }
    `
    document.head.appendChild(style)
  }

  /**
   * 显示Toast
   */
  public show(
    message: string, 
    type: ToastType = 'success', 
    options: ToastOptions = {}
  ): string {
    const {
      duration = 3000,
      showIcon = true,
      closable = true
    } = options

    const toastId = `toast-${++this.toastCounter}`
    const toastElement = this.createToastElement(message, type, toastId, showIcon, closable)
    
    if (this.container) {
      this.container.appendChild(toastElement)
      this.toasts.set(toastId, toastElement)

      // 触发显示动画
      requestAnimationFrame(() => {
        toastElement.classList.add('show')
      })

      // 自动隐藏
      if (duration > 0) {
        setTimeout(() => {
          this.hide(toastId)
        }, duration)
      }
    }

    return toastId
  }

  /**
   * 创建Toast元素
   */
  private createToastElement(
    message: string, 
    type: ToastType, 
    toastId: string,
    showIcon: boolean,
    closable: boolean
  ): HTMLElement {
    const toast = document.createElement('div')
    toast.className = `echosync-toast echosync-toast-${type}`
    toast.dataset.toastId = toastId

    // 图标
    if (showIcon) {
      const icon = document.createElement('div')
      icon.className = 'echosync-toast-icon'
      icon.innerHTML = this.getIcon(type)
      toast.appendChild(icon)
    }

    // 内容
    const content = document.createElement('div')
    content.className = 'echosync-toast-content'
    content.textContent = message
    toast.appendChild(content)

    // 关闭按钮
    if (closable) {
      const closeBtn = document.createElement('div')
      closeBtn.className = 'echosync-toast-close'
      closeBtn.innerHTML = '×'
      closeBtn.addEventListener('click', () => {
        this.hide(toastId)
      })
      toast.appendChild(closeBtn)
    }

    // 点击整个Toast也可以关闭
    toast.addEventListener('click', () => {
      this.hide(toastId)
    })

    return toast
  }

  /**
   * 获取图标
   */
  private getIcon(type: ToastType): string {
    switch (type) {
      case 'success':
        return '✓'
      case 'error':
        return '✕'
      case 'warning':
        return '⚠'
      case 'info':
      default:
        return 'ℹ'
    }
  }

  /**
   * 隐藏Toast
   */
  public hide(toastId: string): void {
    const toast = this.toasts.get(toastId)
    if (!toast) return

    toast.classList.remove('show')
    toast.classList.add('hide')

    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast)
      }
      this.toasts.delete(toastId)
    }, 300)
  }

  /**
   * 清除所有Toast
   */
  public clear(): void {
    this.toasts.forEach((_, toastId) => {
      this.hide(toastId)
    })
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    this.clear()
    
    if (this.container) {
      this.container.remove()
      this.container = null
    }
    
    const styles = document.getElementById('echosync-toast-styles')
    if (styles) {
      styles.remove()
    }
    
    Toast.instance = null
  }
}

// 导出便捷方法
export const toast = {
  success: (message: string, options?: ToastOptions) => 
    Toast.getInstance().show(message, 'success', options),
  
  error: (message: string, options?: ToastOptions) => 
    Toast.getInstance().show(message, 'error', options),
  
  info: (message: string, options?: ToastOptions) => 
    Toast.getInstance().show(message, 'info', options),
  
  warning: (message: string, options?: ToastOptions) => 
    Toast.getInstance().show(message, 'warning', options)
}
