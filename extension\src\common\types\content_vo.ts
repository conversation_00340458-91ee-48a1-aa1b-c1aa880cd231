import { ChatHistoryEntity, ChatPromptEntity, PlatformEntity } from "./database_entity"

// 平台相关DTO
export interface CreatePlatformReq {
  name: string
  url: string
  icon?: string
  icon_base64?: string
}

export interface UpdatePlatformReq {
  name?: string
  url?: string
  icon?: string
  icon_base64?: string
  is_delete?: number
}

/**
 * 创建聊天记录的请求DTO
 */
export interface CreateChatPromptReq {
  chat_prompt: string
  prompt_uid?: string  // 可选的prompt_uid，如果提供则使用，否则自动生成
  platform_id: number
  tags?: string[]
}

/**
 * 创建聊天记录的响应DTO
 */
export interface CreateChatPromptResp {
  chatPrompt: ChatPromptEntity
}

/**
 * 创建聊天历史的请求DTO
 */
export interface CreateChatHistoryReq {
  prompt_uid: string
  platform_id: number
  chat_answer: string
  chat_group_name?: string
  chat_sort?: number
  chat_group_id?: string
  create_time?: number
}


/**
 * 平台信息VO 
 */
export interface PlatformInfoVO{
  platform_id: number, // 平台id，用于获取图标等信息
  platform_url: string, // 平台url，用于跳转到平台页面
  platform_name: string
  platform_icon?: string
  platform_icon_base64?: string
}


// 聊天提示词(带平台标志)
export interface ChatPromptListResp extends ChatPromptEntity {
  platform_info: PlatformInfoVO[] // 平台信息数组
}
