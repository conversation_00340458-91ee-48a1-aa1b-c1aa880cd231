# 6.2 用户使用指南

欢迎使用 EchoSync！本指南将详细介绍如何使用插件的各项功能，提升你的AI工作效率。

## 1. 核心功能：提示词同步

这是 EchoSync 最核心的功能。

-   **自动捕获**: 当你在任何一个受支持的AI聊天平台（如ChatGPT, Gemini等）的输入框中输入文字时，插件会自动捕获你的输入。
-   **实时同步**: 你输入的内容会实时、自动地填充到其他已打开的、受支持的AI平台的输入框中。你无需做任何额外操作。

## 2. Popup 界面

点击浏览器工具栏上的 EchoSync 图标，会打开一个功能面板 (Popup)。

### 2.1 主面板

-   **同步开关**: 在顶部有一个总开关，你可以随时暂停或重新启用所有同步功能。
-   **最近提示词**: 主界面会列出你最近使用过的提示词列表。
    -   **一键复用**: 点击列表中的任意一条，该提示词会立即填充到你当前所在的AI聊天页面的输入框中。
    -   **收藏**: 点击提示词旁边的星形图标，可以将其收藏，方便以后快速查找。
    -   **复制/删除**: 每条记录后面都有复制和删除按钮。

### 2.2 历史记录页面

在 Popup 界面点��“查看全部历史”或相应图标，可以进入完整的历史记录页面。

-   **无限滚动**: 加载你的所有历史记录。
-   **搜索**: 在顶部的搜索框中输入关键词，可以快速筛选你的提示词历史。
-   **按平台过滤**: 可以只查看来自特定AI平台（如只看ChatGPT）的记录。

## 3. Options (设置) 页面

右键点击插件图标，选择“选项”或通过 Popup 里的链接，可以进入详细的设置页面。

### 3.1 账户管理

-   **登录/注册**: 如果你想使用跨设备同步功能，需要在这里登录或注册一个 EchoSync 账户。
-   **订阅状态**: 查看你当前的订阅计划（免费版/专业版/VIP版）和到期时间。

### 3.2 同步设置

-   **平台选择**: 这里会列出所有支持的AI平台，每个平台后面都有一个开关。你可以根据自己的需要，精确控制哪些平台需要启用同步功能。例如，你只想在 ChatGPT 和 Claude 之间同步，可以关掉其他平台的开关。
-   **自动捕获开关**: 如果你暂时不希望插件自动记录你的输入，可以在这里关闭“自动捕获”功能。

### 3.3 数据管理

-   **导出数据**: 点击按钮，可以将你的所有本地历史记录和设置导出为一个 `.json` 文件，方便备份。
-   **导入数据**: 选择之前导出的 `.json` 文件，可以将数据恢复到插件中。
-   **清理数据**: 提供一键清除所有本地历史记录的选项。

## 4. 快捷键 (未来版本)

为了进一步提升效率，未来版本将支持自定义快捷键：

-   `Ctrl+Shift+E` (可自定义): 快速打开/关闭 Popup 面板。
-   `Ctrl+Shift+S` (可自定义): 手动触发一次当前提示词的同步。

通过以上功能，你可以极大地简化在多个AI平台间切换和操作的流程。祝你使用愉快！
