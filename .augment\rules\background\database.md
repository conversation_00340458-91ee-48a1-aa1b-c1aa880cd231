---
type: "agent_requested"
description: "数据库操作流程和规则"
---

# 数据库操作规则

## 分层架构
**Content → MessagingService → Background → Service → DAO → Database**

## 目录结构
```
common/
├── dao/         # 数据访问层
├── database/    # 数据库配置
├── service/     # 业务服务层
└── types/       # 类型定义
```

## 核心规则
### 禁止行为
- Content直接访问数据库
- UI组件直接调用DAO
- 跨层级直接调用

### 消息格式
- **ChromeMessage**: type, payload, timestamp
- **DatabaseResult**: success, data, error

## 层级职责
### Service层
- 业务逻辑处理
- 事务管理
- 返回DatabaseResult格式

### DAO层
- 纯CRUD操作
- 单表操作
- 无业务逻辑

### Entity/VO
- **Entity**: 数据库表结构
- **VO**: 前端传输对象

## 开发流程
1. 定义表结构(dexie.ts)
2. 创建Entity类型
3. 创建DAO类
4. 创建Service方法
5. 添加MessageHandler路由
