# 6.1 快速开始指南

本指南旨在帮助你用最快的速度启动并运行 EchoSync 项目。

## 面向开发者：一键启动

如果你是开发者，希望在本地运行项目进行贡献或二次开发，请遵循以下步骤。

### 前置要求

-   Node.js 18+
-   Git
-   最新版 Chrome 浏览器

### 启动流程

```bash
# 1. 克隆项目仓库
git clone https://github.com/your-username/echosync.git
cd echosync

# 2. 安装所有依赖
# 这条命令会同时安装插件和网站的依赖
npm install

# 3. 启动开发环境
# 这会同时启动插件的热重载构建和网站的本地服务器
npm run dev
```

**完成！** 现在：

-   **网站** 正在 `http://localhost:3000` 上运行。
-   **插件** 的构建产物位于 `extension/dist` 目录。请参考下一步在 Chrome 中加载它。

### 在 Chrome 中加载插件

1.  打开 Chrome，地址栏输入 `chrome://extensions/`。
2.  打开右上角的 **开发者模式**。
3.  点击 **加载已解压的扩展程序**。
4.  选择项目中的 `extension/dist` 目录。

---

## 面向用户：快速使用

如果你是普通用户，希望快速体验插件的核心功能。

### 1. 安装插件

-   **方式��� (推荐)**: 前往 [Chrome 应用商店]() (链接待定) 一键安装。
-   **方式二 (测试版)**: 下载开发者提供的 `.zip` 包，解压后，按照上一节 “在 Chrome 中加载插件” 的步骤加载解压后的文件夹。

### 2. 测试核心功能

1.  **打开两个AI网站**: 例如，同时打开 [ChatGPT](https://chat.openai.com) 和 [DeepSeek](https://chat.deepseek.com/)。
2.  **输入提示词**: 在 ChatGPT 的输入框中输入任意文字，例如 “你好”。
3.  **查看同步效果**: 切换到 DeepSeek 的页面，你会发现它的输入框中已经自动填充了 “你好”。
4.  **查看历史记录**: 点击浏览器工具栏上的 EchoSync 图标，在弹出的窗口中，你应该能看到刚才输入的 “你好” 这条历史记录。
5.  **快速复用**: 在弹出的窗口中，点击任意一条历史记录，当前页面的输入框就会被该记录填充。

## 常用命令

```bash
# 根目录脚本
npm run dev          # 同时启动插件和网站的开发模式
npm run build        # 构建所有项目的生产版本
npm run test         # 运行所有测试

# 插件专用脚本 (在 extension/ 目录运行)
npm run dev          # 启动插件开发模式
npm run build        # 构建插件生产版本

# 网站专用脚本 (在 website/ 目录运行)
npm run dev          # 启动网站开发模式
npm run build        # 构建网站生产版本
```
