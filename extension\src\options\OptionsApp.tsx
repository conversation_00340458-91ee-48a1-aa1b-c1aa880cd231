import React, { useState } from 'react'
import { GeneralSettings } from './pages/GeneralSettings'
import { NotePlatformSettings } from './pages/PlatformSettings'
import { DatabaseViewPage } from './pages/DatabaseViewPage'

const OptionsApp: React.FC = () => {
  const [activeTab, setActiveTab] = useState('general')

  const tabs = [
    { id: 'general', name: '常规设置', component: GeneralSettings },
    { id: 'platforms', name: '平台设置', component: NotePlatformSettings },
    { id: 'database', name: '查看数据库', component: DatabaseViewPage }
  ]

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || GeneralSettings

  // 数据库查看页面需要全屏显示
  if (activeTab === 'database') {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="px-4 py-6">
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">EchoSync 设置</h1>
                <p className="text-gray-600 mt-2">配置您的提示词同步和平台集成设置</p>
              </div>
              <nav className="flex gap-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      px-4 py-2 rounded-md text-sm font-medium transition-colors
                      ${activeTab === tab.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-100 bg-white'
                      }
                    `}
                  >
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>
          </div>
          <ActiveComponent />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">EchoSync 设置</h1>
          <p className="text-gray-600 mt-2">配置您的提示词同步和平台集成设置</p>
        </div>

        <div className="flex gap-8">
          {/* 侧边栏导航 */}
          <div className="w-64 flex-shrink-0">
            <nav className="bg-white rounded-lg shadow p-4">
              <ul className="space-y-2">
                {tabs.map((tab) => (
                  <li key={tab.id}>
                    <button
                      onClick={() => setActiveTab(tab.id)}
                      className={`
                        w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors
                        ${activeTab === tab.id
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-700 hover:bg-gray-100'
                        }
                      `}
                    >
                      {tab.name}
                    </button>
                  </li>
                ))}
              </ul>
            </nav>
          </div>

          {/* 主内容区域 */}
          <div className="flex-1">
            <ActiveComponent />
          </div>
        </div>
      </div>
    </div>
  )
}

export default OptionsApp
