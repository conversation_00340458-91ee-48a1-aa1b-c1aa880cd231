# 实施计划

## 任务列表

- [ ] 1. 创建独立的CSS样式文件
  - 创建 `HistoryBubble.css` 文件
  - 定义模态页相关的CSS类和样式
  - 实现高斯模糊背景效果
  - 添加响应式设计支持
  - _需求: 需求4_

- [ ] 2. 重构HistoryBubble组件结构
  - 修改组件的DOM结构为模态页形式
  - 创建背景遮罩元素
  - 实现居中的内容容器
  - 更新组件的基础属性和方法签名
  - _需求: 需求1_

- [ ] 3. 实现模态页显示逻辑
  - 重构 `show()` 方法为模态页显示
  - 添加背景模糊效果的应用和移除
  - 实现模态页的居中定位逻辑
  - 添加显示动画效果
  - _需求: 需求1_

- [ ] 4. 实现复制到剪贴板功能
  - 添加 `copyToClipboard()` 方法
  - 实现 Clipboard API 和降级方案
  - 修改点击事件处理逻辑
  - 集成复制功能到提示词点击事件
  - _需求: 需求2_

- [ ] 5. 创建Toast提示组件
  - 设计Toast组件的结构和样式
  - 实现Toast的显示和隐藏动画
  - 添加定位逻辑（在点击位置附近显示）
  - 实现自动隐藏功能（3秒后）
  - _需求: 需求2_

- [ ] 6. 实现关闭交互逻辑
  - 添加点击空白区域关闭功能
  - 实现ESC键关闭功能
  - 修改提示词点击后的关闭逻辑
  - 确保所有关闭方式都能正确清理资源
  - _需求: 需求3_

- [ ] 7. 更新事件监听器管理
  - 重构事件监听器的绑定和解绑逻辑
  - 添加模态页相关的事件监听器
  - 确保组件销毁时正确清理所有监听器
  - 优化事件处理性能
  - _需求: 需求1, 需求3_

- [ ] 8. 测试和优化
  - 测试模态页在不同屏幕尺寸下的显示效果
  - 验证复制功能在不同浏览器中的兼容性
  - 测试Toast提示的显示位置和时机
  - 优化动画性能和用户体验
  - _需求: 需求1, 需求2, 需求3_

## 实施顺序说明

1. **第1-2步**：建立基础结构，为后续功能实现做准备
2. **第3步**：核心的模态页显示功能，是整个需求的基础
3. **第4-5步**：复制功能和Toast提示，可以并行开发
4. **第6-7步**：完善交互逻辑和事件管理
5. **第8步**：最终测试和优化，确保质量

## 风险点和注意事项

- **样式冲突**：确保新的CSS类不与现有页面样式冲突
- **性能影响**：高斯模糊可能影响性能，需要测试和优化
- **兼容性**：Clipboard API 和 backdrop-filter 的浏览器兼容性
- **事件管理**：确保事件监听器的正确清理，避免内存泄漏
