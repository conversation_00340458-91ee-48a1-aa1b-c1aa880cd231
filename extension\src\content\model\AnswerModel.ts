import { PlatformEntity } from "@/common/types/database_entity";
import { Singleton } from "../../common/base/Singleton";
import ArchiveService from "../service/ArchiveService";
import { ConversationData, QuestionAnswerPair } from "../types/KimiTypes";
import { GenerateUtils } from "../utils/GenerateUtils";
import { platform } from "os";
import { CreateChatHistoryReq } from "@/common/types/content_vo";
import { PlatformConfig } from "../types/PlatformConfigType";

interface PromptItem{
    prompt: string;
    promptUid: string;
}

/**
 * 答案数据模型
 * 继承Singleton，作为单例类存储聊天数据
 * 为KimiAnswerController和KimiAnswerService提供数据访问接口
 */
export class AnswerModel extends Singleton<AnswerModel> {

    private currentTitle: string = '';
    private currentChatId: string = ''; // 当前聊天组的id
    private readonly MAX_CONVERSATIONS = 10; // 最大对话数量

    private promptList: PromptItem[] = [];
    private answerList: string[] = [];
    private platform: PlatformEntity = null;
    
    initPlatform(arg0: PlatformEntity) {
        this.platform = arg0;
    }
    /**
     * 更新对话标题
     */
    public setCurrentTitle(title: string): void {
        this.currentTitle = title;
    }
    
    /**
     * 获取当前标题
     */
    public getCurrentTitle(): string {
        return this.currentTitle;
    }

    public setChatId(chatId: string): void {
        this.currentChatId = chatId;
    }

    public async addPrompt(prompt: string): Promise<void> {
        const displayPrompt = prompt.length > 10 ? prompt.slice(0, 10) + '...' : prompt;
        console.log('[KimiAnswerModel] 添加问题 index：%d, content: %s', this.promptList.length, displayPrompt);
        
        const promptUid = await ArchiveService.getInstance().archivePrompt(prompt, this.platform?.id);
        this.promptList.push({
            prompt,
            promptUid
        });
    }

    /**
     * 
     * @param answer 答案markdown
     * @returns id
     */
    public async addAnswer(answer: string): Promise<number> {
        const displayAnswer = answer.length > 10 ? answer.slice(0, 10) + '...' : answer;
        console.log('[KimiAnswerModel] Answer index：%d, %s', this.answerList.length, displayAnswer);

        const answerData: CreateChatHistoryReq = {
            prompt_uid: this.promptList[this.promptList.length - 1].promptUid,
            platform_id: this.platform?.id,
            chat_answer: answer,
            chat_group_name: this.currentTitle,
            chat_group_id: this.currentChatId,
            chat_sort: this.answerList.length,
            create_time: Date.now()
        }
        await ArchiveService.getInstance().archiveAnswer(answerData);
        this.answerList.push(answer);
        return this.answerList.length - 1;
    }
    
    /**
     * 获取问答对（用于导出功能）
     * @param index 答案索引
     * @returns 问答对或null
     */
    public getQAPair(index: number): { prompt: string; answer: string } | null {
        if (index < 0 || index >= this.answerList.length) {
            console.warn('[KimiAnswerModel] getQAPair: 索引越界', index);
            return null;
        }
        
        return {
            prompt: this.promptList[index]?.prompt || '',
            answer: this.answerList[index] || ''
        };
    }

    /**
     * 获取答案数量
     * @returns 答案总数
     */
    public getAnswerCount(): number {
        return this.answerList.length;
    }

    /**
     * 清理所有对话数据
     */
    public clearAllConversations(): void {

        this.currentTitle = '';
        this.currentChatId = '';
        
        // 清理数组数据，而不是方法引用
        this.promptList.length = 0;  // 清空数组
        this.answerList.length = 0;  // 清空数组
        
        console.info('[KimiAnswerModel] 清理了所有对话数据');
    }

    /**
     * 清理业务资源
     */
    public destroy(): void {
        this.clearAllConversations();
        console.info('[KimiAnswerModel] destroyed');
        super.destroy();
    }

    /**
     * 完全重置单例实例（静态方法）
     * 注意：这会清理所有数据并重置单例实例
     */
    public static resetSingleton(): void {
        AnswerModel.resetInstance();
    }
}