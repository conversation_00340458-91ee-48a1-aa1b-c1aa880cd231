---
type: "development_rules"
description: "EchoSync 项目目录结构和文件组织规则"
---

# 项目结构规则

## 核心目录
```
extension/src/
├── background/    # 后台服务
├── content/       # 内容脚本
├── popup/         # 弹窗UI
├── options/       # 设置页
├── common/        # 共享逻辑
└── components/    # 可复用组件
```

## 模块结构

### Background
```
background/
├── index.ts              # 入口(≤20行)
├── messageHandler.ts     # 消息路由
├── databaseConnection.ts # 数据库连接
└── keepAlive.ts         # 保活机制
```

### Content
```
content/
├── adapters/    # 平台适配
├── capture/     # 页面捕捉
├── inject/      # UI注入
├── service/     # 业务服务
├── model/       # 数据模型
└── index.ts     # 入口
```

### Common
```
common/
├── dao/         # 数据访问
├── database/    # 数据库配置
├── service/     # 业务服务
└── types/       # 类型定义
```

## 核心规则

### 文件限制
- 单文件最大300行
- 入口文件最大20行
- 超过限制必须拆分

### 依赖规则
- Content → Background (仅消息)
- UI → Background (仅消息)
- Background → Common (直接)
- 禁止循环依赖

### 命名规则
- 适配器: `platformName.ts`
- 注入器: `ComponentInject.ts`
- 捕捉器: `ComponentCapture.ts`
- 服务: `ServiceName.ts`
- DAO: `EntityDao.ts`
