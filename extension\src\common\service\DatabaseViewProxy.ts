import { Singleton } from "../base/Singleton"
import { DatabaseResult, PaginatedResult } from "../types/comm_vo"
import { MessageType } from "../types/enums"
import { MessagingService } from "./MessagingService"

/**
 * 数据库查看代理服务
 */
export class DatabaseViewProxy extends Singleton<DatabaseViewProxy> {

  /**
   * 获取所有表信息
   */
  async getAllTables(): Promise<DatabaseResult<Array<{
    name: string
    displayName: string
    description: string
  }>>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_GET_ALL_TABLES, {})
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get all tables failed'
      }
    }
  }

  /**
   * 获取表数据
   */
  async getTableData(
    tableName: string,
    page: number = 1,
    limit: number = 20
  ): Promise<DatabaseResult<PaginatedResult<any>>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_GET_TABLE_DATA, {
        tableName,
        page,
        limit
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get table data failed'
      }
    }
  }

  /**
   * (待删除) 获取表记录总数
   */
  async getTableCount(tableName: string): Promise<DatabaseResult<number>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_GET_TABLE_COUNT, {
        tableName
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get table count failed'
      }
    }
  }

  /**
   * (待删除) 删除表记录
   */
  async deleteRecord(tableName: string, recordId: number): Promise<DatabaseResult<boolean>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_DELETE_RECORD, {
        tableName,
        recordId
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Delete record failed'
      }
    }
  }

  /**
   * (待删除) 清空表所有数据
   */
  async clearTable(tableName: string): Promise<DatabaseResult<number>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CLEAR_TABLE, {
        tableName
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Clear table failed'
      }
    }
  }
}


export const databaseViewProxy = DatabaseViewProxy.getInstance()