import { PlatformInfoVO } from '@/common/types/content_vo'


export interface PlatformIconOptions {
  size?: number
}

export class PlatformIcon {
  private options: Required<PlatformIconOptions>

  constructor(options: PlatformIconOptions = {}) {
    this.options = {
      size: options.size || 20
    }

    this.addStyles()
  }

  /**
   * 添加样式
   */
  private addStyles(): void {
    if (document.getElementById('echosync-platform-icon-styles')) return

    const style = document.createElement('style')
    style.id = 'echosync-platform-icon-styles'
    style.textContent = `
      .echosync-platform-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        overflow: hidden;
        background: rgba(0, 0, 0, 0.05);
      }

      .echosync-platform-icon img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }

      .echosync-platform-icon-fallback {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 600;
        color: #6b7280;
        background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      }
    `
    document.head.appendChild(style)
  }

  /**
   * 创建平台图标元素
   */
  public createIcon(platform: PlatformInfoVO, options: Partial<PlatformIconOptions> = {}): HTMLElement {
    const finalOptions = { ...this.options, ...options }

    const container = document.createElement('div')
    container.className = 'echosync-platform-icon'
    container.style.width = `${finalOptions.size}px`
    container.style.height = `${finalOptions.size}px`

    // 显示图标
    this.showIcon(platform, container)

    return container
  }

  /**
   * 显示图标
   */
  private showIcon(platform: PlatformInfoVO, container: HTMLElement): void {
    // 优先使用 icon_base64
    if (platform.platform_icon_base64 && platform.platform_icon_base64.startsWith('data:image/')) {
      const img = document.createElement('img')
      img.src = platform.platform_icon_base64
      img.alt = platform.platform_name
      img.onerror = () => this.showFallback(container, platform.platform_name)
      container.appendChild(img)
    } else {
      // 使用 name 显示回退图标
      this.showFallback(container, platform.platform_name)
    }
  }

  /**
   * 显示回退图标
   */
  private showFallback(container: HTMLElement, platformName: string): void {
    const fallback = document.createElement('div')
    fallback.className = 'echosync-platform-icon-fallback'
    fallback.textContent = platformName.charAt(0).toUpperCase()
    container.appendChild(fallback)
  }
}
