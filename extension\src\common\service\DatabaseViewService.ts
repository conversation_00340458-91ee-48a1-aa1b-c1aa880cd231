import { chatHistoryDao } from '../dao/ChatHistoryDao'
import { chatPromptDao } from '../dao/ChatPromptDao'
import { platformDao } from '../dao/PlatformDao'
import { DatabaseResult, PaginatedResult } from '@/common/types/comm_vo'

/**
 * 数据库查看服务
 * 提供数据库表的通用查询功能，用于Options页面的数据库查看功能
 */
export class DatabaseViewService {
  private static instance: DatabaseViewService

  public static getInstance(): DatabaseViewService {
    if (!DatabaseViewService.instance) {
      DatabaseViewService.instance = new DatabaseViewService()
    }
    return DatabaseViewService.instance
  }

  /**
   * 获取所有表的信息
   */
  async getAllTables(): Promise<DatabaseResult<Array<{
    name: string
    displayName: string
    description: string
  }>>> {
    try {
      const tables = [
        {
          name: 'chatHistory',
          displayName: '聊天历史',
          description: '存储用户与AI平台的聊天历史记录'
        },
        {
          name: 'chatPrompt',
          displayName: '聊天提示词',
          description: '存储用户输入的提示词内容'
        },
        {
          name: 'platform',
          displayName: '平台信息',
          description: '存储支持的AI平台基本信息'
        }
      ]

      return {
        success: true,
        data: tables
      }
    } catch (error) {
      console.error('【EchoSync】Get all tables error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get tables failed'
      }
    }
  }

  /**
   * 获取指定表的数据
   */
  async getTableData(
    tableName: string,
    page: number = 1,
    limit: number = 20
  ): Promise<DatabaseResult<PaginatedResult<any>>> {
    try {
      let data: any[] = []
      let total: number = 0

      const offset = (page - 1) * limit

      switch (tableName) {
        case 'chatHistory':
          data = await chatHistoryDao.findAll({
            limit,
            offset,
            orderBy: 'id',
            orderDirection: 'DESC'
          })
          total = await chatHistoryDao.count()
          break

        case 'chatPrompt':
          data = await chatPromptDao.findAll({
            limit,
            offset,
            orderBy: 'id',
            orderDirection: 'DESC'
          })
          total = await chatPromptDao.count()
          break

        case 'platform':
          data = await platformDao.findAll({
            limit,
            offset
          })
          total = await platformDao.count()
          break

        default:
          return {
            success: false,
            error: `Unknown table: ${tableName}`
          }
      }

      const totalPages = Math.ceil(total / limit)

      return {
        success: true,
        data: {
          data,
          total,
          page,
          limit,
          totalPages
        }
      }
    } catch (error) {
      console.error(`【EchoSync】Get table data error for ${tableName}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get table data failed'
      }
    }
  }

  /**
   * 获取指定表的记录总数
   */
  async getTableCount(tableName: string): Promise<DatabaseResult<number>> {
    try {
      let count: number = 0

      switch (tableName) {
        case 'chatHistory':
          count = await chatHistoryDao.count()
          break
        case 'chatPrompt':
          count = await chatPromptDao.count()
          break
        case 'platform':
          count = await platformDao.count()
          break
        default:
          return {
            success: false,
            error: `Unknown table: ${tableName}`
          }
      }

      return {
        success: true,
        data: count
      }
    } catch (error) {
      console.error(`【EchoSync】Get table count error for ${tableName}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get table count failed'
      }
    }
  }

  /**
   * 删除指定表的记录
   */
  async deleteRecord(
    tableName: string,
    recordId: number
  ): Promise<DatabaseResult<boolean>> {
    try {
      let success = false

      switch (tableName) {
        case 'chatHistory':
          await chatHistoryDao.delete(recordId)
          success = true
          break

        case 'chatPrompt':
          await chatPromptDao.delete(recordId)
          success = true
          break

        case 'platform':
          await platformDao.delete(recordId)
          success = true
          break

        default:
          return {
            success: false,
            error: `Unknown table: ${tableName}`
          }
      }

      return {
        success: true,
        data: success
      }
    } catch (error) {
      console.error(`【EchoSync】Delete record error for ${tableName}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Delete record failed'
      }
    }
  }

  /**
   * 清空指定表的所有数据
   */
  async clearTable(tableName: string): Promise<DatabaseResult<number>> {
    try {
      let deletedCount = 0

      switch (tableName) {
        case 'chatHistory':
          deletedCount = await chatHistoryDao.clear()
          break

        case 'chatPrompt':
          deletedCount = await chatPromptDao.clear()
          break

        case 'platform':
          deletedCount = await platformDao.clear()
          break

        default:
          return {
            success: false,
            error: `Unknown table: ${tableName}`
          }
      }

      return {
        success: true,
        data: deletedCount
      }
    } catch (error) {
      console.error(`【EchoSync】Clear table error for ${tableName}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Clear table failed'
      }
    }
  }
}

// 导出单例实例
export const databaseViewService = DatabaseViewService.getInstance()
