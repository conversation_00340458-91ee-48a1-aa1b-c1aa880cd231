import React from 'react'
import { PlatformTabs, StatusIndicator } from '../components/button'
import { useSettings } from '../hooks/useSettings'

export function SettingsPage() {
  const {
    settings,
    loading,
    error,
    browseFolder
  } = useSettings()

  const platforms = [
    { id: 'obsidian', label: 'Obsidian', icon: '🔵' },
    { id: 'notion', label: 'Notion', icon: '🟣' },
    { id: 'markdown', label: 'Markdown', icon: '📄' }
  ]
  
  // 当前活跃平台（固定为obsidian）
  const [activePlatform, setActivePlatform] = React.useState<'obsidian' | 'notion' | 'markdown'>('obsidian')

  // 处理加载状态
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
        <div>加载设置中...</div>
      </div>
    )
  }

  // 处理错误状态
  if (error) {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-lg)' }}>
        <div className="card fade-in">
          <div className="card-content">
            <div style={{ color: 'var(--error)', textAlign: 'center' }}>
              加载设置失败: {error}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 如果没有设置数据
  if (!settings) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
        <div>设置数据不可用</div> 
      </div>
    )
  }

  const getStatusIndicator = (deviceId: string, hasPath: boolean) => {
    // 判断是否是当前设备
    const isCurrent = deviceId === settings.currentDevice.currentDeviceId
    
    if (isCurrent) {
      return <StatusIndicator type="success">当前设备</StatusIndicator>
    } else if (hasPath) {
      return <StatusIndicator type="success">已配置</StatusIndicator>
    } else {
      return <StatusIndicator type="warning">未配置</StatusIndicator>
    }
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-lg)' }}>
      {/* 设备信息 */}
      <div className="card fade-in">
        <div className="card-title">🖥️ 当前设备信息</div>
        <div className="card-content">
          <div style={{ fontWeight: 600 }}>{settings.currentDevice.fullName}</div>
        </div>
      </div>

      {/* 平台导出配置 */}
      <div className="card fade-in">
        <div className="card-title">📂 平台导出配置</div>
        <div className="card-content">
          {/* Platform Tabs */}
          <PlatformTabs
            tabs={platforms}
            activeTab={activePlatform}
            onTabChange={(platform) => {
              setActivePlatform(platform as 'obsidian' | 'notion' | 'markdown')
            }}
          />

          {/* Platform Content */}
          <div className="platform-content active">
            {Object.values(settings.platforms[activePlatform].devices).map((device) => {
              // 判断设备状态
              const isCurrent = device.id === settings.currentDevice.currentDeviceId
              
              return (
                <div
                  key={device.id}
                  className={`device-item ${isCurrent ? 'current-device' : ''}`}
                >
                  <div className="device-header">
                    <span>{device.icon}</span>
                    <span>{device.name}</span>
                    {getStatusIndicator(device.id, !!device.path)}
                  </div>
                  <div className="path-label">导出路径:</div>
                  <div className="device-path">
                    <input
                      type="text"
                      value={device.path}
                      placeholder={device.path ? '选择导出路径...' : '未配置导出路径'}
                      readOnly
                    />
                    <button
                      className="btn btn-secondary btn-small"
                      onClick={() => browseFolder(activePlatform, device.id)}
                    >
                      {device.path ? '浏览...' : '设置'}
                    </button>
                    {!isCurrent && device.path && (
                      <button style={{ background: 'none', border: 'none', cursor: 'pointer', padding: '4px' }}>
                        ⚙️
                      </button>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}
