import { Singleton } from "../base/Singleton"
import { DatabaseResult } from "../types/comm_vo"
import { PlatformEntity } from "../types/database_entity"
import { MessageType } from "../types/enums"
import { MessagingService } from "./MessagingService"

/**
 * 平台数据库代理服务
 */
export class PlatformDatabaseProxy extends Singleton<PlatformDatabaseProxy> {
  
  /**
   * 获取所有平台
   */
  async getAll(): Promise<DatabaseResult<PlatformEntity[]>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_PLATFORM_GET_LIST, {})
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get platform list failed'
      }
    }
  }

  /**
   * 更新平台favicon
   */
  async updatePlatformFavicon(platformId: number, faviconBase64: string, faviconUrl?: string): Promise<DatabaseResult<boolean>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.UPDATE_PLATFORM_FAVICON, {
        platformId,
        faviconBase64,
        faviconUrl
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Update platform favicon failed'
      }
    }
  }
}

// 导出单例实例
export const platformDatabaseProxy = PlatformDatabaseProxy.getInstance()