/**
 * 对话数据结构
 */
export interface ConversationData {
    title: string;
    qaList: QuestionAnswerPair[];
    createdAt: number;
    updatedAt: number;
}

/**
 * 问答对数据结构
 */
export interface QuestionAnswerPair {
    question: string;
    answer: string;
    timestamp: number;
    questionId: string;  // 用于问答匹配
    answerId: string;    // 用于问答匹配
}

/**
 * 页面状态结构
 */
export interface PageState {
    isWelcomePage: boolean;
    isChatPage: boolean;
    hasTransitioned: boolean;
    chatId?: string;  // 聊天页面的聊天ID
}