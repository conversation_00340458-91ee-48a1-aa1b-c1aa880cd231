# Content Script 查询数据库数据的标准流程

本文档详细描述了 Content Script 如何通过消息机制查询数据库数据的完整流程。

## 一、整体查询流程

```mermaid
sequenceDiagram
    participant CS as Content Script
    participant MS as MessagingService
    participant M<PERSON> as MessageHandler
    participant CHS as ChatHistoryService
    participant CHD as ChatHistoryDao
    participant DB as Dexie Database

    CS->>MS: MessagingService.sendToBackground
    MS->>MH: MessageHandler.handleMessage
    MH->>MH: databaseConnectionManager.ensureConnection
    MH->>CHS: chatHistoryService.getChatHistoryWithPlatform
    CHS->>CHD: chatHistoryDao.findByPlatform
    CHD->>DB: 执行 Dexie 查询
    DB-->>CHD: 返回查询结果
    CHD-->>CHS: 返回格式化数据
    CHS-->>MH: 返回 DatabaseResult
    MH-->>MS: sendResponse 回调
    MS-->>CS: 返回查询结果
```

## 二、详细查询流程图

```mermaid
flowchart TD
    A[Content Script 需要数据] --> B[构造查询消息]
    B --> C[调用 MessagingService.sendMessage]
    
    C --> D[消息发送到 Background]
    D --> E[MessageHandler 接收消息]
    E --> F{消息类型判断}
    
    F -->|DB_CHAT_PROMPT_LIST_GET| H[chatPromptService.getChatPromptList]
    F -->|DB_PLATFORM_GET_LIST| I[platformService.findAllActive]


    G --> K[ChatHistoryDao 查询方法]
    H --> L[ChatHistoryDao 查询方法]
    I --> M[PlatformDao 查询方法]
    J --> N[ChatHistoryDao 搜索方法]

    K --> O[Dexie 数据库查询]
    L --> O
    M --> O
    N --> O
    
    O --> S[Dexie 数据库查询]
    P --> S
    Q --> S
    R --> S
    
    S --> T{查询成功?}
    T -->|是| U[返回查询结果]
    T -->|否| V[返回错误信息]
    
    U --> W[Service 层处理结果]
    V --> X[Service 层处理错误]
    
    W --> Y[构造成功响应]
    X --> Z[构造错误响应]
    
    Y --> AA[MessageHandler 发送响应]
    Z --> AA
    
    AA --> BB[Content Script 接收响应]
    BB --> CC{响应成功?}
    
    CC -->|是| DD[处理查询数据]
    CC -->|否| EE[处理错误信息]
    
    DD --> FF[更新 UI 或执行业务逻辑]
    EE --> GG[显示错误提示或降级处理]
```

## 三、具体查询示例

### 3.1 查询聊天历史记录

```mermaid
sequenceDiagram
    participant UI as UI Component
    participant CS as Content Script
    participant B as Background
    participant CHS as ChatHistoryService
    participant CHD as ChatHistoryDao
    participant DB as Dexie Database

    UI->>CS: 用户点击历史记录按钮
    CS->>CS: 构造查询消息
    
    Note over CS: 消息格式
    Note over CS: {type: "GET_CHAT_HISTORY", payload: {platformId: "chatgpt", limit: 20}}
    
    CS->>B: sendMessage(queryMessage)
    B->>CHS: handleGetChatHistory(payload)
    CHS->>CHD: findByPlatform(platformId, limit)
    CHD->>DB: chatHistory.where('platformId').equals(platformId).limit(limit)
    DB-->>CHD: 返回历史记录数组
    CHD-->>CHS: 返回格式化数据
    CHS-->>B: {success: true, data: historyList}
    B-->>CS: sendResponse(result)
    CS->>UI: 更新历史记录列表
```

### 3.2 查询提示词模板

```mermaid
sequenceDiagram
    participant UI as Prompt Selector
    participant CS as Content Script
    participant B as Background
    participant CPS as ChatPromptService
    participant CPD as ChatPromptDao
    participant DB as Dexie Database

    UI->>CS: 用户打开提示词选择器
    CS->>CS: 构造查询消息
    
    Note over CS: 消息格式
    Note over CS: {type: "GET_CHAT_PROMPTS", payload: {category: "coding", active: true}}
    
    CS->>B: sendMessage(queryMessage)
    B->>CPS: handleGetChatPrompts(payload)
    CPS->>CPD: findByCategory(category, active)
    CPD->>DB: chatPrompts.where('category').equals(category).and(p => p.active === true)
    DB-->>CPD: 返回提示词数组
    CPD-->>CPS: 返回格式化数据
    CPS-->>B: {success: true, data: promptList}
    B-->>CS: sendResponse(result)
    CS->>UI: 显示提示词选项
```

### 3.3 查询平台配置

```mermaid
sequenceDiagram
    participant CS as Content Script
    participant B as Background
    participant PS as PlatformService
    participant PD as PlatformDao
    participant DB as Dexie Database

    CS->>CS: 初始化时需要平台配置
    CS->>CS: 构造查询消息
    
    Note over CS: 消息格式
    Note over CS: {type: "GET_PLATFORMS", payload: {}}
    
    CS->>B: sendMessage(queryMessage)
    B->>PS: handleGetPlatforms(payload)
    PS->>PD: findAll()
    PD->>DB: platforms.toArray()
    DB-->>PD: 返回所有平台配置
    PD-->>PS: 返回平台数据
    PS-->>B: {success: true, data: platformList}
    B-->>CS: sendResponse(result)
    CS->>CS: 设置当前平台信息
```

## 四、错误处理流程

```mermaid
flowchart TD
    A[查询过程中发生错误] --> B{错误类型判断}
    
    B -->|网络错误| C[消息发送失败]
    B -->|数据库错误| D[数据库查询失败]
    B -->|业务逻辑错误| E[Service 层错误]
    B -->|参数错误| F[请求参数无效]
    
    C --> C1[记录网络错误]
    C1 --> C2[重试机制]
    C2 --> C3{重试成功?}
    C3 -->|是| G[返回查询结果]
    C3 -->|否| H[返回网络错误]
    
    D --> D1[记录数据库错误]
    D1 --> D2[检查数据库连接]
    D2 --> D3{连接正常?}
    D3 -->|是| I[返回查询错误]
    D3 -->|否| J[尝试重新连接]
    
    E --> E1[记录业务错误]
    E1 --> E2[返回业务错误信息]
    
    F --> F1[记录参数错误]
    F1 --> F2[返回参数验证错误]
    
    H --> K[Content Script 错误处理]
    I --> K
    E2 --> K
    F2 --> K
    J --> L[数据库重连流程]
    
    K --> M[显示用户友好错误]
    K --> N[降级处理或缓存数据]
    
    L --> O{重连成功?}
    O -->|是| P[重新执行查询]
    O -->|否| Q[离线模式]
    
    P --> G
    Q --> N
```

## 五、性能优化策略

### 5.1 查询优化

```mermaid
flowchart TD
    A[查询请求] --> B{是否有缓存?}
    B -->|是| C[检查缓存有效性]
    B -->|否| D[执行数据库查询]
    
    C --> E{缓存有效?}
    E -->|是| F[返回缓存数据]
    E -->|否| D
    
    D --> G[数据库查询]
    G --> H[更新缓存]
    H --> I[返回查询结果]
    
    F --> J[查询完成]
    I --> J
```

### 5.2 批量查询优化

```mermaid
sequenceDiagram
    participant CS as Content Script
    participant B as Background
    participant S as Service Layer
    participant DB as Database

    Note over CS: 批量查询请求
    CS->>B: {type: "BATCH_QUERY", payload: {queries: [...]}}
    B->>S: 处理批量查询
    S->>S: 合并相同类型查询
    S->>DB: 执行批量数据库操作
    DB-->>S: 返回批量结果
    S->>S: 分组返回结果
    S-->>B: {success: true, results: {...}}
    B-->>CS: 返回批量查询结果
```

## 六、最佳实践

### 6.1 实际消息格式
基于代码中的 ChromeMessage 接口：
```typescript
interface ChromeMessage {
  type: MessageType;     // 消息类型枚举
  payload?: any;         // 查询参数
  timestamp: number;     // 时间戳
}

// 响应格式（DatabaseResult）
interface DatabaseResult<T> {
  success: boolean;      // 是否成功
  data?: T;             // 查询结果
  error?: string;       // 错误信息
}
```

### 6.2 实际实现特点
- 消息处理: 基于 MessageType 枚举的 switch-case 路由
- 数据库连接: 每次消息处理前确保连接就绪
- 错误处理: 简单的 try-catch 和 console.error 记录
- 响应格式: 统一的 DatabaseResult 格式

### 6.3 性能特点
1. **同步处理**: 大部分查询操作是同步的
2. **简单路由**: 基于消息类型的直接路由
3. **最小开销**: 避免复杂的缓存和重试机制
4. **直接响应**: 查询结果直接通过 sendResponse 返回
