## Origin Private File System + SQLite WASM**
Chrome 109+ 提供了 **WebAssembly 版 SQLite**，跑在 **Origin Private File System (OPFS)**，**无需服务器、无需额外权限**，容量只受磁盘限制。

#### 1. 引入 SQLite WASM

bash

复制

```bash
# 官方仓库
git clone https://github.com/sqlite/sqlite-wasm
```

#### 2. 在扩展中加载

TypeScript

复制

```ts
// service worker 或 content script
import initSqlJs from './sqlite-wasm/sqlite3.js';

const sqlite3 = await initSqlJs({
  locateFile: (file) => `./sqlite-wasm/${file}`,
});

const db = new sqlite3.oo1.DB('/mydb.sqlite3', 'c'); // 'c' 创建或打开
db.exec(`CREATE TABLE IF NOT EXISTS notes(id INTEGER PRIMARY KEY, text TEXT)`);
db.exec({sql: `INSERT INTO notes(text) VALUES(?)`, bind: ['Hello WASM SQLite']});
const rows = db.exec({sql: `SELECT * FROM notes`, rowMode: 'object'});
console.log(rows); // [{id:1, text:'Hello WASM SQLite'}]
```

## 如果我想对sqllit3中的某个长文本字段，进行模糊搜索，如何设计能提高查询速度。比如表a的content字段有10000字，包含“Chrome”字段，我想要搜索“Chrome”字段即命中这一行。
SQLite 3.35+（Chrome 109+ 自带的 SQLite-WASM 已包含）<mark style="background: #BBFABBA6;">内置 **FTS5** 虚拟表</mark>，专为全文检索优化。

#### 1. 建 FTS5 虚拟表
```sql
-- 假设原表 a(id INTEGER PRIMARY KEY, content TEXT)
CREATE VIRTUAL TABLE a_fts USING fts5(content, content='a', content_rowid='id');
```
- `content='a'` 告诉 FTS5 数据来自表 a 的 content 列。  
- `content_rowid='id'` 让 FTS5 用原表主键做回表。

#### 2. 触发器保持同步
```sql
-- 插入
CREATE TRIGGER a_fts_insert AFTER INSERT ON a BEGIN
  INSERT INTO a_fts(rowid, content) VALUES (new.id, new.content);
END;

-- 更新
CREATE TRIGGER a_fts_update AFTER UPDATE ON a BEGIN
  UPDATE a_fts SET content = new.content WHERE rowid = new.id;
END;

-- 删除
CREATE TRIGGER a_fts_delete AFTER DELETE ON a BEGIN
  DELETE FROM a_fts WHERE rowid = old.id;
END;
```

#### 3. 查询（毫秒级）
```sql
SELECT a.*
FROM a_fts
JOIN a ON a.id = a_fts.rowid
WHERE a_fts MATCH 'Chrome';
```
- 支持前缀、布尔、邻近、短语等高阶语法。  
- 内部用 **倒排索引 + B-Tree**，无需额外分词器即可中文/英文混合搜索。