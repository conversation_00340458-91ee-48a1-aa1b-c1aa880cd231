import { BaseAIAdapter } from './core/BaseAIAdapter'

import { ChatGPTConfig } from '../types/Consts'
import { PlatformEntity } from '@/common/types/database_entity'


export class ChatGPTAdapter extends BaseAIAdapter {
  constructor(platform: PlatformEntity) {
    super(platform)
  }

  // /**
  //  * 获取 ChatGPT 平台特定的选择器配置
  //  */
  // getSelectors(): SelectorConfig {
  //   return {
  //     inputField: [
  //       '#prompt-textarea',
  //       'textarea[placeholder*="Message"]'
  //     ],
  //     sendButton: [
  //       'button[data-testid="send-button"]',
  //       'button[aria-label="Send prompt"]'
  //     ]
  //   }
  // }


}
