// GenerateUtils 改进建议

export class GenerateUtils {

  /** 
   * 生成提示词唯一ID（基于SHA-1哈希）
   * @param prompt 提示词内容
   * @returns Promise<string> 40字符的十六进制哈希字符串
   */
  public static async generatePromptUid(prompt: string): Promise<string> {
    // 参数验证
    if (typeof prompt !== 'string') {
      throw new Error('Prompt must be a string');
    }

    try {
      // 使用WebCrypto API的SHA-1算法生成唯一ID
      const encoder = new TextEncoder();
      const data = encoder.encode(prompt);    
      const hash = await crypto.subtle.digest('SHA-1', data);
      
      // 将hash buffer转换为16进制字符串
      const hashArray = Array.from(new Uint8Array(hash));    
      const uid = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      
      return uid;
    } catch (error) {
      throw new Error(`Failed to generate prompt UID: ${error.message}`);
    }
  }

  /** 
   * 生成提示词唯一ID（实例方法，保持向后兼容）
   * @param prompt 提示词内容
   * @returns Promise<string> 40字符的十六进制哈希字符串
   * @deprecated 使用静态方法 GenerateUtils.generatePromptUid() 代替
   */
  public static async promptUid(prompt: string): Promise<string> {
    return GenerateUtils.generatePromptUid(prompt);
  }

  /** 
   * 生成基于时间戳的唯一ID（用于需要时间特性的场景）
   * @param prefix 可选的前缀
   * @returns string 时间戳+随机字符串格式的ID
   */
  public static generateTimestampUid(prefix: string = ''): string {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    return prefix ? `${prefix}-${timestamp}-${randomStr}` : `${timestamp}-${randomStr}`;
  }

  /** 
   * 生成随机UUID（用于需要完全随机性的场景）
   * @returns string UUID格式的字符串
   */
  public static generateUUID(): string {
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID();
    }
    
    // Fallback for environments without crypto.randomUUID
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * 验证prompt UID格式是否正确
   * @param uid 需要验证的UID
   * @returns boolean 是否为有效的prompt UID格式
   */
  public static isValidPromptUid(uid: string): boolean {
    if (typeof uid !== 'string') {
      return false;
    }
    // SHA-1 哈希应该是40个十六进制字符
    return /^[a-f0-9]{40}$/.test(uid);
  }
}