---
type: "development_rules"
description: "命名规范和代码风格规则"
---

# 命名规范

## 文件命名
- **类**: `ClassName.ts`
- **组件**: `ComponentName.tsx`
- **工具**: `utilityName.ts`
- **类型**: `types.ts`

## 特殊命名
- **适配器**: `platformName.ts`
- **注入器**: `ComponentInject.ts`
- **捕捉器**: `ComponentCapture.ts`
- **服务**: `ServiceName.ts`
- **DAO**: `EntityDao.ts`

## 变量命名
- **变量**: camelCase (`userName`)
- **常量**: UPPER_SNAKE_CASE (`MAX_COUNT`)
- **私有**: 下划线前缀 (`_state`)
- **布尔**: is/has/can前缀 (`isVisible`)

## 函数命名
- **普通**: camelCase动词 (`getUserData`)
- **事件**: handle前缀 (`handleClick`)
- **获取**: get前缀 (`getConfig`)
- **设置**: set前缀 (`setVisible`)
- **检查**: is/has/can前缀 (`isReady`)

## 类型命名
- **类**: PascalCase (`MessageHandler`)
- **接口**: Type后缀 (`ConfigType`)
- **抽象类**: Base前缀 (`BaseCapture`)
- **枚举**: PascalCase (`MessageType`)

## 导入规则
1. Node.js内置模块
2. 第三方库
3. 项目内部模块
4. 相对路径

## 代码风格
- 2空格缩进
- 单引号字符串
- 语句末尾分号
- 使用const/let
- Prettier格式化
