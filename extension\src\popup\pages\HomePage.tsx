import React from 'react'
import { useAppStore } from '../stores/app-store'
import { ToggleSwitch, ProgressBar, StatusIndicator } from '../components/button'

export function HomePage() {
  const popupUI = useAppStore(state => state.popupUI)
  const toggleFloatingBall = useAppStore(state => state.toggleFloatingBall)

  // Mock data - TODO: Replace with real data from store
  const syncProgress = 85

  // Mock data - TODO: Replace with real data from store
  const todayStats = {
    conversations: 23,
    characters: '1.2k',
    websites: 5,
    duration: '12min'
  }

  const quickActions = [
    { icon: '📝', label: '新建对话' },
    { icon: '📋', label: '查看历史' },
    { icon: '📤', label: '导出数据' },
    { icon: '⚙️', label: '设置' }
  ]

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-lg)' }}>
      {/* 悬浮球控制 */}
      <div className="card fade-in">
        <div className="card-title">🔮 悬浮球控制</div>
        <div className="card-content">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
            <span style={{ fontWeight: 600 }}>启用悬浮球</span>
            <ToggleSwitch
              checked={popupUI.floatingBallEnabled}
              onChange={toggleFloatingBall}
            />
          </div>
          <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
            悬浮球将在网页上显示，方便快速访问AI功能
          </div>
        </div>
      </div>

      {/* 同步进度 */}
      <div className="card fade-in">
        <div className="card-title">📊 同步进度</div>
        <div className="card-content">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <span style={{ fontWeight: 600 }}>数据同步</span>
            <StatusIndicator type="success">✓ 已同步</StatusIndicator>
          </div>
          <ProgressBar value={syncProgress} />
          <div style={{ fontSize: '12px', color: 'var(--text-muted)', marginTop: '8px' }}>
            最后同步: 2分钟前 • 下次同步: 实时
          </div>
        </div>
      </div>

      {/* 今日统计 */}
      <div className="card fade-in">
        <div className="card-title">📈 今日统计</div>
        <div className="card-content">
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-value">{todayStats.conversations}</div>
              <div className="stat-label">对话次数</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">{todayStats.characters}</div>
              <div className="stat-label">字符数</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">{todayStats.websites}</div>
              <div className="stat-label">网站访问</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">{todayStats.duration}</div>
              <div className="stat-label">使用时长</div>
            </div>
          </div>
        </div>
      </div>

      {/* 快捷操作 */}
      <div className="card fade-in">
        <div className="card-title">⚡ 快捷操作</div>
        <div className="card-content">
          <div className="action-grid">
            {quickActions.map((action, index) => (
              <div key={index} className="action-item">
                <div className="action-icon">{action.icon}</div>
                <div className="action-label">{action.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

    </div>
  )
}
