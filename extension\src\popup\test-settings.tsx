import React, { useState } from 'react'
import { useSettings } from './hooks/useSettings'
import type { DeviceConfig } from '../common/service/SettingsStorageService'

// 笔记平台类型
type NotePlatform = 'obsidian' | 'notion' | 'markdown'

// 测试设置功能的组件
export function TestSettings() {
  const {
    settings,
    loading,
    error,
    updateDevicePath,
    browseFolder
  } = useSettings()

  // 使用本地状态管理当前查看的平台
  const [viewingPlatform, setViewingPlatform] = useState<NotePlatform>('obsidian')

  if (loading) {
    return <div>Loading settings...</div>
  }

  if (error) {
    return <div>Error: {error}</div>
  }

  if (!settings) {
    return <div>No settings available</div>
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px' }}>
      <h2>Settings Test Page</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Current Settings (JSON)</h3>
        <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px', overflow: 'auto', maxHeight: '300px' }}>
          {JSON.stringify(settings, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Current Device Info</h3>
        <p><strong>Device ID:</strong> {settings.currentDevice.currentDeviceId}</p>
        <p><strong>Device Name:</strong> {settings.currentDevice.deviceName}</p>
        <p><strong>Full Name:</strong> {settings.currentDevice.fullName}</p>
        <p><strong>OS:</strong> {settings.currentDevice.os}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Test Actions</h3>
        
        <div style={{ marginBottom: '10px' }}>
          <label>Switch Viewing Platform: </label>
          <button 
            onClick={() => setViewingPlatform('obsidian')}
            style={{ marginRight: '10px', fontWeight: viewingPlatform === 'obsidian' ? 'bold' : 'normal' }}
          >
            Obsidian
          </button>
          <button 
            onClick={() => setViewingPlatform('notion')}
            style={{ marginRight: '10px', fontWeight: viewingPlatform === 'notion' ? 'bold' : 'normal' }}
          >
            Notion
          </button>
          <button 
            onClick={() => setViewingPlatform('markdown')}
            style={{ fontWeight: viewingPlatform === 'markdown' ? 'bold' : 'normal' }}
          >
            Markdown
          </button>
        </div>

        <div style={{ marginBottom: '10px' }}>
          <button 
            onClick={() => updateDevicePath('obsidian', settings.currentDevice.currentDeviceId, 'C:\\New\\Path\\Obsidian')}
            style={{ marginRight: '10px' }}
          >
            Update Obsidian Path (Current Device)
          </button>
          <button 
            onClick={() => browseFolder('notion', settings.currentDevice.currentDeviceId)}
          >
            Browse Notion Folder (Current Device)
          </button>
        </div>
      </div>

      <div>
        <h3>Viewing Platform: {viewingPlatform}</h3>
        
        <h4>All Devices for {viewingPlatform}:</h4>
        {Object.values(settings.platforms[viewingPlatform].devices).length === 0 ? (
          <p>No devices configured for this platform</p>
        ) : (
          <ul>
            {Object.values(settings.platforms[viewingPlatform].devices).map((device: DeviceConfig) => {
              const isCurrent = device.id === settings.currentDevice.currentDeviceId
              return (
                <li key={device.id} style={{ marginBottom: '10px' }}>
                  <div>
                    <strong>{device.name}</strong> {isCurrent && <span style={{ color: 'green' }}>(Current Device)</span>}
                  </div>
                  <div>ID: {device.id}</div>
                  <div>Icon: {device.icon}</div>
                  <div>Path: {device.path || <em>Not configured</em>}</div>
                  <div>Status: {isCurrent ? 'Active' : (device.path ? 'Configured' : 'Not configured')}</div>
                  <div>Last Active: {new Date(device.lastActive).toLocaleString()}</div>
                </li>
              )
            })}
          </ul>
        )}
      </div>

      <div style={{ marginTop: '20px' }}>
        <h4>All Platforms Overview:</h4>
        {(['obsidian', 'notion', 'markdown'] as NotePlatform[]).map(platform => {
          const deviceCount = Object.keys(settings.platforms[platform].devices).length
          const currentDeviceConfig = settings.platforms[platform].devices[settings.currentDevice.currentDeviceId]
          
          return (
            <div key={platform} style={{ marginBottom: '10px', padding: '10px', background: '#f9f9f9', borderRadius: '4px' }}>
              <strong>{platform.toUpperCase()}</strong>
              <div>Total devices: {deviceCount}</div>
              <div>Current device status: {currentDeviceConfig?.path ? `Configured (${currentDeviceConfig.path})` : 'Not configured'}</div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
