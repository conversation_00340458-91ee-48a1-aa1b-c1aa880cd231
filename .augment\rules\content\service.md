---
type: "development_rules"
description: "Content Script Service层开发规则"
---

# Service层规则

## 核心职责
- **业务逻辑**: 复杂业务规则处理
- **跨模块共享**: 提供共享功能
- **外部通信**: 与Background通信
- **位置**: `content/service/`
- **命名**: `ServiceName.ts`

## 基础结构
```typescript
class ServiceName extends Singleton<ServiceName> {
  public async businessMethod(): Promise<Result> {
    // 业务逻辑
  }
}
```

## 核心规则
- 继承Singleton基类
- 无状态设计(状态由Model管理)
- 避免循环依赖
- 异步方法返回Promise
- 单文件≤300行

## 使用原则
- 简单逻辑: 直接在Inject/Capture处理
- 复杂逻辑: 抽取到Service
- 共享逻辑: 必须使用Service
