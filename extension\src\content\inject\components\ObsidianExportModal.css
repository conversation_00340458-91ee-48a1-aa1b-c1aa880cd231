/* Obsidian 导出 Modal 样式 */

/* 遮罩层 */
.obsidian-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.obsidian-modal-overlay.show {
  opacity: 1;
}

/* Modal 容器 */
.obsidian-modal {
  background: var(--obsidian-bg, #FFFFFF);
  border-radius: 12px;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.obsidian-modal-overlay.show .obsidian-modal {
  transform: scale(1);
}

/* Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--obsidian-spacing-lg, 24px);
  border-bottom: 1px solid var(--obsidian-border, #E5E7EB);
}

.header-title {
  display: flex;
  align-items: center;
  gap: var(--obsidian-spacing-md, 16px);
}

.obsidian-logo {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.obsidian-logo svg {
  width: 100%;
  height: 100%;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--obsidian-text, #1F2937);
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--obsidian-text-light, #6B7280);
  font-size: 28px;
  line-height: 1;
  cursor: pointer;
  border-radius: var(--obsidian-radius-sm, 4px);
  transition: all 0.2s ease;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: var(--obsidian-bg-hover, #F3F4F6);
  color: var(--obsidian-text, #1F2937);
}

/* Body */
.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--obsidian-spacing-lg, 24px);
}

.form-group {
  margin-bottom: var(--obsidian-spacing-lg, 24px);
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  margin-bottom: var(--obsidian-spacing-sm, 8px);
  font-size: 14px;
  font-weight: 500;
  color: var(--obsidian-text, #1F2937);
}

.form-input {
  width: 100%;
  padding: var(--obsidian-spacing-sm, 8px) var(--obsidian-spacing-md, 16px);
  border: 1px solid var(--obsidian-border, #E5E7EB);
  border-radius: var(--obsidian-radius-sm, 4px);
  font-size: 14px;
  color: var(--obsidian-text, #1F2937);
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: var(--obsidian-primary, #3B82F6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.content-preview {
  padding: var(--obsidian-spacing-md, 16px);
  background: var(--obsidian-bg-hover, #F3F4F6);
  border-radius: var(--obsidian-radius-sm, 4px);
  font-size: 14px;
  color: var(--obsidian-text-light, #6B7280);
  max-height: 150px;
  overflow-y: auto;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.metadata {
  padding: var(--obsidian-spacing-sm, 8px) var(--obsidian-spacing-md, 16px);
  background: var(--obsidian-bg-hover, #F3F4F6);
  border-radius: var(--obsidian-radius-sm, 4px);
  font-size: 14px;
  color: var(--obsidian-text-light, #6B7280);
}

/* Tag 选择器 */
.tag-selector {
  display: flex;
  flex-direction: column;
  gap: var(--obsidian-spacing-sm, 8px);
}

.tag-input-wrapper {
  position: relative;
}

.tag-input {
  width: 100%;
  padding: var(--obsidian-spacing-sm, 8px) var(--obsidian-spacing-md, 16px);
  border: 1px solid var(--obsidian-border, #E5E7EB);
  border-radius: var(--obsidian-radius-sm, 4px);
  font-size: 14px;
  color: var(--obsidian-text, #1F2937);
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.tag-input:focus {
  outline: none;
  border-color: var(--obsidian-primary, #3B82F6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--obsidian-spacing-sm, 8px);
  min-height: 32px;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: var(--obsidian-spacing-xs, 4px);
  padding: var(--obsidian-spacing-xs, 4px) var(--obsidian-spacing-sm, 8px);
  background: linear-gradient(135deg, var(--obsidian-primary, #3B82F6), var(--obsidian-secondary, #8B5CF6));
  color: white;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.tag-text {
  line-height: 1;
}

.tag-remove {
  width: 16px;
  height: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 14px;
  line-height: 1;
  cursor: pointer;
  border-radius: 50%;
  transition: background 0.2s ease;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-remove:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Footer */
.modal-footer {
  padding: var(--obsidian-spacing-lg, 24px);
  border-top: 1px solid var(--obsidian-border, #E5E7EB);
  display: flex;
  justify-content: flex-end;
}

.export-btn {
  padding: var(--obsidian-spacing-sm, 8px) var(--obsidian-spacing-lg, 24px);
  background: linear-gradient(135deg, var(--obsidian-primary, #3B82F6), var(--obsidian-secondary, #8B5CF6));
  color: white;
  border: none;
  border-radius: var(--obsidian-radius-sm, 4px);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 150px;
}

.export-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
}

.export-btn:active {
  transform: translateY(0);
}

.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Toast 提示 */
.obsidian-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: var(--obsidian-spacing-md, 16px) var(--obsidian-spacing-lg, 24px);
  background: white;
  color: var(--obsidian-text, #1F2937);
  border-radius: var(--obsidian-radius-sm, 4px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10001;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.obsidian-toast.show {
  opacity: 1;
  transform: translateX(0);
}

.obsidian-toast-success {
  border-left: 4px solid #10B981;
}

.obsidian-toast-warning {
  border-left: 4px solid #F59E0B;
}

.obsidian-toast-error {
  border-left: 4px solid #EF4444;
}

/* 响应式 */
@media (max-width: 768px) {
  .obsidian-modal {
    width: 100%;
    max-width: 95vw;
    max-height: 90vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: var(--obsidian-spacing-md, 16px);
  }
}
