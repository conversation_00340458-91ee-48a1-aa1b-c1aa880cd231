# Kimi 答案导出到 Obsidian - 需求文档

## 📋 需求概述

在 Kimi 对话页面的每个答案后添加 Obsidian 导出功能，允许用户将问答对导出为 Markdown 文件保存到 Obsidian 笔记库。

## 🎯 核心功能

### 1. Obsidian 图标注入
- **位置**: answerCompletion 组件的按钮组中（复制按钮旁边）
- **显示**: 每个答案都显示 Obsidian 图标
- **交互**: 悬浮时显示 tooltip "点击导出 markdown 到 obsidian"

### 2. 导出卡片弹窗
- **触发**: 点击 Obsidian 图标
- **样式**: 
  - 居中 Modal 弹窗
  - 半透明背景遮罩
  - z-index: 10000
  - 支持点击遮罩关闭
  - 支持 ESC 键关闭
  - 蓝色主题，紫色辅助
  
- **内容**:
  - Obsidian 图标（顶部）
  - 标题输入框（预填充问题内容）
  - 正文预览（显示答案内容，只读）
  - Tag 标签选择器（高级版）
    - 输入框输入新标签
    - 显示已添加的标签列表
    - 点击 X 删除标签
    - 支持历史标签选择（可选）
  - 元数据预览（时间戳，自动生成）
  - "Add to Obsidian" 大按钮

### 3. 导出功能
- **文件命名**: `问题标题.md`
  - 过滤特殊字符: `/ \ : * ? " < > |`
  - 重复时添加后缀: `_1`, `_2` ...
  - 长度限制: 最多 100 字符
  
- **Markdown 格式**:
```markdown
---
title: [问题标题]
date: [创建时间 YYYY-MM-DD HH:mm:ss]
tags: [tag1, tag2, tag3]
source: Kimi Chat
---

# [问题标题]

## 问题

[问题内容]

## 答案

[答案内容]
```

- **保存位置**: 从 `ContentSettingsService.getExportPath('obsidian')` 获取
- **错误处理**:
  - 未配置路径: Toast 提示 "请先在设置中配置 Obsidian 导出路径"
  - 导出成功: Toast 提示 "✅ 导出成功！"
  - 导出失败: Toast 提示 "❌ 导出失败: [错误信息]"

## 🔑 关键设计决策

### 1. answerIndex 管理
**决策**: 不维护独立的问答对数组
**原因**: `promptList` 和 `answerList` 的相同下标天然对应一对问答
**实现**: 通过数组索引获取问答对

### 2. 图标位置
**决策**: 每个答案都添加，位于复制按钮旁边
**原因**: 符合用户习惯，操作便捷

### 3. 弹窗样式
**决策**: 居中 Modal，蓝紫主题
**原因**: 视觉一致性，符合产品设计语言

### 4. Tag 交互
**决策**: 高级标签选择器
**功能**:
- 输入框添加新标签
- 标签列表展示
- 点击删除标签
- 历史标签推荐（可选）

### 5. 文件命名
**决策**: 问题标题作为文件名
**原因**: 直观易识别，便于搜索

### 6. 错误处理
**决策**: Toast 提示 + 配置检查
**原因**: 用户友好，快速反馈

## 🎨 UI/UX 规范

### 颜色方案
- **主色**: 蓝色 (#3B82F6)
- **辅助色**: 紫色 (#8B5CF6)
- **背景**: 白色/浅灰
- **文字**: 深灰 (#1F2937)
- **边框**: 浅灰 (#E5E7EB)

### 间距规范
- 卡片内边距: 24px
- 元素间距: 16px
- 按钮高度: 44px
- 输入框高度: 40px

### 字体规范
- 标题: 18px, font-weight: 600
- 正文: 14px, font-weight: 400
- 按钮: 16px, font-weight: 500
- 提示: 12px, font-weight: 400

## 🔗 依赖关系

### 数据依赖
- `AnswerModel`: 获取问答对数据
- `ContentSettingsService`: 获取 Obsidian 导出路径

### 组件依赖
- 参考 `ArchiveButtonInject` 的注入模式
- 参考 `FloatingBubbleInject` 的 UI 组件设计

### 事件依赖
- 监听 `ANSWER_EXTRACTED` 事件（需新增）
- 触发 `EXPORT_SUCCESS` / `EXPORT_FAILED` 事件（可选）

## 📏 技术约束

### 性能要求
- 图标注入延迟 < 100ms
- Modal 打开动画 < 300ms
- 文件导出操作 < 1s

### 兼容性要求
- Chrome 90+
- 支持 Kimi 当前页面结构
- 不影响页面原有功能

### 安全要求
- 文件名过滤特殊字符
- 路径验证防止路径遍历攻击
- 内容转义防止 XSS

## ✅ 验收标准

### 功能验收
- [ ] 每个答案都显示 Obsidian 图标
- [ ] 悬浮显示正确的 tooltip
- [ ] 点击图标弹出导出卡片
- [ ] 卡片正确显示问题和答案
- [ ] Tag 输入和删除功能正常
- [ ] 点击 "Add to Obsidian" 成功导出
- [ ] 未配置路径时显示正确提示
- [ ] 导出成功/失败显示正确反馈

### 用户体验验收
- [ ] 交互流畅，无明显卡顿
- [ ] UI 美观，符合设计规范
- [ ] 错误提示清晰易懂
- [ ] 文件命名符合预期

### 代码质量验收
- [ ] 代码结构清晰，职责分明
- [ ] 错误处理完善
- [ ] 类型定义完整
- [ ] 注释清晰

## 📅 里程碑

### Phase 1: 基础功能 (Day 1-2)
- AnswerModel 改进
- 事件机制
- ObsidianExportButton 组件

### Phase 2: UI 组件 (Day 3-4)
- ObsidianExportModal 组件
- Tag 选择器
- 样式美化

### Phase 3: 导出功能 (Day 5)
- ObsidianExportService
- 文件命名和保存
- 错误处理

### Phase 4: 集成测试 (Day 6)
- 端到端测试
- Bug 修复
- 性能优化

## 🔍 风险评估

### 高风险
- **DOM 结构变化**: Kimi 页面更新可能导致选择器失效
  - **缓解**: 使用灵活的选择器策略，易于维护

### 中风险
- **文件系统权限**: 浏览器扩展无法直接写文件
  - **缓解**: 使用 Chrome 的文件系统 API 或下载 API

### 低风险
- **性能影响**: 大量问答对可能影响性能
  - **缓解**: 按需注入，懒加载

## 📚 参考资料

- Obsidian 官方文档: https://help.obsidian.md/
- Chrome Extension File API: https://developer.chrome.com/docs/extensions/reference/
- 现有组件参考: `ArchiveButtonInject.ts`, `FloatingBubbleInject.ts`
