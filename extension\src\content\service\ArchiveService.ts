import { Singleton } from "@/common/base/Singleton";
import { BaseAIAdapter } from "../adapters/core/BaseAIAdapter";
import { InputModel } from "../model/InputModel";
import { CreateChatPromptResp, CreateChatHistoryReq } from "@/common/types/content_vo";
import { DatabaseResult } from "@/common/types/comm_vo";
import { chatPromptDatabaseProxy } from "@/common/service/ChatPromptDatabaseProxy";
import { chatHistoryDatabaseProxy } from "@/common/service/ChatHistoryDatabaseProxy";
import { ChatHistoryEntity, PlatformEntity } from "@/common/types/database_entity";
import { GenerateUtils } from "../utils/GenerateUtils";

/**
 * 
 * content模块存档服务
 */
export default class ArchiveService  extends Singleton<ArchiveService>{
  private archivedChatUidSet: Set<string> = new Set();
  private archivedPromptSet:  Set<string> = new Set();
  
  /**
   * 存档提示词
   * @param inputPrompt 提示词
   * @param platformResult 平台信息
   * @return promptUid 提示词的唯一id
  */
  public async archivePrompt(inputPrompt: string, platformId: number): Promise<string> {
    if (!inputPrompt || inputPrompt.trim().length === 0) {
      console.warn('【EchoSync】ArchiveButtonInject: Input is empty, cannot archive')
      return ""
    }

    const promptUid = await GenerateUtils.promptUid(inputPrompt)
    try {
      const archiveData = {
        chat_prompt: inputPrompt,
        prompt_uid: promptUid,
        platform_id: platformId,
        create_time: Date.now()
      }
      console.info('【EchoSync】Archiving prompt:', archiveData)
      const result: DatabaseResult<CreateChatPromptResp> = await chatPromptDatabaseProxy.createChatPrompt(archiveData)

      if (result.success) {
        this.archivedChatUidSet.add(promptUid)
        this.archivedPromptSet.add(inputPrompt)
        console.log('【EchoSync】Prompt archived successfully:', result.data)
      } else {
        console.error('【EchoSync】Archive failed:', result.error)
      }
    } catch (error) {
      console.error('【EchoSync】Archive prompt error:', error)
    }
    return promptUid
  }

  public isArchivedPrompt(prompt: string): boolean {
    if (!prompt || typeof prompt !== 'string') {
      return false;
    }
    return this.archivedPromptSet.has(prompt)
  }


  /**
   * 存档答案
   */
  public async archiveAnswer(answerData: CreateChatHistoryReq): Promise<void> {
    if (!answerData || answerData.chat_answer.trim().length === 0) {
      console.warn('【ArchiveService】Answer is empty, cannot archive')
      return
    }

    try {
      console.info('【ArchiveService】Archiving answer start--->:', answerData.chat_answer.slice(0, 50))
      const result: DatabaseResult<ChatHistoryEntity> = await chatHistoryDatabaseProxy.createChatHistory(answerData)

      if (result.success) {
        console.log('【ArchiveService】Answer archived successfully<---:', result.data.chat_answer.slice(0, 50))
      } else {
        console.error('【ArchiveService】Archive answer failed:', result.error)
      }
    } catch (error) {
      console.error('【ArchiveService】Archive answer error:', error)
    }
  }

  destroy(): void {
    this.archivedChatUidSet.clear()
    this.archivedPromptSet.clear()
    console.info('【ArchiveService】Destroyed')
  }

}