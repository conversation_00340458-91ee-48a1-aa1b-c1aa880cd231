# 4.4 测试指南

项目采用 Jest 和 React Testing Library (RTL) 作为主要的测试框架，专注于单元测试和组件测试，以确保代码的质量和稳定性。

## 1. 测试理念

-   **单元测试**: 针对独立的、小块的逻辑单元（如 `lib` 中的工具函数）进行测试，确保其输入输出符合预期。
-   **组件测试**: 使用 RTL 测试 React 组件。我们不关心组件的内部实现细节，而是从用户的角度出发，测试组件是否正确渲染、响应用户交互（点击、输入等）以及是否符合无障碍标准。
-   **集成测试**: 测试多个模块协同工作的场景，例如测试 Popup 页面能否成功从 `zustand` store 获取状态并正确渲染。

## 2. 如何运行测试

在项目根目录或 `extension` 目录下执行以下命令：

```bash
# 运行所有测试用例
npm run test

# 启动监听模式
# Jest会智能地只运行与你当前修改的文件相关的测试，非常适合开发过程中使用。
npm run test:watch

# 生成测试覆盖率报告
# 报告会生成在 `coverage/` 目录下，可以打开 `index.html` 查看详情。
npm run test:coverage
```

## 3. 编写测试用例

测试文件通常与被测试��源文件放在一起，并以 `.test.ts` 或 `.test.tsx` 结尾。

### 示例：测试一个工具函数

```typescript
// extension/src/lib/utils.test.ts

import { formatTimestamp } from './utils';

describe('formatTimestamp', () => {
  it('should format a valid timestamp correctly', () => {
    const timestamp = 1672531200000; // 2023-01-01 00:00:00 UTC
    // 假设我们的函数格式化为 'YYYY-MM-DD HH:mm'
    expect(formatTimestamp(timestamp)).toBe('2023-01-01 08:00'); // 假设在UTC+8时区
  });

  it('should handle null or undefined gracefully', () => {
    expect(formatTimestamp(null)).toBe('');
    expect(formatTimestamp(undefined)).toBe('');
  });
});
```

### 示例：测试一个React组件

```tsx
// extension/src/popup/components/Header.test.tsx

import { render, screen, fireEvent } from '@testing-library/react';
import { Header } from './Header';

// Mock a part of the zustand store
jest.mock('@/stores/app-store', () => ({
  useAppStore: () => ({
    syncEnabled: true,
    toggleSync: jest.fn(),
  }),
}));

describe('Header Component', () => {
  it('should render the title correctly', () => {
    render(<Header />);
    // 使用 screen.getByText 来查找元素，更贴近用户视角
    expect(screen.getByText('EchoSync')).toBeInTheDocument();
  });

  it('should display the correct switch state based on store', () => {
    render(<Header />);
    const switchElement = screen.getByRole('switch');
    // 检查开关是否根据模拟的 store 状态被选中
    expect(switchElement).toBeChecked();
  });

  it('should call toggleSync when the switch is clicked', () => {
    const { useAppStore } = require('@/stores/app-store');
    const mockToggleSync = useAppStore().toggleSync;

    render(<Header />);
    const switchElement = screen.getByRole('switch');
    
    // 模拟用户点击事件
    fireEvent.click(switchElement);

    // 断言 store 中的 toggleSync 方法被调用
    expect(mockToggleSync).toHaveBeenCalledTimes(1);
  });
});
```

## 4. E2E (端到端) 测试 (未来规划)

目前项目主要依赖单元和组件测试。未来，我们计划引入 Playwright 或 Cypress 等 E2E 测试框架，用于模拟真实的用户操作流程，例如：

-   用户在 ChatGPT 页面输入提示词。
-   打开 Gemini 页面，验证提示词是否已同步过来。
-   点击 Popup 中的历史记录，验证输入框是否被正确填充。

这将为项目的核心功能提供更高级别的保障。
