import './ObsidianExportButton.css';

/**
 * Obsidian 导出按钮组件
 * 渲染 Obsidian 图标，处理点击事件
 */
export class ObsidianExportButton {
    private element: HTMLButtonElement;
    private tooltip: HTMLElement | null = null;
    private answerIndex: number;
    private clickHandler: ((index: number) => void) | null = null;

    constructor(answerIndex: number) {
        this.answerIndex = answerIndex;
        this.element = this.createButton();
    }

    /**
     * 创建按钮元素
     */
    private createButton(): HTMLButtonElement {
        const button = document.createElement('button');
        button.className = 'obsidian-export-btn';
        button.setAttribute('data-answer-index', String(this.answerIndex));
        
        // 添加 Obsidian Logo SVG
        button.innerHTML = this.getObsidianLogoSVG();
        
        // 创建并添加 Tooltip
        this.tooltip = this.createTooltip();
        button.appendChild(this.tooltip);
        
        // 绑定事件
        button.addEventListener('click', this.handleClick.bind(this));
        button.addEventListener('mouseenter', this.handleMouseEnter.bind(this));
        button.addEventListener('mouseleave', this.handleMouseLeave.bind(this));
        
        return button;
    }

    /**
     * 创建自定义 Tooltip 元素
     */
    private createTooltip(): HTMLElement {
        const tooltip = document.createElement('div');
        tooltip.className = 'obsidian-tooltip';
        tooltip.textContent = '导出到 Obsidian';
        return tooltip;
    }

    /**
     * 获取 Obsidian Logo SVG
     */
    private getObsidianLogoSVG(): string {
        return `
            <svg width="20" height="20" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <radialGradient id="obsidian-grad-b" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-48 -185 123 -32 179 429.7)">
                        <stop stop-color="#fff" stop-opacity=".4"/>
                        <stop offset="1" stop-opacity=".1"/>
                    </radialGradient>
                    <radialGradient id="obsidian-grad-c" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(41 -310 229 30 341.6 351.3)">
                        <stop stop-color="#fff" stop-opacity=".6"/>
                        <stop offset="1" stop-color="#fff" stop-opacity=".1"/>
                    </radialGradient>
                    <radialGradient id="obsidian-grad-d" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(57 -261 178 39 190.5 296.3)">
                        <stop stop-color="#fff" stop-opacity=".8"/>
                        <stop offset="1" stop-color="#fff" stop-opacity=".4"/>
                    </radialGradient>
                    <radialGradient id="obsidian-grad-e" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-79 -133 153 -90 321.4 464.2)">
                        <stop stop-color="#fff" stop-opacity=".3"/>
                        <stop offset="1" stop-opacity=".3"/>
                    </radialGradient>
                </defs>
                <path d="M359.9 434.3c-2.6 19.1-21.3 34-40 28.9-26.4-7.3-57-18.7-84.7-20.8l-42.3-3.2a27.9 27.9 0 0 1-18-8.4l-73-75a27.9 27.9 0 0 1-5.4-31s45.1-99 46.8-104.2c1.7-5.1 7.8-50 11.4-74.2a28 28 0 0 1 9-16.6l86.2-77.5a28 28 0 0 1 40.6 3.5l72.5 92a29.7 29.7 0 0 1 6.2 18.3c0 17.4 1.5 53.2 11.1 76.3a303 303 0 0 0 35.6 58.5 14 14 0 0 1 1.1 15.7c-6.4 10.8-18.9 31.4-36.7 57.9a143.3 143.3 0 0 0-20.4 59.8Z" fill="#6C31E3"/>
                <path d="M182.7 436.4c33.9-68.7 33-118 18.5-153-13.2-32.4-37.9-52.8-57.3-65.5-.4 1.9-1 3.7-1.8 5.4L96.5 324.8a27.9 27.9 0 0 0 5.5 31l72.9 75c2.3 2.3 5 4.2 7.8 5.6Z" fill="url(#obsidian-grad-b)"/>
                <path d="M274.9 297c9.1.9 18 2.9 26.8 6.1 27.8 10.4 53.1 33.8 74 78.9 1.5-2.6 3-5.1 4.6-7.5a1222 1222 0 0 0 36.7-57.9 14 14 0 0 0-1-15.7 303 303 0 0 1-35.7-58.5c-9.6-23-11-58.9-11.1-76.3 0-6.6-2.1-13.1-6.2-18.3l-72.5-92-1.2-1.5c5.3 17.5 5 31.5 1.7 44.2-3 11.8-8.6 22.5-14.5 33.8-2 3.8-4 7.7-5.9 11.7a140 140 0 0 0-15.8 58c-1 24.2 3.9 54.5 20 95Z" fill="url(#obsidian-grad-c)"/>
                <path d="M274.8 297c-16.1-40.5-21-70.8-20-95 1-24 8-42 15.8-58l6-11.7c5.8-11.3 11.3-22 14.4-33.8a78.5 78.5 0 0 0-1.7-44.2 28 28 0 0 0-39.4-2l-86.2 77.5a28 28 0 0 0-9 16.6L144.2 216c0 .7-.2 1.3-.3 2 19.4 12.6 44 33 57.3 65.3 2.6 6.4 4.8 13.1 6.4 20.4a200 200 0 0 1 67.2-6.8Z" fill="url(#obsidian-grad-d)"/>
                <path d="M320 463.2c18.6 5.1 37.3-9.8 39.9-29a153 153 0 0 1 15.9-52.2c-21-45.1-46.3-68.5-74-78.9-29.5-11-61.6-7.3-94.2.6 7.3 33.1 3 76.4-24.8 132.7 3.1 1.6 6.6 2.5 10.1 2.8l43.9 3.3c23.8 1.7 59.3 14 83.2 20.7Z" fill="url(#obsidian-grad-e)"/>
            </svg>
        `;
    }

    /**
     * 处理点击事件
     */
    private handleClick(event: MouseEvent): void {
        event.preventDefault();
        event.stopPropagation();
        
        console.log(`[ObsidianExportButton] 点击导出按钮，索引: ${this.answerIndex}`);
        
        if (this.clickHandler) {
            this.clickHandler(this.answerIndex);
        }
    }

    /**
     * 处理鼠标进入事件
     */
    private handleMouseEnter(): void {
        this.element.style.color = 'var(--obsidian-primary, #3B82F6)';
    }

    /**
     * 处理鼠标离开事件
     */
    private handleMouseLeave(): void {
        this.element.style.color = '';
    }

    /**
     * 设置点击回调
     */
    public onClick(handler: (index: number) => void): void {
        this.clickHandler = handler;
    }

    /**
     * 渲染组件，返回 DOM 元素
     */
    public render(): HTMLButtonElement {
        return this.element;
    }

    /**
     * 销毁组件
     */
    public destroy(): void {
        this.element.removeEventListener('click', this.handleClick);
        this.element.removeEventListener('mouseenter', this.handleMouseEnter);
        this.element.removeEventListener('mouseleave', this.handleMouseLeave);
        this.element.remove();
    }
}
