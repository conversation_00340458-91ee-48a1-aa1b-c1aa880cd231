import { ChatPromptListResp } from '@/common/types/content_vo'
import { PlatformIcon } from '@/content/inject/components/PlatformIcon'

export interface HistoryBubbleOptions {
  maxItems?: number
  animationDuration?: number
  showPlatformIcons?: boolean
  pageSize?: number
  enableSearch?: boolean
  enableTimeFilter?: boolean
}

export interface HistoryBubbleEvents {
  onItemClick: (chat: ChatPromptListResp) => void
}

/**
 * 历史记录模态页UI组件
 * 纯UI组件，负责渲染和样式
 */
export class HistoryBubble {
  private modalContainer: HTMLElement | null = null
  private backdropElement: HTMLElement | null = null
  private contentContainer: HTMLElement | null = null
  private toastContainer: HTMLElement | null = null
  private isVisible: boolean = false
  private chatHistory: ChatPromptListResp[] = []
  private filteredHistory: ChatPromptListResp[] = []
  private options: Required<HistoryBubbleOptions>
  private platformIcon: PlatformIcon
  private events: HistoryBubbleEvents
  private eventListeners: Array<{element: Element | Document, event: string, handler: EventListener}> = []
  private currentPage: number = 1
  private searchQuery: string = ''
  private timeFilter: string = 'all' // 'all', 'today', 'yesterday', 'week', 'month'

  constructor(options: HistoryBubbleOptions = {}, events: HistoryBubbleEvents) {
    this.options = {
      maxItems: options.maxItems || 10,
      animationDuration: options.animationDuration || 300,
      showPlatformIcons: options.showPlatformIcons !== false,
      pageSize: options.pageSize || 20,
      enableSearch: options.enableSearch !== false,
      enableTimeFilter: options.enableTimeFilter !== false
    }

    this.events = events
    this.platformIcon = new PlatformIcon()
    this.createModalStructure()
    this.loadStyles()
  }

  /**
   * 创建模态页结构
   */
  private createModalStructure(): void {
    // 创建模态页容器
    this.modalContainer = document.createElement('div')
    this.modalContainer.id = 'echosync-history-modal'
    this.modalContainer.className = 'echosync-history-modal'

    // 创建背景遮罩
    this.backdropElement = document.createElement('div')
    this.backdropElement.className = 'echosync-modal-backdrop'

    // 创建内容容器
    this.contentContainer = document.createElement('div')
    this.contentContainer.className = 'echosync-modal-content'

    // 创建Toast容器
    this.toastContainer = document.createElement('div')
    this.toastContainer.className = 'echosync-toast-container'

    // 组装结构
    this.modalContainer.appendChild(this.backdropElement)
    this.modalContainer.appendChild(this.contentContainer)
    document.body.appendChild(this.modalContainer)
    document.body.appendChild(this.toastContainer)

    // 绑定事件
    this.setupEventListeners()
  }

  /**
   * 加载样式文件
   */
  private loadStyles(): void {
    if (document.getElementById('echosync-history-modal-styles')) return

    // 加载外部CSS文件
    const link = document.createElement('link')
    link.id = 'echosync-history-modal-styles'
    link.rel = 'stylesheet'
    link.href = chrome.runtime.getURL('src/content/inject/components/HistoryBubble.css')
    document.head.appendChild(link)
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 点击背景关闭模态页
    const backdropClickHandler = (event: MouseEvent) => {
      if (event.target === this.backdropElement) {
        this.hide()
      }
    }
    this.backdropElement?.addEventListener('click', backdropClickHandler)
    this.eventListeners.push({
      element: this.backdropElement!,
      event: 'click',
      handler: backdropClickHandler
    })

    // ESC键关闭模态页
    const keydownHandler = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && this.isVisible) {
        this.hide()
      }
    }
    document.addEventListener('keydown', keydownHandler)
    this.eventListeners.push({
      element: document,
      event: 'keydown',
      handler: keydownHandler
    })
  }

  /**
   * 更新聊天历史数据
   */
  public updateHistory(history: ChatPromptListResp[]): void {
    this.chatHistory = history
    this.applyFilters()
    this.renderContent()
  }

  /**
   * 应用筛选条件
   */
  private applyFilters(): void {
    let filtered = [...this.chatHistory]

    // 应用搜索筛选
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase().trim()
      filtered = filtered.filter(chat => 
        chat.chat_prompt.toLowerCase().includes(query)
      )
    }

    // 应用时间筛选
    if (this.timeFilter !== 'all') {
      const now = Date.now()
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const todayStart = today.getTime()

      filtered = filtered.filter(chat => {
        const chatTime = chat.create_time
        switch (this.timeFilter) {
          case 'today':
            return chatTime >= todayStart
          case 'yesterday':
            const yesterdayStart = todayStart - 24 * 60 * 60 * 1000
            return chatTime >= yesterdayStart && chatTime < todayStart
          case 'week':
            const weekStart = todayStart - 7 * 24 * 60 * 60 * 1000
            return chatTime >= weekStart
          case 'month':
            const monthStart = todayStart - 30 * 24 * 60 * 60 * 1000
            return chatTime >= monthStart
          default:
            return true
        }
      })
    }

    this.filteredHistory = filtered
    this.currentPage = 1 // 重置到第一页
  }

  /**
   * 渲染内容
   */
  private renderContent(): void {
    if (!this.contentContainer) return

    // 清空容器
    this.contentContainer.innerHTML = ''

    // 添加标题
    const header = document.createElement('div')
    header.className = 'echosync-history-header'
    header.innerHTML = `
      <h3 class="echosync-history-title">历史提示词</h3>
    `
    this.contentContainer.appendChild(header)

    // 添加搜索和筛选区域
    if (this.options.enableSearch || this.options.enableTimeFilter) {
      const filtersDiv = this.createFiltersSection()
      this.contentContainer.appendChild(filtersDiv)
    }

    // 添加统计信息
    const statsDiv = this.createStatsSection()
    this.contentContainer.appendChild(statsDiv)

    // 创建列表容器
    const listContainer = document.createElement('div')
    listContainer.className = 'echosync-history-list'

    const currentPageData = this.getCurrentPageData()
    if (currentPageData.length === 0) {
      // 显示空状态
      const emptyDiv = document.createElement('div')
      emptyDiv.className = 'echosync-history-empty'
      emptyDiv.textContent = this.filteredHistory.length === 0 ? 
        (this.searchQuery || this.timeFilter !== 'all' ? '没有找到匹配的记录' : '暂无历史提示词') :
        '暂无历史提示词'
      listContainer.appendChild(emptyDiv)
    } else {
      // 渲染历史记录
      currentPageData.forEach((chat, index) => {
        const item = this.createHistoryItem(chat, index)
        listContainer.appendChild(item)
      })
    }

    this.contentContainer.appendChild(listContainer)

    // 添加分页控件
    if (this.getTotalPages() > 1) {
      const paginationDiv = this.createPaginationSection()
      this.contentContainer.appendChild(paginationDiv)
    }
  }

  /**
   * 创建历史记录项
   */
  private createHistoryItem(chat: ChatPromptListResp, index: number): HTMLElement {
    const item = document.createElement('div')
    item.className = 'echosync-history-item'
    item.dataset.chatId = chat.id.toString()
    item.dataset.promptUid = chat.prompt_uid

    // 格式化时间
    const timeInfo = this.formatTime(chat.create_time)

    // 创建内容
    const content = document.createElement('div')
    content.className = 'echosync-history-item-content'

    const textDiv = document.createElement('div')
    textDiv.className = 'echosync-history-item-text'

    const promptDiv = document.createElement('div')
    promptDiv.className = 'echosync-history-item-prompt'
    promptDiv.textContent = chat.chat_prompt
    promptDiv.title = chat.chat_prompt

    const metaDiv = document.createElement('div')
    metaDiv.className = 'echosync-history-item-meta'

    const timeSpan = document.createElement('div')
    timeSpan.className = 'echosync-history-item-time'
    
    const relativeTime = document.createElement('span')
    relativeTime.className = 'echosync-time-relative'
    relativeTime.textContent = timeInfo.relative
    
    const separator = document.createElement('span')
    separator.textContent = '·'
    separator.style.opacity = '0.5'
    
    const absoluteTime = document.createElement('span')
    absoluteTime.className = 'echosync-time-absolute'
    absoluteTime.textContent = timeInfo.absolute
    
    timeSpan.appendChild(relativeTime)
    timeSpan.appendChild(separator)
    timeSpan.appendChild(absoluteTime)

    const platformsDiv = document.createElement('div')
    platformsDiv.className = 'echosync-history-item-platforms'

    // 添加平台图标
    if (this.options.showPlatformIcons && (chat.platform_info && chat.platform_info.length > 0)) {
      chat.platform_info.forEach(platform => {
        const iconElement = this.platformIcon.createIcon(platform, { size: 16 })
        platformsDiv.appendChild(iconElement)
      })
    }

    metaDiv.appendChild(timeSpan)
    metaDiv.appendChild(platformsDiv)

    textDiv.appendChild(promptDiv)
    textDiv.appendChild(metaDiv)
    content.appendChild(textDiv)
    item.appendChild(content)

    // 添加点击事件
    item.addEventListener('click', (event) => {
      this.handleItemClick(chat, event.currentTarget as HTMLElement, event)
    })

    return item
  }

  /**
   * 处理项目点击
   */
  private async handleItemClick(chat: ChatPromptListResp, clickedElement: HTMLElement, event: MouseEvent): Promise<void> {
    // 复制提示词到剪贴板
    const copySuccess = await this.copyToClipboard(chat.chat_prompt)

    // 获取点击位置
    const clickPosition = {
      x: event.clientX,
      y: event.clientY
    }

    // 显示Toast提示
    if (copySuccess) {
      this.showToast('已复制到剪贴板', clickPosition)
    } else {
      this.showToast('复制失败，请重试', clickPosition, true)
    }

    // 调用事件回调
    this.events.onItemClick(chat)

    // 延迟隐藏模态页，让用户看到Toast效果
    setTimeout(() => {
      this.hide()
    }, 500)
  }

  /**
   * 显示Toast提示
   */
  private showToast(message: string, position: {x: number, y: number}, isError: boolean = false): void {
    if (!this.toastContainer) return

    // 创建Toast元素
    const toast = document.createElement('div')
    toast.className = `echosync-toast ${isError ? 'error' : ''}`
    toast.textContent = message

    // 计算位置（在点击位置附近显示）
    const toastWidth = 150 // 预估宽度
    const toastHeight = 40 // 预估高度

    let left = position.x - toastWidth / 2
    let top = position.y - toastHeight - 10

    // 边界检查
    if (left < 10) left = 10
    if (left + toastWidth > window.innerWidth - 10) {
      left = window.innerWidth - toastWidth - 10
    }
    if (top < 10) {
      top = position.y + 10
    }

    // 设置位置
    toast.style.left = `${left}px`
    toast.style.top = `${top}px`

    // 添加到容器
    this.toastContainer.appendChild(toast)

    // 显示动画
    requestAnimationFrame(() => {
      toast.classList.add('show')
    })

    // 3秒后自动隐藏
    setTimeout(() => {
      toast.classList.remove('show')
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast)
        }
      }, 300) // 等待隐藏动画完成
    }, 3000)
  }

  /**
   * 复制文本到剪贴板
   */
  private async copyToClipboard(text: string): Promise<boolean> {
    try {
      // 优先使用现代 Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text)
        return true
      } else {
        // 降级方案：使用 document.execCommand
        return this.fallbackCopyToClipboard(text)
      }
    } catch (error) {
      console.error('复制到剪贴板失败:', error)
      return this.fallbackCopyToClipboard(text)
    }
  }

  /**
   * 降级复制方案
   */
  private fallbackCopyToClipboard(text: string): boolean {
    try {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      const result = document.execCommand('copy')
      document.body.removeChild(textArea)
      return result
    } catch (error) {
      console.error('降级复制方案失败:', error)
      return false
    }
  }

  /**
   * 格式化时间
   */
  private formatTime(timestamp: number): { relative: string; absolute: string } {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    let relative: string
    if (minutes < 1) relative = '刚刚'
    else if (minutes < 60) relative = `${minutes}分钟前`
    else if (hours < 24) relative = `${hours}小时前`
    else if (days < 7) relative = `${days}天前`
    else relative = new Date(timestamp).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    })

    // 绝对时间格式
    const date = new Date(timestamp)
    const isToday = date.toDateString() === new Date().toDateString()
    const isThisYear = date.getFullYear() === new Date().getFullYear()

    let absolute: string
    if (isToday) {
      absolute = date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    } else if (isThisYear) {
      absolute = date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit'
      }) + ' ' + date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    } else {
      absolute = date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }) + ' ' + date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    return { relative, absolute }
  }

  /**
   * 显示模态页
   */
  public show(anchorElement?: HTMLElement): void {
    if (!this.modalContainer || this.isVisible) return

    // 显示模态页
    this.modalContainer.style.display = 'block'

    // 触发显示动画
    requestAnimationFrame(() => {
      if (this.modalContainer) {
        this.modalContainer.classList.add('show')
      }
    })

    this.isVisible = true
  }

  /**
   * 隐藏模态页
   */
  public hide(): void {
    if (!this.modalContainer || !this.isVisible) return

    // 移除显示类，触发隐藏动画
    this.modalContainer.classList.remove('show')

    // 动画完成后隐藏元素
    setTimeout(() => {
      if (this.modalContainer) {
        this.modalContainer.style.display = 'none'
      }
    }, this.options.animationDuration)

    this.isVisible = false
  }

  /**
   * 切换显示状态
   */
  public toggle(anchorElement?: HTMLElement): void {
    if (this.isVisible) {
      this.hide()
    } else {
      this.show(anchorElement)
    }
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    // 清理事件监听器
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler)
    })
    this.eventListeners = []

    // 移除DOM元素
    if (this.modalContainer) {
      this.modalContainer.remove()
      this.modalContainer = null
    }
    if (this.toastContainer) {
      this.toastContainer.remove()
      this.toastContainer = null
    }

    // 移除样式
    const styles = document.getElementById('echosync-history-modal-styles')
    if (styles) {
      styles.remove()
    }

    this.isVisible = false
  }

  /**
   * 获取可见状态
   */
  public get visible(): boolean {
    return this.isVisible
  }

  /**
   * 创建筛选区域
   */
  private createFiltersSection(): HTMLElement {
    const filtersDiv = document.createElement('div')
    filtersDiv.className = 'echosync-history-filters'

    const filtersRow = document.createElement('div')
    filtersRow.className = 'echosync-filters-row'

    // 搜索框
    if (this.options.enableSearch) {
      const searchBox = document.createElement('div')
      searchBox.className = 'echosync-search-box'

      const searchInput = document.createElement('input')
      searchInput.type = 'text'
      searchInput.className = 'echosync-search-input'
      searchInput.placeholder = '搜索提示词内容...'
      searchInput.value = this.searchQuery

      const searchIcon = document.createElement('div')
      searchIcon.className = 'echosync-search-icon'
      searchIcon.innerHTML = '🔍'

      searchBox.appendChild(searchIcon)
      searchBox.appendChild(searchInput)

      // 搜索事件
      searchInput.addEventListener('input', (e) => {
        this.searchQuery = (e.target as HTMLInputElement).value
        this.applyFilters()
        this.renderContent()
      })

      filtersRow.appendChild(searchBox)
    }

    // 时间筛选
    if (this.options.enableTimeFilter) {
      const timeFilterDiv = document.createElement('div')
      timeFilterDiv.className = 'echosync-time-filter'

      const filters = [
        { key: 'all', label: '全部' },
        { key: 'today', label: '今天' },
        { key: 'yesterday', label: '昨天' },
        { key: 'week', label: '本周' },
        { key: 'month', label: '本月' }
      ]

      filters.forEach(filter => {
        const btn = document.createElement('button')
        btn.className = `echosync-filter-btn ${this.timeFilter === filter.key ? 'active' : ''}`
        btn.textContent = filter.label
        btn.addEventListener('click', () => {
          this.timeFilter = filter.key
          this.applyFilters()
          this.renderContent()
        })
        timeFilterDiv.appendChild(btn)
      })

      filtersRow.appendChild(timeFilterDiv)
    }

    filtersDiv.appendChild(filtersRow)
    return filtersDiv
  }

  /**
   * 创建统计信息区域
   */
  private createStatsSection(): HTMLElement {
    const statsDiv = document.createElement('div')
    statsDiv.className = 'echosync-history-stats'

    const total = this.filteredHistory.length
    const totalPages = this.getTotalPages()
    const currentStart = (this.currentPage - 1) * this.options.pageSize + 1
    const currentEnd = Math.min(this.currentPage * this.options.pageSize, total)

    let statsText = `共 ${total} 条记录`
    if (totalPages > 1) {
      statsText += ` · 第 ${currentStart}-${currentEnd} 条 · 第 ${this.currentPage} 页，共 ${totalPages} 页`
    }

    statsDiv.textContent = statsText
    return statsDiv
  }

  /**
   * 获取当前页数据
   */
  private getCurrentPageData(): ChatPromptListResp[] {
    const start = (this.currentPage - 1) * this.options.pageSize
    const end = start + this.options.pageSize
    return this.filteredHistory.slice(start, end)
  }

  /**
   * 获取总页数
   */
  private getTotalPages(): number {
    return Math.ceil(this.filteredHistory.length / this.options.pageSize)
  }

  /**
   * 创建分页区域
   */
  private createPaginationSection(): HTMLElement {
    const paginationDiv = document.createElement('div')
    paginationDiv.className = 'echosync-pagination'

    const totalPages = this.getTotalPages()

    // 上一页按钮
    const prevBtn = document.createElement('button')
    prevBtn.className = 'echosync-pagination-btn'
    prevBtn.textContent = '‹'
    prevBtn.disabled = this.currentPage === 1
    prevBtn.addEventListener('click', () => {
      if (this.currentPage > 1) {
        this.currentPage--
        this.renderContent()
      }
    })
    paginationDiv.appendChild(prevBtn)

    // 页码按钮
    const startPage = Math.max(1, this.currentPage - 2)
    const endPage = Math.min(totalPages, this.currentPage + 2)

    // 第一页
    if (startPage > 1) {
      const firstBtn = this.createPageButton(1)
      paginationDiv.appendChild(firstBtn)
      
      if (startPage > 2) {
        const ellipsis = document.createElement('span')
        ellipsis.className = 'echosync-pagination-info'
        ellipsis.textContent = '...'
        paginationDiv.appendChild(ellipsis)
      }
    }

    // 中间页码
    for (let i = startPage; i <= endPage; i++) {
      const pageBtn = this.createPageButton(i)
      paginationDiv.appendChild(pageBtn)
    }

    // 最后一页
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        const ellipsis = document.createElement('span')
        ellipsis.className = 'echosync-pagination-info'
        ellipsis.textContent = '...'
        paginationDiv.appendChild(ellipsis)
      }
      
      const lastBtn = this.createPageButton(totalPages)
      paginationDiv.appendChild(lastBtn)
    }

    // 下一页按钮
    const nextBtn = document.createElement('button')
    nextBtn.className = 'echosync-pagination-btn'
    nextBtn.textContent = '›'
    nextBtn.disabled = this.currentPage === totalPages
    nextBtn.addEventListener('click', () => {
      if (this.currentPage < totalPages) {
        this.currentPage++
        this.renderContent()
      }
    })
    paginationDiv.appendChild(nextBtn)

    return paginationDiv
  }

  /**
   * 创建页码按钮
   */
  private createPageButton(pageNum: number): HTMLElement {
    const btn = document.createElement('button')
    btn.className = `echosync-pagination-btn ${this.currentPage === pageNum ? 'active' : ''}`
    btn.textContent = pageNum.toString()
    btn.addEventListener('click', () => {
      this.currentPage = pageNum
      this.renderContent()
    })
    return btn
  }
}
