# 修复Tab数据共享问题 - 任务规划

## 需求分析
当前问题：不同页面（deepseek, kimi等）各自访问IndexedDB，导致数据在Chrome DevTools中不显示在一起。

解决方案：所有IndexedDB的读写操作都集中到Service Worker中进行，其他模块（tab, popup, options）通过消息机制与background通信。

## 任务分解

### 1. 分析当前IndexedDB使用情况 ✅
- [x] 检查所有直接使用IndexedDB的代码位置
- [x] 确认哪些模块直接访问了IndexedDB
- [x] 分析当前的数据流和存储模式

**发现的问题模块：**
- Content Script: `HistoryManager.ts`, `ArchiveButton.ts`
- Popup: `useChatHistory.ts`, `usePlatform.ts`
- 其他: `debug.ts`, `e2e-test.ts`

### 2. 设计Service Worker中心化存储方案 ✅
- [x] 确认Service Worker中已有的IndexedDB操作
- [x] 设计统一的消息处理机制
- [x] 规划数据模型和API接口


### 3. 修改content script中的IndexedDB访问 ✅
- [x] 移除直接IndexedDB访问代码
- [x] 实现通过消息机制与Service Worker通信
- [x] 确保捕获的提示词正确发送到Service Worker
- [x] 修改 `ArchiveButton.ts`, `HistoryManager.ts`, `AIAdapter.ts`

### 4. 修改popup页面中的IndexedDB访问 ✅
- [x] 移除直接IndexedDB访问代码
- [x] 实现通过消息机制与Service Worker通信
- [x] 确保历史记录正确显示
- [x] 修改 `useChatHistory.ts`, `usePlatform.ts`

### 5. 修改其他模块中的IndexedDB访问 ✅
- [x] 修改 `debug.ts` 中的直接IndexedDB访问
- [x] 修改 `e2e-test.ts` 中的直接IndexedDB访问

### 6. 增强Service Worker的消息处理 ✅
- [x] 实现完整的CRUD操作消息处理
- [x] 添加错误处理和响应机制
- [x] 扩展MessageType支持所有数据库操作

### 7. 测试验证 ✅
- [x] 编译测试通过
- [x] 构建测试成功
- [x] 确保功能正常工作

## 实施顺序
1. 先分析当前代码，找出所有直接IndexedDB访问点
2. 设计并实现Service Worker中的统一存储接口
3. 逐个修改各模块，改为消息通信方式
4. 全面测试验证

## 注意事项
- 确保数据模型一致性
- 处理好异步通信和错误情况
- 考虑Service Worker生命周期问题
