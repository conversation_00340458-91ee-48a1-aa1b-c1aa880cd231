import { CommonSelectors } from "./CommonSelectors";
import { PlatformConfig } from "../types/PlatformConfigType";
import { kimiSelector } from "./kimiConfig";
import { PlatformEntity } from "@/common/types/database_entity";

  /**
   * 选择器配置
   */
export interface SelectorConfig {
    inputField: string[];
    sendButton: string[];
    // Kimi 特有的选择器类型
    headerContent?: string[];
    headerValue?: string[];
    chatContentList?: string[];
    chatHeaderContent?: string[];
    promptItem?: string[];   // 提示词父节点
    promptContent?: string[] // 提示词内容
    promptAction?: string[]; // 提示词操作按钮
    answerItem?: string[];
    answerCompletion?: string[];
    copyButton?: string[];
    markdown?: string[]; // markdown内容选择器(即真实的回答)
}


/**
 * 选择器管理器
 */
export default class SelectorManager { 

    // 提供统一的选择器获得入口
    private static selector = null;
    public static getSelector(): SelectorConfig { 
        return SelectorManager.selector;
    }

    private static getPlatformSelectors(platform: PlatformEntity): SelectorConfig { 
        // 获取平台特定的选择器
        switch (platform.name) { 
            case "Kimi":
                return kimiSelector;
            default:
                return CommonSelectors;
        }
    }
    
    // 改造为init
    public static initSelector(platform: PlatformEntity): SelectorConfig {
        // 获取平台特定的选择器
        const platformSelectors = this.getPlatformSelectors(platform);
        // 与common选择器合并
        const common = CommonSelectors;

        // 合并所有选择器类型（包括新增的可选类型）
        const allKeys = new Set([
            ...Object.keys(common),
            ...Object.keys(platformSelectors)
        ]);

        // 平台特定选择器优先，然后是通用选择器
        SelectorManager.selector = Array.from(allKeys).reduce((merged, key) => {
            merged[key] = [
                ...(platformSelectors[key] || []),
                ...(common[key] || [])
            ];
            return merged;
        }, {} as SelectorConfig);
        return SelectorManager.selector;
    }


    // TODO 新增一个refresh
    public static refreshSelector(platform: PlatformConfig): SelectorConfig {
        // 获取平台特定的选择器
        const platformSelectors = this.getPlatformSelectors(platform);
        // 与common选择器合并
        const common = CommonSelectors;

        // 合并所有选择器类型（包括新增的可选类型）
        const allKeys = new Set([
            ...Object.keys(common),
            ...Object.keys(platformSelectors)
        ]);

        // 平台特定选择器优先，然后是通用选择器
        SelectorManager.selector = Array.from(allKeys).reduce((merged, key) => {
            merged[key] = [
                ...(platformSelectors[key] || []),
                ...(common[key] || [])
            ];
            return merged;
        }, {} as SelectorConfig);
        return SelectorManager.selector;
    }
}
