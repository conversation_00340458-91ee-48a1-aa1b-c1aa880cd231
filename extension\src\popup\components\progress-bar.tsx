import React from 'react'
import { cn } from '@/common/utils'

interface ProgressBarProps {
  value: number // 0-100
  showAnimation?: boolean
  className?: string
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  showAnimation = true,
  className
}) => {
  const clampedValue = Math.max(0, Math.min(100, value))

  return (
    <div className={cn('progress-bar', className)}>
      <div
        className="progress-fill"
        style={{ width: `${clampedValue}%` }}
        role="progressbar"
        aria-valuenow={clampedValue}
        aria-valuemin={0}
        aria-valuemax={100}
      />
    </div>
  )
}
