# 技术方案设计

## 架构概述

实现完整的问答流程捕捉系统，包括发送事件捕捉、答案接收检测、数据存储和UI状态更新。

## 技术栈

- **开发语言**: TypeScript 5
- **架构模式**: Singleton模式 + 事件驱动
- **通信方式**: CustomEvent + MessagingService
- **数据存储**: IndexedDB (通过Background)

## 核心组件设计

### 1. AskAnswerCapture 组件

```typescript
export class AskAnswerCapture extends Singleton<AskAnswerCapture> {
  private archiveService: ArchiveService
  private inputModel: InputModel
  private isWaitingForAnswer: boolean = false
  private currentChatUid: string = ''
  
  // 核心方法
  public initializeCapture(): void
  private setupSendButtonListener(): void
  private setupAnswerDetection(): void
  private handleSendEvent(): Promise<void>
  private handleAnswerReceived(answer: string): Promise<void>
}
```

### 2. ArchiveService 扩展

```typescript
// 新增方法
public async archiveAnswer(chatUid: string, answer: string, platform: PlatformEntity): Promise<void>
```

### 3. ChatHistoryDatabaseProxy 扩展

```typescript
// 新增方法
public async createChatHistory(data: CreateChatHistoryInput): Promise<DatabaseResult<boolean>>
```

## 数据流设计

### 1. 发送流程

```mermaid
graph TD
    A[用户点击发送] --> B[AskAnswerCapture捕捉事件]
    B --> C[触发自定义事件]
    C --> D[ArchiveButtonInject显示状态]
    B --> E[调用ArchiveService.archivePrompt]
    E --> F[存储提示词]
    B --> G[开始监听答案]
```

### 2. 答案接收流程

```mermaid
graph TD
    A[AI返回答案] --> B[AskAnswerCapture检测答案]
    B --> C[提取答案内容]
    C --> D[调用ArchiveService.archiveAnswer]
    D --> E[ChatHistoryDatabaseProxy]
    E --> F[Background处理]
    F --> G[ChatHistoryService存储]
```

## 关键技术实现

### 1. 发送按钮检测

```typescript
private setupSendButtonListener(): void {
  // 监听发送按钮点击
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;
    if (this.isSendButton(target)) {
      this.handleSendEvent();
    }
  });
}

private isSendButton(element: HTMLElement): boolean {
  // Kimi平台发送按钮特征检测
  return element.matches('[data-testid="send-button"]') ||
         element.closest('[data-testid="send-button"]') !== null;
}
```

### 2. 答案检测策略

```typescript
private setupAnswerDetection(): void {
  // 使用MutationObserver监听DOM变化
  const observer = new MutationObserver((mutations) => {
    if (this.isWaitingForAnswer) {
      this.checkForNewAnswer(mutations);
    }
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    characterData: true
  });
}

private checkForNewAnswer(mutations: MutationRecord[]): void {
  // 检测新的答案内容
  for (const mutation of mutations) {
    if (mutation.type === 'childList') {
      const newAnswerElement = this.findNewAnswerElement(mutation);
      if (newAnswerElement) {
        this.extractAndArchiveAnswer(newAnswerElement);
      }
    }
  }
}
```

### 3. 事件通信机制

```typescript
private notifyArchiveButton(): void {
  const event = new CustomEvent('echosync:prompt-sent', {
    detail: {
      chatUid: this.currentChatUid,
      timestamp: Date.now()
    }
  });
  document.dispatchEvent(event);
}
```

## 数据模型设计

### 1. ChatHistory 数据结构

```typescript
interface CreateChatHistoryInput {
  chat_uid: string;
  chat_prompt: string;
  chat_answer: string;
  platform_id: number;
  create_time: number;
  update_time: number;
}
```

### 2. 状态管理

```typescript
interface AskAnswerState {
  isWaitingForAnswer: boolean;
  currentChatUid: string;
  sendTimestamp: number;
  answerStartTime?: number;
}
```

## 平台适配策略

### 1. Kimi平台选择器

```typescript
const KIMI_SELECTORS = {
  sendButton: '[data-testid="send-button"]',
  inputArea: '[data-testid="chat-input"]',
  answerContainer: '[data-testid="message-content"]',
  copyButton: '[data-testid="copy-button"]'
};
```

### 2. 通用检测逻辑

```typescript
private detectPlatformElements(): PlatformElements {
  // 根据当前平台返回对应的选择器
  const hostname = window.location.hostname;
  
  switch (hostname) {
    case 'kimi.moonshot.cn':
      return KIMI_SELECTORS;
    default:
      return COMMON_SELECTORS;
  }
}
```

## 错误处理策略

### 1. 捕捉失败处理

```typescript
private async handleSendEvent(): Promise<void> {
  try {
    await this.archiveService.archivePrompt(prompt, platform);
    this.notifyArchiveButton();
  } catch (error) {
    console.error('【AskAnswerCapture】Archive prompt failed:', error);
    // 不阻断用户操作，静默处理错误
  }
}
```

### 2. 答案检测超时

```typescript
private startAnswerTimeout(): void {
  setTimeout(() => {
    if (this.isWaitingForAnswer) {
      console.warn('【AskAnswerCapture】Answer detection timeout');
      this.isWaitingForAnswer = false;
    }
  }, 30000); // 30秒超时
}
```

## 性能优化

### 1. 防抖处理

```typescript
private debouncedAnswerCheck = debounce((mutations: MutationRecord[]) => {
  this.checkForNewAnswer(mutations);
}, 500);
```

### 2. 内存管理

```typescript
public destroy(): void {
  this.observer?.disconnect();
  this.clearTimeouts();
  super.destroy();
}
```

## 测试策略

### 1. 单元测试

- AskAnswerCapture的核心方法测试
- 事件触发和监听测试
- 数据存储流程测试

### 2. 集成测试

- 完整问答流程测试
- 多平台兼容性测试
- 错误场景处理测试

## 部署考虑

### 1. 渐进式启用

- 先在Kimi平台测试
- 验证稳定后扩展到其他平台
- 提供开关控制功能启用

### 2. 监控和日志

- 关键操作日志记录
- 性能指标监控
- 错误率统计
