import React from 'react'
import { cn } from '@/common/utils'

interface StatusIndicatorProps {
  type: 'success' | 'warning' | 'error' | 'info'
  children: React.ReactNode
  className?: string
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  type,
  children,
  className
}) => {
  return (
    <span className={cn('status-indicator', `status-${type}`, className)}>
      {children}
    </span>
  )
}
