import { useState, useEffect } from 'react'

export const useStorage = <T>(key: string, defaultValue: T) => {
  const [value, setValue] = useState<T>(defaultValue)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadValue = async () => {
      try {
        const result = await chrome.storage.sync.get(key)
        setValue(result[key] ?? defaultValue)
      } catch (error) {
        console.error('Storage load error:', error)
        setValue(defaultValue)
      } finally {
        setLoading(false)
      }
    }

    loadValue()
  }, [key, defaultValue])

  const updateValue = async (newValue: T) => {
    try {
      await chrome.storage.sync.set({ [key]: newValue })
      setValue(newValue)
      return true
    } catch (error) {
      console.error('Storage save error:', error)
      return false
    }
  }

  return { value, updateValue, loading }
}