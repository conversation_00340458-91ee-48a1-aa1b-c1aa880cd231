// Content 模块的设置服务适配器 (只读)
// 职责: 根据笔记平台返回当前设备的导出路径
// 注意: Content 模块只读设置,所有写操作在 Popup 模块完成

import { settingsStorage } from '@/common/service/SettingsStorageService'

// 笔记平台类型
export type NotePlatform = 'obsidian' | 'notion' | 'markdown'

/**
 * Content 模块设置服务 (只读)
 * 提供简单的导出路径查询功能
 */
export class ContentSettingsService {
  private static instance: ContentSettingsService

  private constructor() {}

  static getInstance(): ContentSettingsService {
    if (!ContentSettingsService.instance) {
      ContentSettingsService.instance = new ContentSettingsService()
    }
    return ContentSettingsService.instance
  }

  /**
   * 获取指定平台的导出路径
   * @param platform 笔记平台 ('obsidian' | 'notion' | 'markdown')
   * @returns 导出路径,如果未配置则返回 null
   * 
   * @example
   * const path = await contentSettingsService.getExportPath('obsidian')
   * if (path) {
   *   console.log('Obsidian导出路径:', path)
   * }
   */
  async getExportPath(platform: NotePlatform): Promise<string | null> {
    try {
      const deviceConfig = await settingsStorage.getCurrentDeviceConfig(platform)
      return deviceConfig?.path || null
    } catch (error) {
      console.error(`Failed to get export path for ${platform}:`, error)
      return null
    }
  }

  /**
   * 检查指定平台是否已配置导出路径
   * @param platform 笔记平台
   * @returns true 表示已配置, false 表示未配置
   * 
   * @example
   * const isConfigured = await contentSettingsService.isPlatformConfigured('obsidian')
   * if (!isConfigured) {
   *   console.log('请先在设置中配置 Obsidian 导出路径')
   * }
   */
  async isPlatformConfigured(platform: NotePlatform): Promise<boolean> {
    try {
      return await settingsStorage.isPlatformConfigured(platform)
    } catch (error) {
      console.error(`Failed to check if ${platform} is configured:`, error)
      return false
    }
  }

  /**
   * 保存目录信息到设置
   * @param platform 笔记平台
   * @param directoryName 目录名称（会作为 path 保存）
   * @param displayPath 用于显示的路径（可选，如果不提供则使用 directoryName）
   * 
   * @example
   * await contentSettingsService.saveDirectoryInfo('obsidian', 'MyVault')
   */
  async saveDirectoryInfo(
    platform: NotePlatform, 
    path: string
  ): Promise<void> {
    try {
      const deviceConfig = await settingsStorage.getCurrentDeviceConfig(platform)
      
      if (deviceConfig) {
        // 更新现有设备的路径
        await settingsStorage.updateDevicePath(
          platform, 
          deviceConfig.id, 
          path
        )
      } else {
        // 添加新设备
        const deviceInfo = settingsStorage.getCurrentDeviceInfo()
        const newDevice: import('@/common/service/SettingsStorageService').DeviceConfig = {
          id: deviceInfo.currentDeviceId,
          name: deviceInfo.fullName,
          icon: this.getDeviceIcon(deviceInfo.os),
          path: path,
          lastActive: Date.now()
        }
        await settingsStorage.addDevice(platform, newDevice)
      }
    } catch (error) {
      console.error(`Failed to save directory info for ${platform}:`, error)
      throw error
    }
  }

  /**
   * 获取设备图标
   */
  private getDeviceIcon(os: string): string {
    const osLower = os.toLowerCase()
    if (osLower.includes('windows')) return '🖥️'
    if (osLower.includes('mac')) return '💻'
    if (osLower.includes('linux')) return '🐧'
    return '💻'
  }

  /**
   * 获取保存的目录名称
   * @param platform 笔记平台
   * @returns 目录名称（即 path 字段），如果未配置则返回 null
   */
  async getDirectoryName(platform: NotePlatform): Promise<string | null> {
    try {
      const deviceConfig = await settingsStorage.getCurrentDeviceConfig(platform)
      return deviceConfig?.path || null
    } catch (error) {
      console.error(`Failed to get directory name for ${platform}:`, error)
      return null
    }
  }
}

// 导出单例实例
export const contentSettingsService = ContentSettingsService.getInstance()
