# 4.3 官方网站开发指南

## 1. 开发流程

1.  **配置环境变量**: 确保你已经在 `website/` 目录下创建了 `.env.local` 文件，并填入了正确的 Supabase 和 Stripe API 密钥。详情请参考 [4.1 开发环境设置](./4.1_Environment_Setup.md)。

2.  **启动开发服务器**:
    ```bash
    # 在项目根目录运行
    npm run dev:website
    ```
    Next.js 会启动一个热重载的开发服务器，默认监听 `http://localhost:3000`。

3.  **进行开发**:
    -   **页面**: 在 `website/src/app/` 目录下创建或修改页面。Next.js 14 使用基于文件系统的 App Router，目录结构即路由结构。
    -   **组件**: 可复用的 React 组件放在 `website/src/components/` 目录下。
    -   **API路由**: 在 `website/src/app/api/` 目录下创建文件来定义服务器端点。
    -   **后端交互**: 与 Supabase 或 Stripe 的交互逻辑封装在 `website/src/lib/` 中。

## 2. 后端服务与部署

关于网站的后端服务集成（数据库、认证）和部署的详细步骤，我们已经编写了专门的实战指南。

**➡️ 请参考：[4.5 后端与部署实战指南](./4.5_Backend_and_Deployment_Guide.md)**

该指南包含了以下内容的详细说明：

-   **使用 Vercel 进行部署**
-   **集成 Supabase 进行数据库和认证管理**
-   **集成 Stripe 处理支付订阅**

