import './ObsidianExportModal.css';
import { AnswerModel } from '../../model/AnswerModel';
import { TagSelector } from './TagSelector';
import { ObsidianExportService } from '../../service/ObsidianExportService';

/**
 * Obsidian 导出 Modal 组件
 * 显示导出设置界面，收集用户输入
 */
export class ObsidianExportModal {
    private overlay: HTMLElement | null = null;
    private modal: HTMLElement | null = null;
    private answerIndex: number;
    private tagSelector: TagSelector | null = null;
    private titleInput: HTMLInputElement | null = null;

    constructor(answerIndex: number) {
        this.answerIndex = answerIndex;
    }

    /**
     * 创建 Modal 结构
     */
    private createModal(): void {
        // 创建遮罩层
        this.overlay = document.createElement('div');
        this.overlay.className = 'obsidian-modal-overlay';
        this.overlay.addEventListener('click', this.handleOverlayClick.bind(this));

        // 创建 Modal 容器
        this.modal = document.createElement('div');
        this.modal.className = 'obsidian-modal';
        this.modal.addEventListener('click', (e) => e.stopPropagation());

        // 创建 Header
        const header = this.createHeader();
        this.modal.appendChild(header);

        // 创建 Body
        const body = this.createBody();
        this.modal.appendChild(body);

        // 创建 Footer
        const footer = this.createFooter();
        this.modal.appendChild(footer);

        this.overlay.appendChild(this.modal);
    }

    /**
     * 创建 Header
     */
    private createHeader(): HTMLElement {
        const header = document.createElement('div');
        header.className = 'modal-header';

        // Logo 和标题
        const titleContainer = document.createElement('div');
        titleContainer.className = 'header-title';

        // 使用内联 SVG Logo（紫色渐变）
        const logo = document.createElement('div');
        logo.className = 'obsidian-logo';
        logo.innerHTML = `
            <svg viewBox="0 0 100 100" width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="obsidian-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#6366f1;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <path d="M30 10 L70 10 L90 50 L70 90 L30 90 L10 50 Z" 
                      fill="url(#obsidian-gradient)" 
                      stroke="none"/>
                <text x="50" y="65" 
                      font-family="Arial, sans-serif" 
                      font-size="45" 
                      font-weight="bold" 
                      fill="white" 
                      text-anchor="middle">O</text>
            </svg>
        `;

        const title = document.createElement('h3');
        title.textContent = '导出到 Obsidian';

        titleContainer.appendChild(logo);
        titleContainer.appendChild(title);

        // 关闭按钮
        const closeButton = document.createElement('button');
        closeButton.className = 'close-btn';
        closeButton.textContent = '×';
        closeButton.setAttribute('aria-label', '关闭');
        closeButton.addEventListener('click', this.hide.bind(this));

        header.appendChild(titleContainer);
        header.appendChild(closeButton);

        return header;
    }

    /**
     * 创建 Body
     */
    private createBody(): HTMLElement {
        const body = document.createElement('div');
        body.className = 'modal-body';

        // 获取问答数据
        const qaPair = AnswerModel.getInstance().getQAPair(this.answerIndex);
        if (!qaPair) {
            console.error('[ObsidianExportModal] 无法获取问答数据，索引:', this.answerIndex);
            return body;
        }

        // 标题输入
        const titleGroup = this.createFormGroup(
            '标题',
            this.createTitleInput(qaPair.prompt)
        );
        body.appendChild(titleGroup);

        // 正文预览
        const contentGroup = this.createFormGroup(
            '正文',
            this.createContentPreview(qaPair.answer)
        );
        body.appendChild(contentGroup);

        // Tag 选择器
        this.tagSelector = new TagSelector();
        const tagGroup = this.createFormGroup(
            '标签',
            this.tagSelector.render()
        );
        body.appendChild(tagGroup);

        // 元数据
        const metadataGroup = this.createFormGroup(
            '创建时间',
            this.createMetadata()
        );
        body.appendChild(metadataGroup);

        return body;
    }

    /**
     * 创建表单组
     */
    private createFormGroup(label: string, content: HTMLElement): HTMLElement {
        const group = document.createElement('div');
        group.className = 'form-group';

        const labelElement = document.createElement('label');
        labelElement.className = 'form-label';
        labelElement.textContent = label;

        group.appendChild(labelElement);
        group.appendChild(content);

        return group;
    }

    /**
     * 创建标题输入框
     */
    private createTitleInput(defaultTitle: string): HTMLInputElement {
        this.titleInput = document.createElement('input');
        this.titleInput.type = 'text';
        this.titleInput.className = 'form-input';
        this.titleInput.value = defaultTitle;
        this.titleInput.placeholder = '请输入标题';

        return this.titleInput;
    }

    /**
     * 创建内容预览
     */
    private createContentPreview(content: string): HTMLElement {
        const preview = document.createElement('div');
        preview.className = 'content-preview';

        // 限制预览长度
        const maxLength = 200;
        const displayContent = content.length > maxLength 
            ? content.substring(0, maxLength) + '...' 
            : content;

        preview.textContent = displayContent;

        return preview;
    }

    /**
     * 创建元数据显示
     */
    private createMetadata(): HTMLElement {
        const metadata = document.createElement('div');
        metadata.className = 'metadata';

        const now = new Date();
        const formattedTime = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        metadata.textContent = formattedTime;

        return metadata;
    }

    /**
     * 创建 Footer
     */
    private createFooter(): HTMLElement {
        const footer = document.createElement('div');
        footer.className = 'modal-footer';

        const exportButton = document.createElement('button');
        exportButton.className = 'export-btn';
        exportButton.textContent = 'Add to Obsidian';
        exportButton.addEventListener('click', this.handleExport.bind(this));

        footer.appendChild(exportButton);

        return footer;
    }

    /**
     * 处理遮罩层点击
     */
    private handleOverlayClick(event: MouseEvent): void {
        if (event.target === this.overlay) {
            this.hide();
        }
    }

    /**
     * 处理导出
     */
    private async handleExport(): Promise<void> {
        try {
            // 获取表单数据
            const title = this.titleInput?.value.trim() || '';
            const tags = this.tagSelector?.getTags() || [];

            if (!title) {
                this.showToast('标题不能为空', 'warning');
                return;
            }

            // 禁用按钮，防止重复点击
            const exportButton = this.modal?.querySelector('.export-btn') as HTMLButtonElement;
            if (exportButton) {
                exportButton.disabled = true;
                exportButton.textContent = '导出中...';
            }

            // 调用导出服务
            await ObsidianExportService.getInstance().exportToObsidian({
                answerIndex: this.answerIndex,
                title,
                tags
            });

            // 成功提示
            this.showToast('✅ 导出成功！', 'success');

            // 延迟关闭 Modal
            setTimeout(() => {
                this.hide();
            }, 1000);

        } catch (error) {
            console.error('[ObsidianExportModal] 导出失败', error);
            
            // 恢复按钮状态
            const exportButton = this.modal?.querySelector('.export-btn') as HTMLButtonElement;
            if (exportButton) {
                exportButton.disabled = false;
                exportButton.textContent = 'Add to Obsidian';
            }
        }
    }

    /**
     * 显示 Toast 提示
     */
    private showToast(message: string, type: 'success' | 'warning' | 'error'): void {
        // 简单的 Toast 实现
        const toast = document.createElement('div');
        toast.className = `obsidian-toast obsidian-toast-${type}`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // 动画显示
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // 3秒后自动关闭
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 3000);
    }

    /**
     * 显示 Modal
     */
    public show(): void {
        if (!this.overlay) {
            this.createModal();
        }

        if (this.overlay) {
            document.body.appendChild(this.overlay);

            // 添加动画
            requestAnimationFrame(() => {
                this.overlay?.classList.add('show');
            });

            // 绑定 ESC 键
            document.addEventListener('keydown', this.handleKeyDown.bind(this));

            // 聚焦标题输入框
            setTimeout(() => {
                this.titleInput?.focus();
                this.titleInput?.select();
            }, 100);
        }
    }

    /**
     * 隐藏 Modal
     */
    public hide(): void {
        if (this.overlay) {
            this.overlay.classList.remove('show');

            setTimeout(() => {
                this.overlay?.remove();
                this.overlay = null;
                this.modal = null;
                this.titleInput = null;

                // 销毁 TagSelector
                if (this.tagSelector) {
                    this.tagSelector.destroy();
                    this.tagSelector = null;
                }

                // 移除 ESC 键监听
                document.removeEventListener('keydown', this.handleKeyDown);
            }, 300);
        }
    }

    /**
     * 处理键盘事件
     */
    private handleKeyDown = (event: KeyboardEvent): void => {
        if (event.key === 'Escape') {
            this.hide();
        }
    };
}
