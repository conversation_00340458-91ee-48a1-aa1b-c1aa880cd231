/**
 * 错误类型枚举
 */
export enum ErrorType {
    DOM_SELECTOR_FAILED = 'DOM_SELECTOR_FAILED',
    CLIPBOARD_ACCESS_FAILED = 'CLIPBOARD_ACCESS_FAILED',
    OBSERVER_INIT_FAILED = 'OBSERVER_INIT_FAILED',
    DATA_SAVE_FAILED = 'DATA_SAVE_FAILED',
    PAGE_TRANSITION_FAILED = 'PAGE_TRANSITION_FAILED',
    QUESTION_CAPTURE_FAILED = 'QUESTION_CAPTURE_FAILED',
    ANSWER_CAPTURE_FAILED = 'ANSWER_CAPTURE_FAILED'
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
    /**
     * 处理错误
     * @param errorType 错误类型
     * @param error 错误对象
     * @param context 上下文信息
     */
    public static handle(errorType: ErrorType, error: any, context?: any): void {
        const timestamp = new Date().toISOString();
        const contextStr = context ? ` | Context: ${JSON.stringify(context)}` : '';
        
        switch (errorType) {
            case ErrorType.DOM_SELECTOR_FAILED:
                console.warn(`[${timestamp}] [KimiAnswerService] DOM选择器失效: ${error}${contextStr}`);
                // 尝试备用选择器
                break;
            case ErrorType.CLIPBOARD_ACCESS_FAILED:
                console.error(`[${timestamp}] [KimiAnswerService] 剪贴板访问失败: ${error}${contextStr}`);
                // 降级到其他内容提取方案
                break;
            case ErrorType.OBSERVER_INIT_FAILED:
                console.error(`[${timestamp}] [KimiAnswerService] 监听器初始化失败: ${error}${contextStr}`);
                // 尝试重新初始化
                break;
            case ErrorType.DATA_SAVE_FAILED:
                console.error(`[${timestamp}] [KimiAnswerService] 数据保存失败: ${error}${contextStr}`);
                // 缓存到本地，稍后重试
                break;
            case ErrorType.PAGE_TRANSITION_FAILED:
                console.warn(`[${timestamp}] [KimiAnswerService] 页面跳转处理失败: ${error}${contextStr}`);
                break;
            case ErrorType.QUESTION_CAPTURE_FAILED:
                console.warn(`[${timestamp}] [KimiAnswerService] 问题捕获失败: ${error}${contextStr}`);
                break;
            case ErrorType.ANSWER_CAPTURE_FAILED:
                console.warn(`[${timestamp}] [KimiAnswerService] 答案捕获失败: ${error}${contextStr}`);
                break;
            default:
                console.error(`[${timestamp}] [KimiAnswerService] 未知错误类型: ${errorType} | ${error}${contextStr}`);
        }
    }
    
    /**
     * 判断错误是否可重试
     */
    public static isRetryableError(errorType: ErrorType): boolean {
        const retryableErrors = [
            ErrorType.DOM_SELECTOR_FAILED,
            ErrorType.CLIPBOARD_ACCESS_FAILED,
            ErrorType.OBSERVER_INIT_FAILED,
            ErrorType.DATA_SAVE_FAILED
        ];
        return retryableErrors.includes(errorType);
    }
}