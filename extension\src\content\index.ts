import { ContentScriptManager } from '@/content/adapters/core/ContentScriptManager'
import { DOMUtils } from '@/content/utils/DOMUtils'
console.log('【EchoSync】index EchoSync Content Script loaded on:', window.location.href)
console.log('【EchoSync】index Current hostname:', window.location.hostname)
console.log('【EchoSync】index User agent:', navigator.userAgent)


// 全局管理器实例
let globalManager: ContentScriptManager | null = null

/**
 * 强制清理所有气泡相关元素
 */
function forceCleanupAllBubbles(): void {
  console.log('【EchoSync】Force cleaning up all bubble elements before reinitializing...')

  const bubbleSelectors = [
    '#echosync-floating-bubble',
    '[id*="echosync-floating-bubble"]',
    '[class*="floating-bubble"]',
    '[class*="bubble-content"]'
  ]

  let cleanedCount = 0
  bubbleSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector)
    elements.forEach(element => {
      if (element.parentNode) {
        element.remove()
        cleanedCount++
      }
    })
  })

  if (cleanedCount > 0) {
    console.log(`【EchoSync】Cleaned up ${cleanedCount} bubble elements before reinitializing`)
  }
}

/**
 * 初始化或重新初始化管理器
 */
async function initializeManager(): Promise<void> {
  // 销毁现有管理器前，强制清理气泡元素
  if (globalManager) {
    // 强制清理所有气泡元素
    forceCleanupAllBubbles()

    globalManager.destroy()
    globalManager = null
  }

  // 等待一帧确保清理完成
  await new Promise(resolve => requestAnimationFrame(resolve))

  // 等待页面完全加载
  await DOMUtils.waitForPageLoad()

  // 创建新的管理器
  globalManager = new ContentScriptManager()

  // 初始化管理器（等待DOM元素可用）
  await globalManager.initialize()
}

// 初始化管理器
initializeManager().catch(error => {
  console.error('【EchoSync】Failed to initialize manager:', error)
})

// 处理SPA路由变化
let lastUrl = location.href
const urlObserver = new MutationObserver(() => {
  const url = location.href
  if (url !== lastUrl) {
    lastUrl = url
    console.log('【EchoSync】URL changed, reinitializing...', url)

    // 使用waitForPageLoad等待页面渲染完成后重新初始化
    initializeManager().catch(error => {
      console.error('【EchoSync】Failed to reinitialize manager after URL change:', error)
    })
  }
})

// 
urlObserver.observe(document, { subtree: true, childList: true })

// 整页刷新或关闭标签页时彻底清理：
window.addEventListener('beforeunload', () => {
  if (globalManager) {
    globalManager.destroy()
    globalManager = null
  }
  urlObserver.disconnect()
})
