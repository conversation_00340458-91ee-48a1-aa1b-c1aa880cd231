import { detectCurrentPlatform } from "@/content/capture/PlatformDetector";
import { ChatGPTAdapter } from "@/content/adapters/chatgpt";
import { DeepSeekAdapter } from "@/content/adapters/deepseek";
import { ClaudeAdapter } from "@/content/adapters/claude";
import { GeminiAdapter } from "@/content/adapters/gemini";
import { KimiAdapter } from "@/content/adapters/kimi/kimi";
import { BaseAIAdapter } from "@/content/adapters/core/BaseAIAdapter";
import { platformDatabaseProxy } from "@/common/service/PlatformDatabaseProxy";
import { checkAndUpdateFavicon } from "@/content/capture/FaviconCapture";
import type {
  PlatformEntity,
} from "@/common/types/database_entity";
import { PlatformConfig } from "../types/PlatformConfigType";

/**
 * Content Script 管理器
 * 重构后以 BaseAIAdapter 为核心应用上下文，统一管理组件间交互
 */
export class ContentScriptManager {
  private adapter: BaseAIAdapter | null = null;
  private isInitialized: boolean = false;

  constructor() {
    // 不在构造函数中立即初始化，而是等待外部调用
    console.log('【EchoSync】ContentScriptManager created, waiting for initialization...');
  }

  /**
   * 公共初始化方法，确保页面元素加载完成后再初始化
   */
  async initialize(): Promise<void> {
    console.log('【EchoSync】Starting ContentScriptManager initialization...');
    await this.init();
  }

  /**
   * 初始化管理器
   * 重构后以 BaseAIAdapter 为核心应用上下文
   */
  private async init() {
    if (this.isInitialized) return;

    try {
      // 1. 检测平台
      const platformConfig: PlatformConfig = detectCurrentPlatform();
      // 2. 加载平台信息
      const platformEntity: PlatformEntity = await this.loadPlatformEntity(platformConfig);

      if (platformConfig) {
        // 2. 创建适配器
        this.adapter = this.createAdapter(platformEntity);

        if (this.adapter) {
          // 3. 初始化适配器（等待DOM元素可用）
          await this.adapter.initialize();

          
          this.isInitialized = true;
          console.log("【EchoSync】Initialized for:", platformConfig.name);
        }
      }
    } catch (error) {
      console.error("【EchoSync】Init error:", error);
    }
  }

  /**
   * 创建适配器实例作为核心应用上下文
   */
  private createAdapter(platform: PlatformEntity): BaseAIAdapter | null {
    const adapters = {
      ChatGPT: () => new ChatGPTAdapter(platform),
      DeepSeek: () => new DeepSeekAdapter(platform),
      Claude: () => new ClaudeAdapter(platform),
      Gemini: () => new GeminiAdapter(platform),
      Kimi: () => new KimiAdapter(platform),
    };

    return adapters[platform.name]?.() || null;
  }

  /**
   * 加载平台信息 比如favicon
   */
  private async loadPlatformEntity(platformConfig: PlatformConfig): Promise<PlatformEntity> {
   
    try {
      const platformsResult = await platformDatabaseProxy.getAll();

      if (platformsResult.success) {
        
        // 找到当前平台
        const currentPlatform: PlatformEntity =
          platformsResult.data.find((p: any) => {
            const nameMatch = p.name === platformConfig.name;
            const urlMatch = window.location.href.includes(
              p.url.replace("https://", "").replace("http://", "")
            );
            return nameMatch || urlMatch;
          }) || null;

        if (currentPlatform?.id) {
          // 更新平台favicon
          checkAndUpdateFavicon(currentPlatform).catch(console.error);
        }

        
        console.log(
          "【EchoSync】Platform info loaded via MessagingService:",
          currentPlatform
        );

        return currentPlatform;
      } else {
        console.warn(
          "【EchoSync】Failed to load platform info:",
          platformsResult.error
        );
      }
    } catch (error) {
      console.error("【EchoSync】Error loading platform info:", error);
    }
  }

  /**
   * 销毁管理器
   * 清理核心应用上下文和所有依赖组件
   */
  destroy(): void {
    if (this.adapter) {
      this.adapter.destroy();
      this.adapter = null;
      console.log(
        "【EchoSync】Core application context (BaseAIAdapter) destroyed"
      );
    }

    // 清理其他组件
    this.isInitialized = false;
    console.log("【EchoSync】ContentScriptManager destroyed completely");
  }
}
