import { PageState } from "../../types/KimiTypes";

/**
 * 页面状态管理服务
 */
export class KimiPageService {
    
    /**
     * 基于URL判断页面类型
     */
    public static getPageTypeFromUrl(): { isHome: boolean; isChat: boolean; chatId?: string } {
        const url = window.location.href;
        
        // Home页模式: https://www.kimi.com 或 https://www.kimi.com/
        const isHome = /^https:\/\/www\.kimi\.com\/?$/i.test(url);
        
        // Chat页模式: https://www.kimi.com/chat/{chat_id}
        const chatMatch = url.match(/^https:\/\/www\.kimi\.com\/chat\/([a-zA-Z0-9]+)$/i);
        const isChat = !!chatMatch;
        const chatId = chatMatch?.[1];
        
        return {
            isHome,
            isChat,
            chatId
        };
    }
    /**
     * 检测欢迎页面
     * 基于URL优先，降级使用DOM检测
     */
    public static isWelcomePage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isHome) {
            return true;
        }
        
        // 降级使用DOM检测
        return document.querySelector('.home-page') !== null;
    }
    
    /**
     * 检测聊天页面
     * 基于URL优先，降级使用DOM检测
     */
    public static isChatPage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isChat) {
            return true;
        }
        
        // 降级使用DOM检测
        return document.querySelector('.chat-page.chat') !== null;
    }
    
    /**
     * 获取当前页面状态
     */
    public static getCurrentPageState(): PageState {
        const urlResult = this.getPageTypeFromUrl();
        
        return {
            isWelcomePage: urlResult.isHome || this.isWelcomePage(),
            isChatPage: urlResult.isChat || this.isChatPage(),
            hasTransitioned: false,
            chatId: urlResult.chatId
        };
    }

    /**
     * 获取当前聊天ID（仅在聊天页有效）
     */
    public static getCurrentChatId(): string | null {
        const urlResult = this.getPageTypeFromUrl();
        return urlResult.chatId || null;
    }
}