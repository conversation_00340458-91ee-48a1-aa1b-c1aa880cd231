# Obsidian 导出功能 - File System Access API 实现

## 更新时间
2025-10-06

## 实现方案

### 技术选型
使用 **File System Access API** 替代 Chrome Downloads API

### 优势
- ✅ 用户可以选择任意目录
- ✅ 授权后持久化访问（无需每次选择）
- ✅ 直接写入文件系统（不依赖浏览器下载目录）
- ✅ 支持子目录和复杂路径
- ✅ 在 Content Script 环境中运行（有 DOM 访问）

## 实现架构

### 核心组件

#### 1. FileSystemService
**文件：** `src/content/service/FileSystemService.ts`

**职责：**
- 管理目录选择和权限
- 持久化 DirectoryHandle 到 IndexedDB
- 提供文件写入接口

**关键方法：**
```typescript
class FileSystemService {
  // 请求用户选择目录
  async requestDirectoryAccess(): Promise<FileSystemDirectoryHandle>
  
  // 获取目录句柄（自动处理权限）
  async getDirectoryHandle(forceRequest?: boolean): Promise<FileSystemDirectoryHandle>
  
  // 写入文件
  async writeFile(filename: string, content: string): Promise<void>
  
  // 检查是否有权限
  async hasDirectoryAccess(): Promise<boolean>
  
  // 清除保存的句柄
  async clearDirectoryHandle(): Promise<void>
}
```

#### 2. ObsidianExportService
**修改：** 简化了文件保存逻辑

**修改前：**
```typescript
// 通过 Background Script 和 Chrome Downloads API
await saveFileProxy.saveFileToPath(content, filename, exportPath)
```

**修改后：**
```typescript
// 直接使用 File System Access API
await fileSystemService.writeFile(filename, content)
```

### 数据持久化

#### IndexedDB 结构
```typescript
数据库名: 'obsidian_export_db'
版本: 1
Store名: 'directory_handles'
Key: 'obsidian_directory'
Value: FileSystemDirectoryHandle (可序列化对象)
```

**好处：**
- DirectoryHandle 可以直接存储到 IndexedDB
- 浏览器会维护权限状态
- 页面刷新后仍然有效

## 用户体验流程

### 首次使用

1. **用户点击导出按钮**
   ```
   用户操作 → 点击 Obsidian 导出按钮
   ```

2. **系统检查权限**
   ```typescript
   const hasAccess = await fileSystemService.hasDirectoryAccess()
   // 首次使用：false
   ```

3. **弹出目录选择器**
   ```typescript
   const handle = await window.showDirectoryPicker({
     mode: 'readwrite',
     startIn: 'documents'
   })
   ```
   
   ![目录选择器](https://user-images.githubusercontent.com/example.png)
   
   用户操作：
   - 浏览文件系统
   - 选择 Obsidian vault 目录（如 `D:\Obsidian\MyVault`）
   - 点击"选择文件夹"

4. **授予权限**
   ```
   浏览器提示：
   "允许 echoAIExtention 查看和修改 MyVault 中的文件?"
   [拒绝] [允许]
   ```
   
   用户点击"允许"

5. **保存句柄**
   ```typescript
   await saveDirectoryHandle(handle)
   // 保存到 IndexedDB
   ```

6. **写入文件**
   ```typescript
   await fileSystemService.writeFile('问题标题.md', markdownContent)
   // 文件保存到: D:\Obsidian\MyVault\问题标题.md
   ```

7. **完成提示**
   ```
   ✅ "导出成功"
   ```

### 后续使用

1. **用户点击导出按钮**
   
2. **自动加载权限**
   ```typescript
   const savedHandle = await loadDirectoryHandle()
   // 从 IndexedDB 加载
   
   const permission = await savedHandle.queryPermission({ mode: 'readwrite' })
   // permission === 'granted' ✅
   ```

3. **直接写入文件**
   ```typescript
   await fileSystemService.writeFile('新问题.md', markdownContent)
   ```

4. **完成**
   ```
   无需再次选择目录！
   ```

### 权限丢失处理

**场景：** 用户在浏览器设置中撤销了权限

```typescript
const permission = await savedHandle.queryPermission({ mode: 'readwrite' })
// permission === 'denied'

// 尝试重新请求
const newPermission = await savedHandle.requestPermission({ mode: 'readwrite' })

if (newPermission === 'granted') {
  // 权限恢复
} else {
  // 需要重新选择目录
  await requestDirectoryAccess()
}
```

## API 说明

### File System Access API

#### window.showDirectoryPicker()
```typescript
const handle = await window.showDirectoryPicker({
  mode: 'readwrite',      // 读写权限
  startIn: 'documents'    // 起始位置：文档文件夹
})
```

**返回：** `FileSystemDirectoryHandle`

#### FileSystemDirectoryHandle
```typescript
// 获取文件句柄
const fileHandle = await dirHandle.getFileHandle('test.md', { 
  create: true  // 如果不存在则创建
})

// 查询权限状态
const permission = await dirHandle.queryPermission({ mode: 'readwrite' })
// 返回: 'granted' | 'denied' | 'prompt'

// 请求权限
const newPermission = await dirHandle.requestPermission({ mode: 'readwrite' })
```

#### FileSystemFileHandle
```typescript
// 创建可写流
const writable = await fileHandle.createWritable()

// 写入内容
await writable.write(content)

// 关闭流（必须！）
await writable.close()
```

## 日志输出

### 首次使用
```
[FileSystemService] 请求用户选择目录
[FileSystemService] 用户已选择目录: MyVault
[FileSystemService] 目录句柄已保存
[FileSystemService] 写入文件: 问题标题.md
[FileSystemService] 文件写入成功: 问题标题.md
[ObsidianExportService] 文件保存成功 {filename: "问题标题.md"}
```

### 后续使用
```
[FileSystemService] 目录句柄已加载
[FileSystemService] 使用已保存的目录句柄
[FileSystemService] 写入文件: 新问题.md
[FileSystemService] 文件写入成功: 新问题.md
[ObsidianExportService] 文件保存成功 {filename: "新问题.md"}
```

### 权限问题
```
[FileSystemService] 缓存的句柄无效
[FileSystemService] 权限被撤销，尝试重新请求
[FileSystemService] 权限重新授予
```

## 浏览器兼容性

### 支持情况
| 浏览器 | 版本 | 支持状态 |
|--------|------|----------|
| Chrome | 86+ | ✅ 完全支持 |
| Edge | 86+ | ✅ 完全支持 |
| Opera | 72+ | ✅ 完全支持 |
| Firefox | ❌ | 不支持（实验性） |
| Safari | ❌ | 不支持 |

**注意：** 此实现仅适用于基于 Chromium 的浏览器。

### 功能检测
```typescript
if (!('showDirectoryPicker' in window)) {
  console.error('浏览器不支持 File System Access API')
  // 降级到其他方案
}
```

## 安全性

### 用户控制
- ✅ 需要用户主动选择目录
- ✅ 需要用户明确授予权限
- ✅ 用户可以随时撤销权限
- ✅ 只能访问用户授权的目录

### 权限范围
```typescript
// 只能访问选择的目录及其子目录
dirHandle.getFileHandle('file.md')           // ✅ 允许
dirHandle.getFileHandle('sub/file.md')       // ✅ 允许
dirHandle.getFileHandle('../outside.md')     // ❌ 禁止
```

### 最佳实践
1. 始终检查权限状态
2. 友好处理用户拒绝
3. 不要滥用权限
4. 清晰告知用户数据如何使用

## 故障排查

### 问题1：弹不出目录选择器

**原因：**
- 不在用户交互上下文中调用
- 浏览器不支持 API

**解决：**
```typescript
// ❌ 错误：在异步回调中调用
setTimeout(() => {
  window.showDirectoryPicker()  // 失败！
}, 1000)

// ✅ 正确：在用户点击事件中调用
button.addEventListener('click', async () => {
  await window.showDirectoryPicker()  // 成功！
})
```

### 问题2：权限总是失效

**原因：**
- 浏览器清除了站点数据
- 用户手动撤销权限

**解决：**
- 实现权限检查和重新请求流程
- 提供"重新选择目录"选项

### 问题3：文件写入失败

**原因：**
- 文件名包含非法字符
- 磁盘空间不足
- 权限被撤销

**解决：**
- 过滤文件名中的特殊字符
- 捕获异常并提示用户
- 检查权限状态

## 对比：两种方案

| 特性 | Chrome Downloads API | File System Access API |
|------|---------------------|----------------------|
| 路径限制 | ✅ 相对路径（下载目录） | ✅ 任意目录 |
| 权限持久化 | ❌ 无 | ✅ 有（IndexedDB） |
| 用户体验 | ⚠️ 需配置下载目录 | ✅ 首次选择后无感 |
| 实现复杂度 | ⚠️ 需Background通信 | ✅ 直接在Content Script |
| 浏览器兼容 | ✅ 广泛支持 | ⚠️ 仅Chromium |
| 安全性 | ✅ 沙箱隔离 | ✅ 用户授权 |

## 推荐使用场景

### File System Access API（当前方案）
- ✅ Chrome/Edge 扩展
- ✅ 需要频繁导出
- ✅ 用户有固定的 Obsidian vault
- ✅ 追求无感体验

### Chrome Downloads API（之前方案）
- ✅ 需要跨浏览器兼容
- ✅ 临时导出需求
- ✅ 不确定用户是否会持续使用

## 总结

### 核心改进
1. **用户体验**：首次授权后完全无感
2. **实现简化**：不需要 Background Script 通信
3. **灵活性**：支持任意目录，不受浏览器限制

### 使用流程
```
首次：
点击导出 → 选择目录 → 授予权限 → 保存文件 ✅

后续：
点击导出 → 保存文件 ✅ （无需再选择！）
```

### 最佳实践
- 在用户点击事件中调用 API
- 实现完善的错误处理
- 提供重新选择目录的选项
- 清晰的日志输出便于调试
