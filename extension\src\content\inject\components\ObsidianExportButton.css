/* Obsidian 导出按钮样式 */

/* CSS 变量定义 */
:root {
  --obsidian-primary: #3B82F6;
  --obsidian-secondary: #8B5CF6;
  --obsidian-text: #1F2937;
  --obsidian-text-light: #6B7280;
  --obsidian-border: #E5E7EB;
  --obsidian-bg: #FFFFFF;
  --obsidian-bg-hover: #F3F4F6;
  
  --obsidian-spacing-xs: 4px;
  --obsidian-spacing-sm: 8px;
  --obsidian-spacing-md: 16px;
  
  --obsidian-radius-sm: 4px;
  
  --obsidian-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 按钮基础样式 */
.obsidian-export-btn {
  /* 布局 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  /* 尺寸 */
  width: 32px;
  height: 32px;
  padding: 0;
  
  /* 外观 */
  background: transparent;
  border: none;
  border-radius: var(--obsidian-radius-sm);
  color: var(--obsidian-text-light);
  
  /* 交互 */
  cursor: pointer;
  transition: all 0.2s ease;
  
  /* 重置 */
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  
  /* 闪烁动画 */
  animation: obsidian-pulse 2s ease-in-out infinite;
}

/* 闪烁动画关键帧 */
@keyframes obsidian-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* 悬浮状态 - 停止闪烁 */
.obsidian-export-btn:hover {
  background: var(--obsidian-bg-hover);
  color: var(--obsidian-primary);
  box-shadow: var(--obsidian-shadow-sm);
  animation: none; /* 悬浮时停止闪烁 */
}

/* 激活状态 */
.obsidian-export-btn:active {
  transform: scale(0.95);
}

/* 禁用状态 */
.obsidian-export-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* SVG 图标样式 */
.obsidian-export-btn svg {
  display: block;
  width: 20px;
  height: 20px;
  pointer-events: none;
}

/* 自定义 Tooltip 容器 */
.obsidian-export-btn {
  position: relative;
}

/* 自定义 Tooltip 提示 */
.obsidian-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  font-size: 12px;
  line-height: 1.4;
  white-space: nowrap;
  border-radius: 4px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.15s ease;
  z-index: 10000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Tooltip 小箭头 */
.obsidian-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}

/* 悬停时显示 Tooltip */
.obsidian-export-btn:hover .obsidian-tooltip {
  opacity: 1;
}

/* Tooltip 提示（由 title 属性自动显示） */
.obsidian-export-btn[title] {
  position: relative;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .obsidian-export-btn {
    width: 28px;
    height: 28px;
  }
  
  .obsidian-export-btn svg {
    width: 18px;
    height: 18px;
  }
}
