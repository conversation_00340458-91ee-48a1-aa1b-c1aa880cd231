# EchoAI 插件架构设计文档 (详细版)

本文档详细阐述了 EchoAI Chrome 插件的现代架构设计，旨在实现高度的模块化、可维护性和可扩展性。

## 一、核心设计原则

- **模块化 (Modularity)**: 插件的每个部分都被分解为职责明确的独立模块。
- **分层架构 (Layered Architecture)**: 严格分离业务逻辑、数据访问和用户界面，确保各层独立。
- **消息驱动 (Message-Driven)**: 各模块之间通过标准化的消息系统进行异步通信，实现完全解耦。
- **设计模式驱动**: 在关键模块（如`content`）中，严格遵循既定的设计模式（如中介者模式、模板方法模式）进行开发。

---

## 二、项目目录结构

`extension/src` 目录是插件的核心代码库，其完整结构如下：

```
extension/
├── dist/                           # 构建输出目录
│   ├── assets/                     # 静态资源文件
│   ├── icons/                      # 图标文件
│   ├── src/                        # 编译后的源码
│   ├── vendor/                     # 第三方库文件
│   ├── manifest.json               # 插件清单文件
│   └── service-worker-loader.js    # Service Worker加载器
├── public/                         # 公共静态资源
│   ├── icons/                      # 插件图标文件
│   ├── manifest.json               # 开发环境清单文件
│   └── manifest.md                 # 清单文件说明
├── src/                           # 源代码目录
│   ├── background/                # 后台服务模块 (插件的大脑)
│   │   ├── index.ts               # 主入口文件 - 简洁的协调器
│   │   ├── messageHandler.ts      # 消息处理模块 - 处理所有消息类型
│   │   ├── eventListeners.ts      # 事件监听模块 - 处理Chrome扩展事件
│   │   ├── databaseConnection.ts  # 数据库连接管理
│   │   ├── keepAlive.ts           # 保活管理
│   │   ├── healthMonitor.ts       # 健康监控
│   │   └── README.md              # Background模块架构文档
│   ├── common/                    # 核心服务与业务逻辑 (插件的心脏)
│   │   ├── dao/                   # 数据访问对象层
│   │   │   ├── ChatHistoryDao.ts  # 聊天历史数据访问
│   │   │   ├── ChatPromptDao.ts   # 聊天提示词数据访问
│   │   │   └── PlatformDao.ts     # 平台配置数据访问
│   │   ├── database/              # 数据库层
│   │   │   └── dexie.ts           # Dexie数据库配置和初始化
│   │   ├── service/               # 业务服务层
│   │   │   ├── ChatHistoryService.ts  # 聊天历史业务逻辑
│   │   │   ├── ChatPromptService.ts   # 聊天提示词业务逻辑
│   │   │   ├── PlatformService.ts     # 平台管理业务逻辑
│   │   │   ├── DatabaseProxy.ts       # 数据库代理服务
│   │   │   ├── MessagingService.ts    # 消息通信服务
│   │   │   └── storage.ts             # 存储服务
│   │   ├── types/                 # 类型定义
│   │   │   ├── database_entity.ts # 数据库实体类型
│   │   │   ├── enums.ts           # 枚举类型定义
│   │   │   └── index.ts           # 类型导出入口
│   │   └── utils.ts               # 通用工具函数
│   ├── content/                   # 内容脚本模块 (与网页交互)
│   │   ├── adapters/              # 平台适配器
│   │   │   ├── BaseAIAdapter.ts   # 基础适配器 - 中介者模式核心
│   │   │   ├── chatgpt.ts         # ChatGPT平台适配器
│   │   │   ├── claude.ts          # Claude平台适配器
│   │   │   ├── deepseek.ts        # DeepSeek平台适配器
│   │   │   ├── gemini.ts          # Gemini平台适配器
│   │   │   └── kimi.ts            # Kimi平台适配器
│   │   ├── capture/               # 页面捕捉模块
│   │   │   ├── BaseCapture.ts     # 基础捕捉类
│   │   │   ├── FaviconCapture.ts  # 网站图标捕捉
│   │   │   ├── InputCapture.ts    # 输入框监听捕捉
│   │   │   └── PlatformDetector.ts # 平台检测器
│   │   ├── components/            # 内容脚本UI组件
│   │   │   ├── ArchiveButton.ts   # 存档按钮组件
│   │   │   ├── FloatingBubble.ts  # 浮动气泡组件
│   │   │   └── HistoryBubble.ts   # 历史记录气泡组件
│   │   ├── configs/               # 配置文件
│   │   │   ├── CommonSelectors.ts # 通用选择器配置
│   │   │   ├── Consts.ts          # 常量定义
│   │   │   └── DOMEnum.ts         # DOM相关枚举
│   │   ├── inject/                # UI注入模块
│   │   │   ├── ArchiveButtonInject.ts    # 存档按钮注入器
│   │   │   ├── FloatingBubbleInject.ts   # 浮动气泡注入器
│   │   │   ├── HistoryBubbleIService.ts    # 历史气泡注入器
│   │   │   └── FLoatingBubbleDrag.ts     # 浮动气泡拖拽功能
│   │   ├── types/                 # 内容脚本类型定义
│   │   │   └── PlatformConfigType.ts     # 平台配置类型
│   │   ├── utils/                 # 内容脚本工具
│   │   │   ├── DOMUtils.ts        # DOM操作工具
│   │   │   └── PerformanceOptimizer.ts   # 性能优化工具
│   │   ├── ContentScriptManager.ts       # 内容脚本管理器
│   │   └── index.ts               # 内容脚本入口
│   ├── components/                # 可复用的UI组件 (基于React)
│   │   ├── ui/                    # UI基础组件
│   │   ├── PlatformIcon.ts        # 平台图标组件
│   │   └── Toast.ts               # 提示消息组件
│   ├── popup/                     # 弹出窗口UI (React应用)
│   │   ├── components/            # 弹窗专用组件
│   │   │   └── Header.tsx         # 弹窗头部组件
│   │   ├── pages/                 # 弹窗页面
│   │   │   └── HomePage.tsx       # 主页面
│   │   ├── App.tsx                # 弹窗应用主组件
│   │   ├── main.tsx               # 弹窗应用入口
│   │   └── index.html             # 弹窗HTML模板
│   ├── options/                   # 选项页面UI (React应用)
│   │   ├── OptionsApp.tsx         # 选项页面主组件
│   │   ├── main.tsx               # 选项页面入口
│   │   └── index.html             # 选项页面HTML模板
│   ├── stores/                    # 状态管理
│   │   └── app-store.ts           # 应用状态存储
│   ├── styles/                    # 样式文件
│   │   └── globals.css            # 全局样式
│   └── setupTests.ts              # 测试配置文件
├── jest.config.js                 # Jest测试配置
├── package.json                   # 项目依赖配置
├── postcss.config.js              # PostCSS配置
├── tailwind.config.js             # Tailwind CSS配置
├── tsconfig.json                  # TypeScript配置
├── tsconfig.node.json             # Node.js TypeScript配置
└── vite.config.ts                 # Vite构建配置
```

### Background模块详细说明

`background/` 目录采用模块化架构设计，遵循单一职责原则，实现了从512行代码重构为18行主入口的优雅设计：

#### 核心设计原则
- **单一职责原则 (SRP)**: 每个模块职责明确，易于维护和测试
- **优雅简洁**: 主入口文件仅18行代码，清晰明了
- **可读性优先**: 清晰的模块命名和方法命名，详细的注释和文档

#### 模块功能说明

1. **`index.ts` - 主入口协调器**
   - **职责**: 仅负责初始化和协调各模块，不包含具体业务逻辑
   - **功能**: 初始化事件监听器、设置消息处理器、输出启动日志
   - **设计**: 简洁的协调器模式，代码量控制在20行以内

2. **`messageHandler.ts` - 消息处理中心**
   - **职责**: 处理所有类型的Chrome扩展消息
   - **功能**:
     - 数据库操作消息 (CRUD)
     - 平台管理消息
     - Favicon相关消息
     - 系统状态消息
     - 提示词同步消息
   - **设计**: 集中化消息处理，统一的错误处理和响应格式

3. **`eventListeners.ts` - 事件监听管理器**
   - **职责**: 处理Chrome扩展的生命周期事件
   - **功能**:
     - 扩展安装/更新事件
     - Service Worker启动事件
     - 标签页更新事件
     - 快捷键命令事件
   - **设计**: 统一的事件监听管理，模块化的事件处理

4. **`databaseConnection.ts` - 数据库连接管理**
   - **职责**: 管理数据库连接的生命周期
   - **功能**: 数据库初始化、连接状态监控、异常处理
   - **设计**: 单例模式，确保数据库连接的唯一性和稳定性

5. **`keepAlive.ts` - 保活管理**
   - **职责**: 维持Service Worker的活跃状态
   - **功能**: 定期发送心跳、处理休眠唤醒、连接状态维护
   - **设计**: 轻量级保活机制，避免Service Worker被浏览器回收

6. **`healthMonitor.ts` - 健康监控**
   - **职责**: 监控插件各模块的健康状态
   - **功能**: 性能监控、错误统计、状态报告
   - **设计**: 非侵入式监控，提供插件运行状态的可观测性

#### Background模块开发规则

1. **模块独立性**: 每个模块应该可以独立测试和维护
2. **错误隔离**: 模块间错误不应相互影响，使用统一的错误处理机制
3. **日志规范**: 使用一致的日志前缀和格式，便于调试和监控
4. **异步处理**: 所有耗时操作必须异步处理，避免阻塞主线程
5. **资源管理**: 合理管理内存和定时器，避免资源泄漏

---

## 三、核心模块类图与职责

### 1. `background` 模块 (后台服务层)

`background` 模块是插件的控制中心，采用模块化架构，实现了高度的职责分离和可维护性。

**类图:**
```mermaid
classDiagram
    class BackgroundIndex {
        +initialize()
        +setupEventListeners()
        +setupMessageHandler()
    }
    class MessageHandler {
        +handleMessage(message, sender, sendResponse)
        +routeToService(messageType, payload)
    }
    class EventListeners {
        +setupInstallListener()
        +setupTabUpdateListener()
        +setupCommandListener()
    }
    class DatabaseConnection {
        +initialize()
        +getConnection()
        +healthCheck()
    }
    class KeepAlive {
        +startHeartbeat()
        +maintainConnection()
    }
    class HealthMonitor {
        +monitorPerformance()
        +reportStatus()
    }

    BackgroundIndex --> MessageHandler : Uses
    BackgroundIndex --> EventListeners : Uses
    MessageHandler --> DatabaseConnection : Uses
    MessageHandler --> KeepAlive : Uses
    MessageHandler --> HealthMonitor : Uses
```

### 2. `common` 模块 (核心服务层)

`common` 模块是所有业务逻辑的所在地，采用了经典的分层架构，确保了业务逻辑和数据访问的清晰分离。

**类图:**
```mermaid
classDiagram
    class ServiceLayer {
        +ChatHistoryService
        +ChatPromptService
        +PlatformService
        +MessagingService
        +DatabaseProxy
    }
    class DAOLayer {
        +ChatHistoryDao
        +ChatPromptDao
        +PlatformDao
    }
    class Database {
        +DexieInstance
        +Schema
        +Indexes
    }

    ServiceLayer --> DAOLayer : Uses
    DAOLayer --> Database : Accesses

    class ChatHistoryService {
        +saveHistory(data)
        +getHistoryList()
        +deleteHistory(id)
        +searchHistory(query)
    }
    class ChatPromptService {
        +savePrompt(data)
        +getPromptList()
        +syncPrompts()
        +updatePrompt(id, data)
    }
    class PlatformService {
        +detectPlatform()
        +getPlatformConfig()
        +updateNotePlatformSettings()
        +getSupportedPlatforms()
    }
    class MessagingService {
        +sendMessage(type, payload)
        +broadcastMessage(message)
        +handleResponse(response)
    }
    class DatabaseProxy {
        +executeQuery(query)
        +transaction(operations)
        +backup()
        +restore()
    }
```

**各层职责:**
- **`Service` (服务层)**: **业务逻辑的实现者**。
  - **功能**: 处理复杂的业务流程，例如“保存一条新的聊天记录”可能需要先查询`ChatPromptDao`，如果提示词不存在则创建，然后再调用`ChatHistoryDao`创建历史记录。它负责协调一个或多个DAO来完成一个完整的业务功能。
  - **规则**: Service层是业务逻辑的唯一入口，被`background`中的`messageHandler`调用。

- **`DAO` (数据访问对象层)**: **数据库的直接操作者**。
  - **功能**: 封装对单个数据库表的所有`CRUD`（增、删、改、查）操作。例如，`ChatHistoryDao`只负责`chat_history`表的原子操作。
  - **规则**: DAO层不应包含任何业务逻辑，只提供纯粹的数据读写能力。

- **`database` (数据库层)**: **数据库的定义者**。
  - **功能**: 使用`Dexie.js`库来定义数据库的表结构（Schema）、版本和索引。
  - **规则**: 只负责数据库的初始化和连接管理。

### 3. `content` 模块 (页面交互层)

`content` 模块的设计核心是**中介者模式 (Mediator Pattern)**，由`BaseAIAdapter`扮演中介者的角色，统一协调所有子模块的交互。

**类图:**
```mermaid
classDiagram
    class BaseAIAdapter {
        <<Mediator>>
        +inputCapture
        +archiveButtonInject
        +notify(sender, event)
    }
    class InputCapture {
        -adapter: BaseAIAdapter
        +listenToInput()
    }
    class ArchiveButtonInject {
        -adapter: BaseAIAdapter
        +injectButton()
    }
    class ContentScriptManager {
        +createAdapter()
    }

    ContentScriptManager ..> BaseAIAdapter : Creates
    BaseAIAdapter o-- InputCapture
    BaseAIAdapter o-- ArchiveButtonInject
    InputCapture --|> BaseAIAdapter : Notifies
    ArchiveButtonInject --|> BaseAIAdapter : Notifies
```

**各模块职责:**
- **`ContentScriptManager.ts`**: **内容脚本的入口和管理器**。
  - **功能**: 检测当前页面URL，并据此创建对应的平台适配器实例（如`new ChatGPTAdapter()`）。它是`content`脚本的启动点。

- **`BaseAIAdapter.ts`**: **核心中介者**。
  - **功能**: 作为所有`content`子模块的“应用上下文”。它持有所有子模块（如`InputCapture`, `ArchiveButtonInject`）的实例，并提供一个统一的接口供它们交互。它还负责合并通用和平台特定的选择器。
  - **设计模式**: 中介者模式、模板方法模式。

- **`adapters/` (具体适配器)**: **平台策略的实现者**。
  - **功能**: 继承自`BaseAIAdapter`，实现抽象方法`getSelectors()`来提供特定AI网站的DOM元素选择器配置。每个适配器专注于一个平台的特定选择器和页面逻辑。
  - **设计模式**: 策略模式（每个子类都是一个策略）。
  - **实现示例**: `ChatGPTAdapter`提供`#prompt-textarea`等ChatGPT特定选择器，`ClaudeAdapter`提供`div[contenteditable="true"]`等Claude特定选择器。

- **`capture/` (页面捕捉模块)**: **页面的“观察者”**。
  - **功能**: 其**唯一职责**是查找关键的DOM元素（如输入框、发送按钮、聊天记录容器）并监听其原生事件（如`click`, `input`）。
  - **规则**: **严禁**修改DOM或执行任何业务逻辑。当事件发生时，它必须通过调用`Adapter`的方法来通知中介者。

- **`inject/` (UI注入模块)**: **页面的“修改者”**。
  - **功能**: 负责将我们的React组件（如存档按钮）“注入”到页面的正确位置，并管理其生命周期（创建、显示、隐藏、销毁）。
  - **规则**: 它处理UI的挂载逻辑和来自UI组件的用户交互（如按钮点击），然后通过`Adapter`通知中介者。

- **`components/` (UI组件模块)**: **纯UI的实现**。
  - **功能**: 存放用React编写的、可注入到页面中的UI组件。这些组件只负责渲染和样式，不包含复杂的业务逻辑。

### 4. `popup` 和 `options` 模块 (用户界面层)

这两个模块都是基于React的用户界面应用，采用现代前端架构模式。

**类图:**
```mermaid
classDiagram
    class PopupApp {
        +render()
        +handleUserInteraction()
    }
    class OptionsApp {
        +render()
        +handleSettings()
    }
    class Header {
        +displayTitle()
        +showNavigation()
    }
    class HomePage {
        +showMainContent()
        +handleActions()
    }
    class AppStore {
        +state
        +actions
        +getters
    }
    class MessagingService {
        +sendToBackground()
        +receiveFromBackground()
    }

    PopupApp --> Header : Uses
    PopupApp --> HomePage : Uses
    PopupApp --> AppStore : Uses
    OptionsApp --> AppStore : Uses
    PopupApp --> MessagingService : Uses
    OptionsApp --> MessagingService : Uses
```

**各模块职责:**
- **`popup/`**: 插件弹窗界面，提供快速访问和操作功能
- **`options/`**: 插件设置页面，提供详细的配置选项
- **`components/`**: 可复用的UI组件库，支持跨模块使用
- **`stores/`**: 状态管理，使用现代状态管理模式

### 5. 通用支持模块

**`components/` (可复用组件库)**
- **PlatformIcon.ts**: 平台图标组件，支持多种AI平台的图标显示
- **Toast.ts**: 消息提示组件，提供统一的用户反馈机制
- **ui/**: 基础UI组件库，包含按钮、输入框等基础组件

**`stores/` (状态管理)**
- **app-store.ts**: 应用全局状态管理，使用现代状态管理模式

**`styles/` (样式系统)**
- **globals.css**: 全局样式定义，基于Tailwind CSS框架

---

## 四、Content模块开发核心准则 (详细版)

为保证`content`模块的清晰和稳定，必须严格遵守以下开发准则：

1.  **`BaseAIAdapter`职责明确**
    -   作为初始化器，负责各个子模块（如`InputCapture`, `ArchiveButtonInject`）的初始化和销毁。
    -   不处理具体业务逻辑，只提供基础设施支持。
    -   提供统一的选择器配置和平台特定配置的合并能力。

2.  **`capture`模块只观察，不行动**
    -   该模块的职责是 **"观察"** 页面。它负责查找DOM元素并监听原生事件。
    -   当事件发生时，通过自定义事件（定义在`DOMEnum.ts`中）通知其他模块。
    -   **严禁** 在`capture`模块中修改DOM或实现任何注入逻辑。

3.  **`inject`模块只负责UI的"注入"与交互**
    -   该模块负责将`components`目录下的React组件挂载到页面上。
    -   通过`addListener`监听来自`capture`模块的自定义事件。
    -   处理组件自身的事件（如点击）和状态更新。

4.  **数据持久化必须通过消息**
    -   `content`模块中的任何部分（包括`Adapter`）都 **不能** 直接访问数据库。
    -   当需要保存或查询数据时，必须构建一个消息对象，并通过统一的消息服务发送给`background`脚本，由`background`调用`common`层的`Service`来完成。

5.  **通过适配器子类扩展新平台**
    -   `BaseAIAdapter`提供了通用的流程和方法（**模板方法模式**）。
    -   支持新网站时，只需在`adapters`目录下创建一个新的子类，并实现`getSelectors()`抽象方法。选择器系统的实际工作机制如下：
        - **平台特定选择器**: 子类实现`getSelectors()`方法，返回该平台独有的选择器配置（如ChatGPT的`#prompt-textarea`、Claude的`div[contenteditable="true"]`）
        - **通用选择器**: 在`CommonSelectors`配置文件中定义跨平台通用的选择器（如`textarea[placeholder*="message"]`、`button[type="submit"]`等）
        - **选择器合并**: `BaseAIAdapter`的`mergeSelectors()`方法将平台特定选择器和通用选择器合并，平台特定选择器优先级更高，通用选择器作为降级备选方案

---

## 五、整体架构流程图

### 消息流架构
```mermaid
sequenceDiagram
    participant C as Content Script
    participant B as Background
    participant S as Service Layer
    participant D as Database
    participant P as Popup/Options

    C->>B: 发送消息 (保存聊天记录)
    B->>S: 调用 ChatHistoryService
    S->>D: 通过 DAO 操作数据库
    D-->>S: 返回操作结果
    S-->>B: 返回业务结果
    B-->>C: 发送响应消息

    P->>B: 请求数据 (获取历史记录)
    B->>S: 调用 ChatHistoryService
    S->>D: 查询数据库
    D-->>S: 返回查询结果
    S-->>B: 返回业务数据
    B-->>P: 发送数据响应
```

### 数据流架构
```mermaid
flowchart TD
    A[用户操作] --> B[Content Script]
    B --> C[BaseAIAdapter 中介者]
    C --> D[InputCapture 监听]
    C --> E[ArchiveButtonInject 注入]
    C --> F[消息服务]
    F --> G[Background MessageHandler]
    G --> H[Service Layer]
    H --> I[DAO Layer]
    I --> J[Dexie Database]

    K[Popup/Options] --> F
    L[Event Listeners] --> G
    M[Health Monitor] --> G
    N[Keep Alive] --> G
```

### 模块依赖关系
```mermaid
graph TD
    A[Background] --> B[Common Services]
    C[Content Scripts] --> B
    D[Popup] --> B
    E[Options] --> B

    B --> F[DAO Layer]
    F --> G[Database]

    C --> H[Platform Adapters]
    C --> I[UI Components]
    C --> J[Capture Modules]
    C --> K[Inject Modules]

    H --> L[BaseAIAdapter]
    J --> L
    K --> L
```

---

## 六、开发最佳实践

### Background模块最佳实践
1. **保持主入口简洁**: `index.ts` 应该只做协调工作，不包含具体业务逻辑
2. **消息处理集中化**: 所有消息处理逻辑都在 `messageHandler.ts` 中统一管理
3. **事件监听统一管理**: 所有Chrome事件监听都在 `eventListeners.ts` 中处理
4. **错误处理一致性**: 使用统一的错误处理模式和日志格式
5. **异步操作规范**: 所有耗时操作必须异步处理，避免阻塞Service Worker

### Common模块最佳实践
1. **严格分层**: Service层不直接操作数据库，必须通过DAO层
2. **单一职责**: 每个Service只处理一个业务领域的逻辑
3. **事务管理**: 复杂操作使用数据库事务确保数据一致性
4. **错误传播**: 底层错误要正确传播到上层，便于调试和用户反馈

### Content模块最佳实践
1. **中介者模式**: 严格遵循BaseAIAdapter作为唯一中介者的原则
2. **职责分离**: Capture只观察，Inject只修改，Adapter负责协调
3. **平台扩展**: 新平台支持通过继承BaseAIAdapter实现，不修改核心逻辑
4. **性能优化**: 使用防抖、节流等技术优化DOM操作和事件监听