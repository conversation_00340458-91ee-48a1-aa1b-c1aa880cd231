import { ErrorType, ErrorHandler } from "../../types/ErrorTypes";
import { KimiSelectorService } from "./KimiSelectorService";
import { KimiClipboardService } from "./KimiClipboardService";
import { AnswerModel } from "../../model/AnswerModel";
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

/**
 * Kimi答案捕获服务
 * 专门处理答案监听、检测和内容提取
 */
export class KimiAnswerService {
    private answerObserver: MutationObserver | null = null;
    private readonly OBSERVER_CONFIG = { childList: true, subtree: true };
    private readonly ANSWER_TIMEOUT = 30000; // 30秒答案超时

    /**
     * 在指定的聊天列表节点上启动监听
     * @param chatListElement 聊天列表节点
     */
    public async startListeningOnChatList(chatListElement: Element): Promise<void> {
        try {
            console.info('[KimiAnswerService] 在指定聊天列表节点上启动监听');
            
            // 先停止之前的监听器
            this.stopAnswerListener();

            // 获取现有的问题和答案
            await this.captureExistingQAPairs(chatListElement);
            
            // 在指定节点上创建新的监听器
            this.answerObserver = new MutationObserver((mutations) => {
                this.handleMutations(mutations);
            });
            
            // 在聊天列表节点上启动监听
            this.answerObserver.observe(chatListElement, this.OBSERVER_CONFIG);
            console.info('[KimiAnswerService] 聊天列表监听器设置完成');
            
        } catch (error) {
            ErrorHandler.handle(ErrorType.OBSERVER_INIT_FAILED, error, { type: 'chatList' });
        }
    }

    /**************************************************↓↓↓↓↓ 已存在的 ↓↓↓↓↓ ********************************************************************* */
    /**
     * 捕获现有的问答对
     * @param chatListElement 聊天列表节点
     */
    private async captureExistingQAPairs(chatListElement: Element): Promise<void> {
        try {
            console.info('[KimiAnswerService] 开始捕获现有问答对');
            const selectors = SelectorManager.getSelector();

            // 获取所有已存在的问题节点
            const PromptElements = DOMUtils.findElementAllInContainer(chatListElement, selectors.promptItem);
            console.info(`[KimiAnswerService] 找到 ${PromptElements.length} 个问题节点`);
            // 逐个处理问题节点
            for (let i = 0; i < PromptElements.length; i++) {
                const PromptElement = PromptElements[i];
                await this.processExistingPrompt(PromptElement);
            }

            // 获取所有已存在的答案节点
            const answerElements = DOMUtils.findElementAllInContainer(chatListElement, selectors.answerItem);
            console.info(`[KimiAnswerService] 找到 ${answerElements.length} 个答案节点`);
            // 逐个处理已存在的答案节点
            for (let i = 0; i < answerElements.length; i++) {
                const answerElement = answerElements[i];
                await this.processExistingAnswer(answerElement);
            }
            
            console.info(`[KimiAnswerService] 现有问答对捕获完成，处理了 ${answerElements.length} 个答案`);
        } catch (error) {
            console.error('[KimiAnswerService] 捕获现有问答对失败', error);
        }
    }

    /**
     * 处理已存在的问题节点
     * @param PromptElement 问题节点
     */ 
    private async processExistingPrompt(PromptElement: Element): Promise<void> {
            const selectors = SelectorManager.getSelector();
            // 检查是否有完成状态标识
            const contentNode = DOMUtils.findElementInContainer(PromptElement, selectors.promptContent);
            // 获得文本
            if(contentNode){
                contentNode.textContent = contentNode.textContent?.trim();
                await AnswerModel.getInstance().addPrompt(contentNode.textContent);
            }
    }

    /**************************************************↑↑↑↑↑ 已存在的 ↑↑↑↑↑ ********************************************************************* */

    /**
     * 提取答案内容的通用方法
     * 从答案节点中查找复制按钮并获取答案内容
     * @param answerElement 答案节点
     * @param context 上下文描述（用于日志）
     * @returns 答案内容，如果提取失败则返回 null
     */
    private async extractAnswerContent(answerElement: Element, context: string): Promise<string | null> {
        try {
            const selectors = SelectorManager.getSelector();
            
            // 1. 查找 answerCompletion 组件
            const completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
            if (!completionNode) {
                console.warn(`[KimiAnswerService] ${context}: 未找到答案完成组件`);
                return null;
            }

            // 2. 查找复制按钮
            const copyButton = KimiSelectorService.findCompletionButton(completionNode);
            if (!copyButton) {
                console.warn(`[KimiAnswerService] ${context}: 未找到复制按钮`);
                return null;
            }

            // 3. 使用剪贴板服务获取内容
            const answerContent = await KimiClipboardService.simulateClickAndGetContent(answerElement, copyButton);

            if (!answerContent || !answerContent.trim()) {
                console.warn(`[KimiAnswerService] ${context}: 提取的答案内容为空`);
                return null;
            }

            if (answerContent) {
                // 存入 AnswerModel
                const answerIndex = await AnswerModel.getInstance().addAnswer(answerContent);
                console.info(`[KimiAnswerService] ${context} 已保存到数据模型，索引: ${answerIndex}`);
                
                // 触发自定义事件，通知答案已提取
                this.dispatchAnswerExtractedEvent(answerElement, answerIndex);
            }

            return answerContent;
        } catch (error) {
            console.error(`[KimiAnswerService] ${context}: 提取答案内容失败`, error);
            return null;
        }
    }

    /**
     * 触发答案提取完成事件
     * @param answerElement 答案元素
     * @param answerIndex 答案索引
     */
    private dispatchAnswerExtractedEvent(answerElement: Element, answerIndex: number): void {
        try {
            const event = new CustomEvent('ANSWER_EXTRACTED', {
                detail: { 
                    answerElement, 
                    answerIndex 
                }
            });
            document.dispatchEvent(event);
            console.info(`[KimiAnswerService] 触发 ANSWER_EXTRACTED 事件，索引: ${answerIndex}`);
        } catch (error) {
            console.error('[KimiAnswerService] 触发事件失败', error);
        }
    }

    /**
     * 处理已存在的答案节点
     * @param answerElement 答案节点
     */
    private async processExistingAnswer(answerElement: Element): Promise<void> {
        try {
            const selectors = SelectorManager.getSelector();
            
            // 检查是否有完成状态标识
            const completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
            if (!completionNode) {
                console.warn('[KimiAnswerService] 答案节点未完成，跳过处理');
                return;
            }

            // 使用统一的方法提取答案内容
            const answerContent = await this.extractAnswerContent(answerElement, '处理现有答案');
        } catch (error) {
            console.error('[KimiAnswerService] 处理现有答案失败', error);
        }
    }

    /**************************************************↑↑↑↑↑ 已存在的 ↑↑↑↑↑ ********************************************************************* */

    /**
     * 处理 DOM 变化
     * @param mutations DOM 变化列表
     */
    private handleMutations(mutations: MutationRecord[]): void {
        try {
            mutations.forEach((mutation) => {
                // console.log("[KimiAnswerService] DOM 节点变化", mutation)
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node instanceof Element) {
                            // 异步处理，但不等待结果，避免阻塞MutationObserver
                            this.handleNewNode(node).catch(error => {
                                console.error('[KimiAnswerService] 处理新节点失败', error);
                            });
                        }
                    });
                }
            });
        } catch (error) {
            ErrorHandler.handle(ErrorType.ANSWER_CAPTURE_FAILED, error);
        }
    }


    private generatingPromptElement: Element | null = null;
    private generatingAnswerElement: Element | null = null;

    /**
     * 处理新添加的节点 - 异步处理，支持问题和答案节点
     * @param node 新节点
     */
    private async handleNewNode(node: Element): Promise<void> {
        const selectors = SelectorManager.getSelector();
        if (!selectors) {
            console.warn('[KimiAnswerService] 选择器配置未找到');
            return;
        }


        // 检测问题节点
        if (selectors.promptItem?.some(sel => node.matches(sel))) {
            console.info('[KimiAnswerService] 检测到新的问题元素');
            if (this.generatingPromptElement) {
                console.info('[KimiAnswerService] 已存在生成中的问题节点，跳过处理');
                return;
            }else{
                this.generatingPromptElement = node;
                // 立即处理问题节点（问题内容通常是完整的）
                await this.processExistingPrompt(this.generatingPromptElement);
            }
        } else {
            // 查找子节点中的问题元素
            const childPromptElements = DOMUtils.findElementAllInContainer(node, selectors.promptItem);
            if (childPromptElements?.length > 0) {
                console.info('[KimiAnswerService] 在子节点中检测到问题元素');
                if (this.generatingPromptElement) {
                    console.info('[KimiAnswerService] 已存在生成中的问题节点，跳过处理');
                    return;
                }else{
                    this.generatingPromptElement = childPromptElements[0]; // 取第一个
                    // 立即处理问题节点（问题内容通常是完整的）
                    await this.processExistingPrompt(this.generatingPromptElement);
                }
            }
        }

        // 检测答案节点
        if (selectors.answerItem?.some(sel => node.matches(sel))) {
            console.info('[KimiAnswerService] 检测到新的答案元素');
            if(this.generatingAnswerElement){
                console.info('[KimiAnswerService] 已存在生成中的答案节点，跳过处理');
                return;
            }
            this.generatingAnswerElement = node;
            this.processGeneratingAnswer(this.generatingAnswerElement);
        } else {
            // 查找子节点中的答案元素
            const childAnswerElements = DOMUtils.findElementAllInContainer(node, selectors.answerItem);
            if (childAnswerElements?.length > 0) {
                console.info('[KimiAnswerService] 在子节点中检测到答案元素');
                if(this.generatingAnswerElement){
                    console.info('[KimiAnswerService] 已存在生成中的答案节点，跳过处理');
                    return;
                }
                this.generatingAnswerElement = childAnswerElements[0]; // 取第一个
                this.processGeneratingAnswer(this.generatingAnswerElement);
            }
        }
    }

    private clearGeneratingElements(): void {
        this.generatingPromptElement = null;
        this.generatingAnswerElement = null;
    }

    /**
     * 处理正在生成的答案节点
     * 监听answerCompletion组件和copyButton的出现
     */
    private processGeneratingAnswer(answerElement: Element): void {
        console.info('[KimiAnswerService] 开始监听答案生成完成状态');
        
        const startTime = Date.now();
        
        // 创建专门的监听器来监听答案完成状态
        const completionObserver = new MutationObserver((mutations) => {
            // 检查是否超时
            if (Date.now() - startTime > this.ANSWER_TIMEOUT) {
                console.warn('[KimiAnswerService] 答案生成监听超时');
                completionObserver.disconnect();
                this.clearGeneratingElements();
                return;
            }

            // 检查答案是否完成（answerCompletion + copyButton）
            if (this.isAnswerComplete(answerElement)) {
                console.info('[KimiAnswerService] 检测到答案生成完成');
                completionObserver.disconnect();
                
                // 答案完成，开始提取内容
                this.extractCompletedAnswer(answerElement).catch(error => {
                    console.error('[KimiAnswerService] 提取完成答案失败', error);
                });
            }
        });

        // 监听answerElement及其子树的变化
        completionObserver.observe(answerElement, this.OBSERVER_CONFIG);
        
        // 立即检查一次（可能答案已经完成了）
        if (this.isAnswerComplete(answerElement)) {
            console.info('[KimiAnswerService] 答案已经完成，立即处理');
            completionObserver.disconnect();
            this.extractCompletedAnswer(answerElement).catch(error => {
                console.error('[KimiAnswerService] 提取完成答案失败', error);
            });
        }
    }

    /**
     * 检查答案是否完成生成
     * 标准：存在answerCompletion组件且包含copyButton
     */
    private isAnswerComplete(answerElement: Element): boolean {
        const selectors = SelectorManager.getSelector();
        if (!selectors?.answerCompletion || !selectors?.copyButton) {
            return false;
        }

        // 查找answerCompletion组件
        const completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
        if (!completionNode) {
            return false;
        }

        // 检查completionNode中是否存在copyButton
        const copyButton = DOMUtils.findElementInContainer(completionNode, selectors.copyButton);
        return !!copyButton;
    }

    /**
     * 提取已完成的答案内容
     */
    private async extractCompletedAnswer(answerElement: Element): Promise<void> {
        try {
            // 使用统一的方法提取答案内容
            const answerContent = await this.extractAnswerContent(answerElement, '提取新生成答案');
        } catch (error) {
            console.error('[KimiAnswerService] 提取已完成答案失败', error);
        }
        this.clearGeneratingElements();
    }

    /**
     * 停止答案监听
     */
    public stopAnswerListener(): void {
        if (this.answerObserver) {
            this.answerObserver.disconnect();
            this.answerObserver = null;
            console.info('[KimiAnswerService] 答案监听器已停止');
        }
    }

    /**
     * 销毁服务
     */
    public destroy(): void {
        this.stopAnswerListener();
        this.clearGeneratingElements();
        
        console.info('[KimiAnswerService] 答案服务已销毁');
    }
}