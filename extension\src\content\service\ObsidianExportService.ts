import { AnswerModel } from '../model/AnswerModel';
import { fileSystemService } from './FileSystemService';


/**
 * Obsidian 导出错误类
 */
export class ObsidianExportError extends Error {
    constructor(
        public code: ExportError,
        message: string
    ) {
        super(message);
        this.name = 'ObsidianExportError';
    }
}

/**
 * Obsidian 导出服务
 * 负责生成 Markdown 内容、文件命名、文件保存
 */
export class ObsidianExportService {
    private static instance: ObsidianExportService;
    private existingFilenames: Set<string> = new Set();

    private constructor() {}

    /**
     * 获取单例实例
     */
    public static getInstance(): ObsidianExportService {
        if (!ObsidianExportService.instance) {
            ObsidianExportService.instance = new ObsidianExportService();
        }
        return ObsidianExportService.instance;
    }

    /**
     * 导出到 Obsidian
     */
    public async exportToObsidian(params: ExportParams): Promise<void> {
        try {
            console.log('[ObsidianExportService] 开始导出', params);

            // 1. 获取问答对
            const qaPair = this.getQAPair(params.answerIndex);
            if (!qaPair) {
                throw new ObsidianExportError(
                    ExportError.INVALID_INDEX,
                    '无法获取问答内容'
                );
            }

            // 2. 构建导出数据
            const exportData: ExportData = {
                title: params.title,
                question: qaPair.prompt,
                answer: qaPair.answer,
                tags: params.tags,
                timestamp: this.getCurrentTimestamp()
            };

            // 3. 生成 Markdown 内容
            const markdown = this.generateMarkdown(exportData);

            // 4. 生成文件名
            const filename = await this.generateFilename(params.title);

            // 5. 保存文件（会自动处理目录选择）
            await this.saveFile(filename, markdown);

            console.log('[ObsidianExportService] 导出成功', { filename });

        } catch (error) {
            // 用户取消选择目录
            if (error instanceof Error && error.message === '用户取消了目录选择') {
                console.log('[ObsidianExportService] 用户取消了目录选择');
                this.showToast('已取消导出', 'warning');
                return; // 不抛出错误，静默返回
            }

            if (error instanceof ObsidianExportError) {
                this.showToast(error.message, 'error');
                throw error;
            }
            
            console.error('[ObsidianExportService] 导出失败', error);
            this.showToast('导出失败，请重试', 'error');
            throw new ObsidianExportError(
                ExportError.FILE_SAVE_FAILED,
                error instanceof Error ? error.message : '未知错误'
            );
        }
    }

    /**
     * 获取问答对
     */
    private getQAPair(answerIndex: number): QAPair | null {
        return AnswerModel.getInstance().getQAPair(answerIndex);
    }

    /**
     * 获取当前时间戳（格式化）
     */
    private getCurrentTimestamp(): string {
        const now = new Date();
        return now.toISOString().replace('T', ' ').substring(0, 19);
    }

    /**
     * 生成 Markdown 内容
     */
    private generateMarkdown(data: ExportData): string {
        const frontmatter = `---
title: ${data.title}
date: ${data.timestamp}
tags: [${data.tags.join(', ')}]
source: Kimi Chat
---

`;

        const content = `# ${data.title}

## 问题

${data.question}

## 答案

${data.answer}
`;

        return frontmatter + content;
    }

    /**
     * 生成文件名
     */
    private async generateFilename(title: string): Promise<string> {
        // 1. 过滤特殊字符
        let filename = title.replace(/[/\\:*?"<>|]/g, '-');

        // 2. 限制长度
        if (filename.length > 100) {
            filename = filename.substring(0, 100);
        }

        // 3. 检查重复，添加后缀
        let finalFilename = filename + '.md';
        let counter = 1;

        while (this.existingFilenames.has(finalFilename)) {
            finalFilename = `${filename}_${counter}.md`;
            counter++;
        }

        // 4. 记录文件名
        this.existingFilenames.add(finalFilename);

        return finalFilename;
    }

    /**
     * 保存文件
     */
    private async saveFile(filename: string, content: string): Promise<void> {
        try {
            console.log('[ObsidianExportService] 保存文件', { filename });

            // 使用 File System Access API 保存文件（指定平台为 obsidian）
            await fileSystemService.writeFile('obsidian', filename, content);

            console.log('[ObsidianExportService] 文件保存成功', { filename });

        } catch (error) {
            console.error('[ObsidianExportService] 保存文件失败', error);
            
            if (error instanceof ObsidianExportError) {
                throw error;
            }
            
            throw new ObsidianExportError(
                ExportError.FILE_SAVE_FAILED,
                error instanceof Error ? error.message : '文件保存失败'
            );
        }
    }

    /**
     * 显示 Toast 提示
     */
    private showToast(message: string, type: 'success' | 'warning' | 'error'): void {
        const toast = document.createElement('div');
        toast.className = `obsidian-toast obsidian-toast-${type}`;
        toast.textContent = message;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 3000);
    }
}
