# AI Sync Plugin - Popup UI 实现需求

AI Sync Plugin 是一款智能AI对话捕获与知识管理插件，通过智能悬浮小球一键捕获任意AI平台对话，自动分类整理并沉淀为个人知识库。

请按照 需求-popup的UI设计.md 中的设计规范，实现popup的UI界面。并遵守 需求-popup的UI设计要点.md 中的设计实现要点。

技术栈使用：Tailwind CSS + shadcn/ui + TypeScript + React + Zustand+ React Router DOM。

ui效果要与popup-demo.html完全一致。

与background如何沟通可以查看 .augment/rules/background/background.md.

要求：
0. popup的路径位于extension/src/popup/目录下。
1. 不需要遵守标准工作流，生成specs内容，直接开始执行。
2. 遵照单一职能原则，每个功能点一个文件，每个文件不超过200行。
3. 全自动执行。不需要跟我确认，遇到问题自己查询网络或者context7解决。
4. 所有功能先不需要与background交互，先完成ui，但是预留与background交互的todo空位。
5. 只有设置页面的内容，与background交互，然后存入存储，但不是数据库。
6. 完成内容后生成总结文档。