---
type: "development_rules"
description: "Chrome插件Options模块React开发规则"
---

# Options规则

## 核心职责
- **配置管理**: 插件设置界面
- **数据持久化**: Chrome Storage API
- **用户体验**: 响应式设计

## 技术栈
- React 18 + TypeScript 5
- Tailwind CSS 3 + shadcn/ui

## 目录结构
```
options/
├── components/  # 配置组件
├── hooks/       # 自定义Hooks
├── App.tsx      # 主应用
└── main.tsx     # 入口
```

## 核心Hooks
- **useSettings**: 设置管理
- **useStorage**: Storage API封装

## 核心规则
- 使用MessagingService与Background通信
- 处理DatabaseResult格式数据
- 受控组件模式
- 表单验证和错误提示
- 响应式设计

## 检查清单
- [ ] React + TypeScript开发
- [ ] 遵循database.md规则
- [ ] 支持键盘导航
- [ ] 实现错误处理
- [ ] 文件≤300行
