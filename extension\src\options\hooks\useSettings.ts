import { useState, useEffect } from 'react'
import { MessagingService } from '@/common/service/MessagingService'
import { MessageType } from '@/common/types'
import type { OptionsSettings } from '../types/settings'

export const useSettings = () => {
  const [settings, setSettings] = useState<OptionsSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadSettings = async () => {
    try {
      setLoading(true)
      const result = await MessagingService.sendToBackground(
        MessageType.GET_SETTINGS
      )
      
      if (result.success) {
        setSettings(result.data)
      } else {
        setError(result.error || '加载设置失败')
      }
    } catch (err) {
      setError('加载设置时发生错误')
    } finally {
      setLoading(false)
    }
  }

  const updateSettings = async (newSettings: Partial<OptionsSettings>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings }
      const result = await MessagingService.sendToBackground(
        MessageType.UPDATE_SETTINGS,
        updatedSettings
      )
      
      if (result.success) {
        setSettings(updatedSettings)
        return true
      } else {
        setError(result.error || '保存设置失败')
        return false
      }
    } catch (err) {
      setError('保存设置时发生错误')
      return false
    }
  }

  useEffect(() => {
    loadSettings()
  }, [])

  return {
    settings,
    loading,
    error,
    updateSettings,
    reload: loadSettings
  }
}
