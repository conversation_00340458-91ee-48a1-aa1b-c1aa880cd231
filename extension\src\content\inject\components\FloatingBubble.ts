/**
 * 悬浮气泡UI组件
 * 专注于UI渲染和样式，不包含业务逻辑
 */
export class FloatingBubble {
  private bubble: HTMLElement | null = null
  private originalPosition = { top: '20px', right: '20px', left: 'auto' }

  /**
   * 创建浮动气泡DOM元素
   */
  render(): HTMLElement {
    // 检查现有元素是否仍然有效
    if (this.bubble && document.body.contains(this.bubble)) {
      console.log('Floating bubble already exists and is in DOM, returning existing element')
      return this.bubble
    }

    // 如果元素不在 DOM 中，重置引用并创建新元素
    if (this.bubble && !document.body.contains(this.bubble)) {
      console.log('Floating bubble exists but not in DOM, creating new one')
      this.bubble = null
    }

    console.log('Creating floating bubble DOM element...')

    this.bubble = document.createElement('div')
    this.bubble.id = 'echosync-floating-bubble'
    this.bubble.innerHTML = `
      <div class="bubble-content">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
        </svg>
      </div>
    `

    this.applyStyles()
    this.restorePosition()

    return this.bubble
  }

  /**
   * 应用样式
   */
  private applyStyles(): void {
    if (!this.bubble) return

    this.bubble.style.cssText = `
      position: fixed;
      top: ${this.originalPosition.top};
      right: ${this.originalPosition.right};
      left: ${this.originalPosition.left};
      width: 30px;
      height: 30px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10000;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: white;
      opacity: 0.9;
      user-select: none;
    `
  }

  /**
   * 恢复保存的位置
   */
  private restorePosition(): void {
    const savedPosition = localStorage.getItem('echosync-bubble-position')
    if (savedPosition) {
      try {
        const position = JSON.parse(savedPosition)
        this.originalPosition = position
        this.updatePosition(position)
      } catch (e) {
        console.warn('Failed to parse saved position:', e)
      }
    }
  }

  /**
   * 显示气泡
   */
  show(): void {
    if (this.bubble) {
      this.bubble.style.display = 'flex'
    }
  }

  /**
   * 隐藏气泡
   */
  hide(): void {
    if (this.bubble) {
      this.bubble.style.display = 'none'
    }
  }

  /**
   * 更新位置
   */
  updatePosition(position: { top?: string; right?: string; left?: string }): void {
    if (!this.bubble) return

    if (position.top) this.bubble.style.top = position.top
    if (position.right) this.bubble.style.right = position.right
    if (position.left) this.bubble.style.left = position.left
  }

  /**
   * 移动到输入框附近（左上角）
   */
  moveToInputField(inputElement: HTMLElement): void {
    // 检查气泡元素是否存在，如果不存在则重新创建
    if (!this.bubble || !document.body.contains(this.bubble)) {
      console.warn('【EchoSync】Bubble element is missing, recreating...');
      this.bubble = null;
      const newBubble = this.render();
      if (document.body && newBubble) {
        document.body.appendChild(newBubble);
      }
    }

    if (!this.bubble) {
      console.error('【EchoSync】Bubble element is null after recreation attempt!');
      return;
    }

    if (!inputElement) {
      console.error('【EchoSync】Input element is null!');
      return;
    }

    const rect = inputElement.getBoundingClientRect();
    const bubbleSize = 30;
    const margin = 8;

    // 计算左上角位置
    const left = Math.max(margin, rect.left - bubbleSize - margin);
    const top = Math.max(margin, rect.top - bubbleSize - margin);

    this.bubble.style.left = `${left}px`;
    this.bubble.style.top = `${top}px`;
    this.bubble.style.right = 'auto';
    this.bubble.style.transform = 'none';
  }

  /**
   * 移动到默认位置
   */
  moveToDefaultPosition(): void {
    if (!this.bubble || !document.body.contains(this.bubble)) {
      console.warn('【EchoSync】Bubble element missing in moveToDefaultPosition, recreating...');
      this.bubble = null;
      const newBubble = this.render();
      if (document.body && newBubble) {
        document.body.appendChild(newBubble);
      }
    }

    if (!this.bubble) return;

    this.bubble.style.top = this.originalPosition.top;
    this.bubble.style.right = this.originalPosition.right;
    this.bubble.style.left = this.originalPosition.left;
    this.bubble.style.transform = 'scale(1)';
  }

  /**
   * 设置悬停效果
   */
  setHoverEffect(isHover: boolean): void {
    if (!this.bubble || !document.body.contains(this.bubble)) {
      console.warn('【EchoSync】Bubble element missing in setHoverEffect, recreating...');
      this.bubble = null;
      const newBubble = this.render();
      if (document.body && newBubble) {
        document.body.appendChild(newBubble);
      }
    }

    if (!this.bubble) return;

    if (isHover) {
      this.bubble.style.transform = 'scale(1.1)';
      this.bubble.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.25)';
    } else {
      this.bubble.style.transform = 'scale(1)';
      this.bubble.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
    }
  }

  /**
   * 边界回弹效果
   */
  snapToBoundary(): void {
    if (!this.bubble) return

    const rect = this.bubble.getBoundingClientRect()
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    const bubbleSize = 30

    let newLeft = rect.left
    let newTop = rect.top

    if (rect.left < 0) newLeft = 0
    if (rect.right > windowWidth) newLeft = windowWidth - bubbleSize
    if (rect.top < 0) newTop = 0
    if (rect.bottom > windowHeight) newTop = windowHeight - bubbleSize

    this.bubble.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    this.bubble.style.left = `${newLeft}px`
    this.bubble.style.top = `${newTop}px`

    this.savePosition({
      top: `${newTop}px`,
      left: `${newLeft}px`,
      right: 'auto'
    })
  }

  /**
   * 保存位置到localStorage
   */
  savePosition(position: { top: string; left: string; right: string }): void {
    this.originalPosition = position
    localStorage.setItem('echosync-bubble-position', JSON.stringify(position))
  }

  /**
   * 添加事件监听器
   */
  addEventListener(event: string, handler: EventListener): void {
    if (this.bubble) {
      this.bubble.addEventListener(event, handler)
    }
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(event: string, handler: EventListener): void {
    if (this.bubble) {
      this.bubble.removeEventListener(event, handler)
    }
  }

  /**
   * 获取DOM元素
   */
  getElement(): HTMLElement | null {
    return this.bubble
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    if (this.bubble) {
      this.bubble.remove()
      this.bubble = null
    }
  }
}

