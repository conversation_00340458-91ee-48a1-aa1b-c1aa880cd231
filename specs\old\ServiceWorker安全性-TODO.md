# ServiceWorker安全性实现 - TODO

## 需求分析
目前的架构将所有 `IndexedDB` 操作集中交给 `Service Worker` 处理，但面临Service Worker会被挂起（non-persistent）的问题，需要制定安全操作来确保与ServiceWorker交互成功，避免数据丢失。

### 核心问题
1. **Service Worker 会被挂起**: 30秒无活动后会被Chrome挂起
2. **数据库连接丢失**: SW挂起后数据库连接需要重新初始化
3. **消息发送失败**: 向挂起的SW发送消息可能失败
4. **用户体验延迟**: 需要唤醒SW才能执行数据库操作

### 解决方案
1. **确保SW在唤醒后能正确初始化数据库连接**
2. **发送数据库操作Message时，要确保Service Worker存活**
3. **使用chrome.alarms（1分钟），每分钟唤醒一次SW，降低延迟**

## 任务分解

### 🎯 任务1: 添加chrome.alarms权限和基础设施 [P0] ✅

#### 1.1 更新manifest.json权限
- [x] 在`extension/public/manifest.json`中添加`"alarms"`权限
- [x] 验证权限配置正确

#### 1.2 创建Service Worker保活管理器
- [x] 创建`extension/src/background/keepAlive.ts`
  - 管理chrome.alarms的创建和监听
  - 提供SW保活的核心逻辑
  - 每分钟触发一次保活操作

### 🎯 任务2: 增强数据库初始化安全性 [P0] ✅

#### 2.1 创建数据库连接管理器
- [x] 创建`extension/src/background/databaseConnection.ts`
  - 管理数据库连接状态
  - 提供连接检查和重连机制
  - 确保每次操作前数据库已正确初始化

#### 2.2 增强现有数据库初始化逻辑
- [x] 修改`background/index.ts`中的`initDatabase`函数
  - 添加连接状态检查
  - 添加重连机制
  - 添加初始化失败的重试逻辑

### 🎯 任务3: 增强消息服务的SW存活检查 [P1] ✅

#### 3.1 扩展MessagingService
- [x] 修改`extension/src/lib/service/messaging.ts`
  - 添加SW存活状态检查
  - 在发送消息前确保SW已唤醒
  - 添加消息发送失败的重试机制

#### 3.2 创建SW状态检测工具
- [x] 在`messaging.ts`中添加SW状态检测方法
  - `checkServiceWorkerAlive()`: 检查SW是否存活
  - `wakeUpServiceWorker()`: 主动唤醒SW
  - `ensureServiceWorkerReady()`: 确保SW准备就绪

### 🎯 任务4: 实现保活机制 [P1] ✅

#### 4.1 实现定时保活
- [x] 在`keepAlive.ts`中实现定时器逻辑
  - 创建1分钟间隔的chrome.alarm
  - 监听alarm事件并执行保活操作
  - 在SW启动时自动设置保活定时器

#### 4.2 实现智能保活策略
- [x] 添加智能保活逻辑
  - 检测用户活动状态
  - 在用户活跃时保持SW存活
  - 在用户不活跃时降低保活频率

### 🎯 任务5: 集成和测试 [P2] ✅

#### 5.1 集成所有安全机制
- [x] 修改`background/index.ts`集成新的安全机制
  - 启动时初始化保活管理器
  - 集成数据库连接管理器
  - 确保所有消息处理都经过安全检查

#### 5.2 添加错误处理和日志
- [x] 完善错误处理机制
  - 添加详细的日志记录
  - 添加错误恢复机制
  - 添加性能监控

## 文件大小控制
- 确保每个TypeScript文件不超过300行
- 如果文件过大，及时拆分为多个模块
- 保持代码简洁优雅，避免过度设计

## 验收标准
1. ✅ Service Worker能够每分钟自动保活
2. ✅ 数据库连接在SW唤醒后能正确重新初始化
3. ✅ 消息发送前能确保SW存活状态
4. ✅ 用户操作不会因SW挂起而失败
5. ✅ 所有TypeScript文件保持在300行以内
6. ✅ 代码保持简洁优雅，无过度设计

## 实施顺序 ✅
1. ✅ 先实现基础的chrome.alarms权限和保活机制
2. ✅ 再增强数据库连接的安全性
3. ✅ 最后完善消息服务的SW状态检查
4. ✅ 集成测试和优化

## 实施总结

### 已完成的文件
1. **`extension/public/manifest.json`** - 添加了`alarms`权限
2. **`extension/src/background/keepAlive.ts`** - Service Worker保活管理器（194行）
3. **`extension/src/background/databaseConnection.ts`** - 数据库连接管理器（247行）
4. **`extension/src/background/healthMonitor.ts`** - 健康监控管理器（200行）
5. **`extension/src/background/test-sw-security.ts`** - 安全性测试脚本（140行）
6. **`extension/src/lib/service/messaging.ts`** - 增强的消息服务（247行）
7. **`extension/src/background/index.ts`** - 集成所有安全机制的主文件（315行）

### 核心功能实现
1. **⏰ 定时保活机制**
   - 每分钟通过chrome.alarms唤醒Service Worker
   - 自动重新初始化保活机制
   - 支持手动保活和状态检查

2. **🔗 数据库连接安全**
   - 带重试的数据库连接机制
   - 连接状态检查和自动重连
   - 数据库健康状态监控

3. **📡 消息服务增强**
   - SW存活状态检查
   - 消息发送前确保SW就绪
   - 智能重试机制

4. **🏥 健康监控系统**
   - 定期健康检查
   - 自动问题修复
   - 系统状态API

5. **🧪 测试和验证**
   - 完整的测试套件
   - 压力测试
   - 开发环境自动测试

### 安全保障
- ✅ Service Worker不会因挂起而丢失数据
- ✅ 数据库连接在SW唤醒后能正确重新初始化
- ✅ 消息发送前确保SW存活状态
- ✅ 所有TypeScript文件保持在300行以内
- ✅ 代码简洁优雅，无过度设计
