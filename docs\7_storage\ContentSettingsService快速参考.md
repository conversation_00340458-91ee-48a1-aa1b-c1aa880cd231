# ContentSettingsService 快速参考

## API 概览

### 导入
```typescript
import { contentSettingsService, type NotePlatform } from '@/content/service/SettingsService'
```

### 类型定义
```typescript
type NotePlatform = 'obsidian' | 'notion' | 'markdown'
```

## 核心方法 (仅 2 个)

### 1. getExportPath()
```typescript
async getExportPath(platform: NotePlatform): Promise<string | null>
```

**功能**: 获取指定平台的导出路径  
**参数**: `platform` - 笔记平台名称  
**返回**: 导出路径字符串,未配置则返回 `null`

**示例**:
```typescript
const path = await contentSettingsService.getExportPath('obsidian')
if (path) {
  console.log('路径:', path)  // 例如: "D:/Notes/Obsidian"
}
```

---

### 2. isPlatformConfigured()
```typescript
async isPlatformConfigured(platform: NotePlatform): Promise<boolean>
```

**功能**: 检查指定平台是否已配置导出路径  
**参数**: `platform` - 笔记平台名称  
**返回**: `true` 已配置, `false` 未配置

**示例**:
```typescript
const isConfigured = await contentSettingsService.isPlatformConfigured('obsidian')
if (!isConfigured) {
  console.log('请先配置 Obsidian')
}
```

## 常用模式

### 模式 1: 导出前检查
```typescript
// 推荐: 先检查,再获取路径
async function exportNote(content: string) {
  const platform: NotePlatform = 'obsidian'
  
  // 1. 检查是否已配置
  const isConfigured = await contentSettingsService.isPlatformConfigured(platform)
  if (!isConfigured) {
    alert('请先在设置中配置 Obsidian 路径')
    return
  }
  
  // 2. 获取路径
  const path = await contentSettingsService.getExportPath(platform)
  
  // 3. 执行导出
  await saveToFile(`${path}/note.md`, content)
}
```

### 模式 2: 直接获取路径
```typescript
// 简洁: 直接获取,检查 null
async function quickExport(content: string) {
  const path = await contentSettingsService.getExportPath('obsidian')
  
  if (!path) {
    alert('Obsidian 未配置')
    return
  }
  
  await saveToFile(`${path}/note.md`, content)
}
```

### 模式 3: 多平台支持
```typescript
async function exportToUserSelectedPlatform(
  platform: NotePlatform, 
  content: string
) {
  const path = await contentSettingsService.getExportPath(platform)
  
  if (!path) {
    const names = {
      obsidian: 'Obsidian',
      notion: 'Notion',
      markdown: 'Markdown'
    }
    alert(`${names[platform]} 未配置`)
    return
  }
  
  await saveToFile(`${path}/note.md`, content)
}
```

### 模式 4: 批量检查
```typescript
async function checkAllPlatforms() {
  const platforms: NotePlatform[] = ['obsidian', 'notion', 'markdown']
  const results: Record<NotePlatform, boolean> = {
    obsidian: false,
    notion: false,
    markdown: false
  }
  
  for (const platform of platforms) {
    results[platform] = await contentSettingsService.isPlatformConfigured(platform)
  }
  
  return results
}
```

## 错误处理

### 友好的错误提示
```typescript
async function exportWithFriendlyError(platform: NotePlatform, content: string) {
  const isConfigured = await contentSettingsService.isPlatformConfigured(platform)
  
  if (!isConfigured) {
    const message = `
      ❌ ${platform.toUpperCase()} 未配置
      
      请按以下步骤配置:
      1️⃣ 点击扩展图标
      2️⃣ 选择 ${platform} 选项卡
      3️⃣ 点击"浏览文件夹"按钮
      4️⃣ 选择导出路径
    `.trim()
    
    showNotification(message)
    return false
  }
  
  const path = await contentSettingsService.getExportPath(platform)
  // 执行导出...
  return true
}
```

## 初始化检查

### Content Script 启动时检查
```typescript
async function initContentScript() {
  // 检查是否至少配置了一个平台
  const obsidian = await contentSettingsService.isPlatformConfigured('obsidian')
  const notion = await contentSettingsService.isPlatformConfigured('notion')
  
  if (!obsidian && !notion) {
    console.warn('⚠️ 未配置任何笔记平台')
    showSetupGuide()
    return false
  }
  
  console.log('✅ Content Script 初始化成功')
  return true
}
```

## 注意事项

### ✅ 正确用法
```typescript
// ✅ Content 模块中只读取
const path = await contentSettingsService.getExportPath('obsidian')

// ✅ 检查配置状态
const isConfigured = await contentSettingsService.isPlatformConfigured('obsidian')
```

### ❌ 错误用法
```typescript
// ❌ Content 模块不应该修改设置
// 所有写操作应该在 Popup 模块进行
await contentSettingsService.updatePath(...)  // 此方法不存在

// ❌ 不要缓存路径太久
// 设置可能随时变化,需要时再获取
const cachedPath = await contentSettingsService.getExportPath('obsidian')
setTimeout(() => {
  // 1小时后使用缓存路径 - 不推荐
  await saveToFile(cachedPath, content)
}, 3600000)

// ✅ 正确做法: 需要时再获取
setTimeout(async () => {
  const path = await contentSettingsService.getExportPath('obsidian')
  await saveToFile(path, content)
}, 3600000)
```

## 性能提示

### Chrome Storage 性能
- `getExportPath()` 调用 `chrome.storage.sync.get()`,通常 < 10ms
- 频繁调用不会造成性能问题
- 底层已有优化,无需在 Content 层额外缓存

### 推荐做法
```typescript
// ✅ 需要时获取,简单直接
async function export() {
  const path = await contentSettingsService.getExportPath('obsidian')
  // 使用 path...
}

// 🤔 过度优化,不推荐
let cachedPath: string | null = null
async function exportWithCache() {
  if (!cachedPath) {
    cachedPath = await contentSettingsService.getExportPath('obsidian')
  }
  // 使用 cachedPath... (可能已过期)
}
```

## 完整示例

### 实际使用场景: 保存提示词到笔记
```typescript
import { contentSettingsService, type NotePlatform } from '@/content/service/SettingsService'

interface PromptData {
  title: string
  content: string
  tags: string[]
}

async function savePromptToNote(
  prompt: PromptData,
  platform: NotePlatform = 'obsidian'
) {
  // 1. 检查配置
  const isConfigured = await contentSettingsService.isPlatformConfigured(platform)
  
  if (!isConfigured) {
    throw new Error(`${platform} 未配置导出路径,请先在设置中配置`)
  }
  
  // 2. 获取路径
  const exportPath = await contentSettingsService.getExportPath(platform)
  
  if (!exportPath) {
    throw new Error(`无法获取 ${platform} 的导出路径`)
  }
  
  // 3. 构造文件内容
  const markdown = `# ${prompt.title}

${prompt.content}

---
Tags: ${prompt.tags.join(', ')}
Date: ${new Date().toISOString()}
`
  
  // 4. 构造文件路径
  const filename = `${prompt.title.replace(/[/\\?%*:|"<>]/g, '-')}.md`
  const fullPath = `${exportPath}/${filename}`
  
  // 5. 保存文件 (需要实现文件保存逻辑)
  console.log(`📝 保存到: ${fullPath}`)
  // await actualSaveFunction(fullPath, markdown)
  
  return fullPath
}

// 使用示例
const prompt = {
  title: '如何写作',
  content: '1. 构思\n2. 大纲\n3. 写作\n4. 修改',
  tags: ['写作', '技巧']
}

savePromptToNote(prompt, 'obsidian')
  .then(path => console.log('✅ 保存成功:', path))
  .catch(err => console.error('❌ 保存失败:', err))
```

## 总结

### ContentSettingsService 特点
- 🎯 **简洁**: 仅 2 个方法,API 极简
- 🔒 **只读**: Content 模块无写权限
- ⚡ **快速**: 直接访问 storage,无额外开销
- 💡 **易用**: 清晰的输入输出,无复杂类型

### 记住这些
1. Content 模块只读设置
2. 所有写操作在 Popup 模块
3. 需要时获取路径,无需缓存
4. 提供友好的错误提示

---

**快速参考完毕,开始使用吧!** 🚀
