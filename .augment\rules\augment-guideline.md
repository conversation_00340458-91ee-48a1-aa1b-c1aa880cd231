---
type: "agent_requested"
description: "规则目录"
---
# EchoSync AI Extension - 开发规则

## 核心原则 (SOLID)
1. **单一职责**: 每个文件/类只负责一个功能
2. **开闭原则**: 对扩展开放，对修改封闭
3. **接口隔离**: 使用小而专一的接口
4. **依赖倒置**: 依赖抽象而非具体实现
5. **文件限制**: 单文件最大300行，超过必须拆分
6. **数据库访问**: 仅通过Background Script访问

## 模块规则

### Content Script
- **[总览](./content/content-overview.md)**: 架构和设计原则
- **[捕捉](./content/capture.md)**: 页面元素捕捉规则
- **[注入](./content/inject.md)**: UI注入规则
- **[服务](./content/service.md)**: 业务逻辑层规则
- **[模型](./content/model.md)**: 数据模型规则

### Background
- **[总览](./background/background.md)**: 后台服务规则
- **[数据库](./background/database.md)**: 数据库操作规则

### UI模块
- **[Popup](./popup/popup.md)**: 弹窗开发规则
- **[Options](./options/options.md)**: 设置页开发规则

### 基础规则
- **[项目结构](./00-project-structure.md)**: 目录组织规范
- **[命名规范](./01-naming-conventions.md)**: 命名规则
- **[UI组件](./02-ui-components.md)**: 组件开发规范

