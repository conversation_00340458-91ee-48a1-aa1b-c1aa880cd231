// Content 模块中使用设置功能的示例
// Content 模块只读设置,所有写操作在 Popup 模块完成

import { contentSettingsService, type NotePlatform } from '../service/ContentSettingsService'

/**
 * 示例 1: 获取 Obsidian 的导出路径
 */
export async function getObsidianExportPath() {
  const path = await contentSettingsService.getExportPath('obsidian')
  
  if (path) {
    console.log('✅ Obsidian 导出路径:', path)
    return path
  } else {
    console.warn('⚠️ Obsidian 未配置导出路径,请在设置中配置')
    return null
  }
}

/**
 * 示例 2: 获取 Notion 的导出路径
 */
export async function getNotionExportPath() {
  const path = await contentSettingsService.getExportPath('notion')
  
  if (path) {
    console.log('✅ Notion 导出路径:', path)
    return path
  } else {
    console.warn('⚠️ Notion 未配置导出路径,请在设置中配置')
    return null
  }
}

/**
 * 示例 3: 检查平台是否已配置
 */
export async function checkPlatformConfiguration(platform: NotePlatform) {
  const isConfigured = await contentSettingsService.isPlatformConfigured(platform)
  
  if (isConfigured) {
    console.log(`✅ ${platform} 已配置`)
    const path = await contentSettingsService.getExportPath(platform)
    console.log(`   导出路径: ${path}`)
  } else {
    console.log(`⚠️ ${platform} 未配置,请在扩展设置中配置导出路径`)
  }
  
  return isConfigured
}

/**
 * 示例 4: 根据用户选择的平台获取导出路径
 */
export async function getExportPathByUserSelection(userSelectedPlatform: NotePlatform) {
  console.log(`用户选择的平台: ${userSelectedPlatform}`)
  
  // 先检查是否已配置
  const isConfigured = await contentSettingsService.isPlatformConfigured(userSelectedPlatform)
  
  if (!isConfigured) {
    console.error(`❌ ${userSelectedPlatform} 未配置,无法导出`)
    return null
  }
  
  // 获取导出路径
  const exportPath = await contentSettingsService.getExportPath(userSelectedPlatform)
  console.log(`📂 导出路径: ${exportPath}`)
  
  return exportPath
}

/**
 * 示例 5: 批量检查所有平台的配置状态
 */
export async function checkAllPlatformsStatus() {
  const platforms: NotePlatform[] = ['obsidian', 'notion', 'markdown']
  const status: Record<NotePlatform, { configured: boolean; path: string | null }> = {
    obsidian: { configured: false, path: null },
    notion: { configured: false, path: null },
    markdown: { configured: false, path: null }
  }
  
  console.log('📊 检查所有平台配置状态:')
  
  for (const platform of platforms) {
    const isConfigured = await contentSettingsService.isPlatformConfigured(platform)
    const path = isConfigured ? await contentSettingsService.getExportPath(platform) : null
    
    status[platform] = { configured: isConfigured, path }
    
    console.log(`  ${platform}: ${isConfigured ? '✅ 已配置' : '❌ 未配置'}`)
    if (path) {
      console.log(`    路径: ${path}`)
    }
  }
  
  return status
}

/**
 * 示例 6: 在导出功能中使用
 */
export async function exportToNotePlatform(platform: NotePlatform, content: string, filename: string) {
  // 1. 检查平台是否已配置
  const isConfigured = await contentSettingsService.isPlatformConfigured(platform)
  
  if (!isConfigured) {
    throw new Error(`${platform} 未配置导出路径,请先在扩展设置中配置`)
  }
  
  // 2. 获取导出路径
  const exportPath = await contentSettingsService.getExportPath(platform)
  
  if (!exportPath) {
    throw new Error(`无法获取 ${platform} 的导出路径`)
  }
  
  // 3. 构造完整的文件路径
  const fullPath = `${exportPath}/${filename}.md`
  
  console.log(`📝 准备导出到: ${fullPath}`)
  console.log(`📄 内容长度: ${content.length} 字符`)
  
  // 4. 执行导出操作 (这里只是示例,实际导出逻辑需要另外实现)
  try {
    // await actualExportFunction(fullPath, content)
    console.log(`✅ 导出成功: ${fullPath}`)
    return fullPath
  } catch (error) {
    console.error(`❌ 导出失败:`, error)
    throw error
  }
}

/**
 * 示例 7: 在 Content Script 初始化时检查配置
 */
export async function initializeContentScript() {
  console.log('🚀 初始化 Content Script...')
  
  // 检查当前用户最可能使用的平台配置状态
  const obsidianConfigured = await contentSettingsService.isPlatformConfigured('obsidian')
  const notionConfigured = await contentSettingsService.isPlatformConfigured('notion')
  
  if (!obsidianConfigured && !notionConfigured) {
    console.warn('⚠️ 未配置任何笔记平台,某些功能可能无法使用')
    console.log('💡 请点击扩展图标打开设置页面进行配置')
    return false
  }
  
  console.log('✅ Content Script 初始化完成')
  return true
}

/**
 * 示例 8: 提供用户友好的错误提示
 */
export async function saveWithUserFriendlyError(platform: NotePlatform, content: string) {
  const isConfigured = await contentSettingsService.isPlatformConfigured(platform)
  
  if (!isConfigured) {
    // 根据平台类型提供具体的配置指导
    const platformNames: Record<NotePlatform, string> = {
      obsidian: 'Obsidian',
      notion: 'Notion',
      markdown: 'Markdown'
    }
    
    const message = `
❌ ${platformNames[platform]} 导出路径未配置

请按以下步骤配置:
1. 点击浏览器工具栏中的 EchoSync 扩展图标
2. 在设置页面中找到 "${platformNames[platform]}" 选项卡
3. 点击"浏览文件夹"按钮选择导出路径
4. 配置完成后重试
    `.trim()
    
    console.error(message)
    return { success: false, message }
  }
  
  const exportPath = await contentSettingsService.getExportPath(platform)
  console.log(`✅ 准备保存到: ${exportPath}`)
  
  // 执行实际的保存操作...
  return { success: true, path: exportPath }
}

// 导出主要的示例函数
export default {
  getObsidianExportPath,
  getNotionExportPath,
  checkPlatformConfiguration,
  getExportPathByUserSelection,
  checkAllPlatformsStatus,
  exportToNotePlatform,
  initializeContentScript,
  saveWithUserFriendlyError
}
