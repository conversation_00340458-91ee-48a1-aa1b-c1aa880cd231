import { MessageType } from "./enums";

// 用户相关类型
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  plan: 'free' | 'pro' | 'vip';
  createdAt: Date;
  updatedAt: Date;
}

// AI平台类型
export type AIPlatform =
  | 'chatgpt'
  | 'deepseek'
  | 'claude'
  | 'gemini'
  | 'kimi'
  | 'poe'
  | 'perplexity'
  | 'you';

export interface Platform {
  id: AIPlatform;
  name: string;
  url: string;
  enabled: boolean;
  selectors: {
    inputField: string;
    sendButton: string;
    messageContainer: string;
  };
}

// 提示词类型
export interface Prompt {
  id: string;
  content: string;
  platform: AIPlatform;
  timestamp: number;
  tags: string[];
  isFavorite: boolean;
  category?: string;
}

// 对话类型
export interface Conversation {
  id: string;
  platform: AIPlatform;
  title: string;
  messages: Message[];
  createdAt: number;
  updatedAt: number;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

// 设置类型
export interface Settings {
  syncEnabled: boolean;
  autoSync: boolean;
  platforms: Platform[];
  shortcuts: {
    openPopup: string;
    quickSync: string;
  };
  theme: 'light' | 'dark' | 'system';
  language: 'zh' | 'en';
}

// 同步设置
export interface SyncSettings {
  enabled: boolean;
  platforms: AIPlatform[];
  autoCapture: boolean;
  realTimeSync: boolean;
}

// 应用状态
export interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  syncSettings: SyncSettings;
  prompts: Prompt[];
  conversations: Conversation[];
  settings: Settings;
  isLoading: boolean;
  activeTab: string;
}


export interface ChromeMessage<T = any> {
  type: MessageType;
  payload: T;
  timestamp: number;
  tabId?: number;
}



export { MessageType };
