# FileSystemService 整合 SettingsStorage - 实现说明

## 更新时间
2025-10-07

## 需求背景

用户要求将 `FileSystemService` 的目录信息存储从纯 IndexedDB 方案改为整合 `SettingsStorage` 的方案：

- **之前**：DirectoryHandle 和所有元信息都存储在 IndexedDB
- **现在**：DirectoryHandle 存储在 IndexedDB，目录元信息存储在 SettingsStorage

## 实现方案

### 数据存储架构

#### 双存储策略

```
┌─────────────────────────┐
│  FileSystemService      │
└────────┬────────┬───────┘
         │        │
         ▼        ▼
┌─────────────┐  ┌─────────────────┐
│  IndexedDB  │  │ SettingsStorage │
│             │  │ (chrome.storage)│
└─────────────┘  └─────────────────┘
     │                   │
     ▼                   ▼
DirectoryHandle      目录元信息
(句柄对象)           (名称、路径)
```

#### 为什么需要双存储？

**IndexedDB：**
- ✅ 可以存储 `FileSystemDirectoryHandle` 对象（浏览器原生支持序列化）
- ✅ 本地存储，不会同步到其他设备
- ❌ 无法存储到 `chrome.storage.sync`（对象太大且不可 JSON 序列化）

**SettingsStorage (chrome.storage.sync)：**
- ✅ 跨设备同步配置信息
- ✅ 可以存储目录名称、路径等元信息
- ✅ 用于验证 DirectoryHandle 的有效性
- ❌ 无法存储 DirectoryHandle 对象

### 数据结构

#### DeviceConfig 接口更新

**文件：** `common/service/SettingsStorage.ts`

```typescript
export interface DeviceConfig {
  id: string              // 设备指纹ID
  name: string            // 设备显示名称 
  icon: string            // 设备图标
  path: string            // 导出路径（用于显示）
  directoryName?: string  // ✨ 新增：选择的目录名称（用于验证）
  lastActive: number      // 最后活跃时间戳
}
```

**新增字段说明：**
- `directoryName`: 用户选择的目录名称（如 `"MyVault"`）
- 用途：验证 IndexedDB 中的 DirectoryHandle 是否与配置匹配

#### ContentSettingsService 新增方法

**文件：** `content/service/ContentSettingsService.ts`

```typescript
export class ContentSettingsService {
  /**
   * 保存目录信息到设置
   * @param platform 笔记平台 ('obsidian' | 'notion' | 'markdown')
   * @param directoryName 目录名称
   * @param displayPath 用于显示的路径（可选）
   */
  async saveDirectoryInfo(
    platform: NotePlatform, 
    directoryName: string,
    displayPath?: string
  ): Promise<void>

  /**
   * 获取保存的目录名称
   * @param platform 笔记平台
   * @returns 目录名称，如果未配置则返回 null
   */
  async getDirectoryName(platform: NotePlatform): Promise<string | null>
}
```

### FileSystemService 实现细节

#### 1. 保存目录句柄（双写）

```typescript
private async saveDirectoryHandle(handle: FileSystemDirectoryHandle): Promise<void> {
  // 1️⃣ 保存到 IndexedDB（DirectoryHandle 对象）
  await new Promise<void>((resolve, reject) => {
    const transaction = this.db!.transaction([STORE_NAME], 'readwrite')
    const store = transaction.objectStore(STORE_NAME)
    const request = store.put(handle, HANDLE_KEY)
    
    request.onsuccess = () => resolve()
    request.onerror = () => reject(request.error)
  })

  // 2️⃣ 保存到 SettingsStorage（目录信息）
  await contentSettingsService.saveDirectoryInfo(
    'obsidian',
    handle.name,  // 目录名称
    handle.name   // 显示路径
  )
}
```

**流程：**
```
用户选择目录
    ↓
获取 DirectoryHandle
    ↓
┌─────────────────────────┐
│ IndexedDB               │  存储：DirectoryHandle 对象
│ Key: 'obsidian_directory'│
│ Value: FileSystemDirectoryHandle
└─────────────────────────┘
    ↓
┌─────────────────────────┐
│ SettingsStorage         │  存储：目录元信息
│ platforms.obsidian.devices[deviceId]
│ {
│   directoryName: "MyVault"
│   path: "MyVault"
│ }
└─────────────────────────┘
```

#### 2. 加载目录句柄（验证）

```typescript
private async loadDirectoryHandle(): Promise<FileSystemDirectoryHandle | null> {
  // 1️⃣ 从 IndexedDB 加载 DirectoryHandle
  const handle = await new Promise<FileSystemDirectoryHandle | null>((resolve) => {
    const transaction = this.db!.transaction([STORE_NAME], 'readonly')
    const store = transaction.objectStore(STORE_NAME)
    const request = store.get(HANDLE_KEY)
    
    request.onsuccess = () => resolve(request.result || null)
  })

  if (!handle) return null

  // 2️⃣ 从 SettingsStorage 获取保存的目录名称进行验证
  const savedDirectoryName = await contentSettingsService.getDirectoryName('obsidian')
  
  if (savedDirectoryName && savedDirectoryName !== handle.name) {
    console.warn('目录名称不匹配，清除旧 handle')
    await this.clearDirectoryHandle()
    return null
  }

  return handle
}
```

**验证流程：**
```
loadDirectoryHandle()
    ↓
从 IndexedDB 读取
    ↓
handle.name === ❓
    ↓
从 SettingsStorage 读取
    ↓
savedDirectoryName === ❓
    ↓
┌─────────────────┐
│ 名称匹配？      │
└────┬─────┬──────┘
     │     │
   YES    NO
     │     │
     ↓     ↓
  返回   清除并
 handle  返回null
```

#### 3. 检查目录访问权限（双验证）

```typescript
async hasDirectoryAccess(): Promise<boolean> {
  // 1️⃣ 检查 SettingsStorage 中是否有配置
  const directoryName = await contentSettingsService.getDirectoryName('obsidian')
  if (!directoryName) return false

  // 2️⃣ 尝试加载并验证 DirectoryHandle
  const savedHandle = await this.loadDirectoryHandle()
  if (!savedHandle) return false

  // 3️⃣ 检查权限状态
  const permission = await savedHandle.queryPermission({ mode: 'readwrite' })
  return permission === 'granted'
}
```

**检查流程：**
```
hasDirectoryAccess()
    ↓
SettingsStorage 有配置？
    │
   NO → false
    │
   YES
    ↓
IndexedDB 有 handle？
    │
   NO → false
    │
   YES
    ↓
权限 === 'granted'？
    │
  YES → true
   NO → false
```

## 优势分析

### 1. 数据一致性

**问题场景：** 用户在多设备间切换，或清除了浏览器数据

**解决方案：**
- SettingsStorage 中记录了用户选择的目录信息
- 加载时验证 IndexedDB 中的 handle 是否与配置匹配
- 不匹配时自动清除，提示用户重新选择

### 2. 跨设备配置同步

**场景：** 用户在设备 A 配置了 Obsidian 路径

```
设备 A:
  SettingsStorage (同步)
    platforms.obsidian.devices[A]
      directoryName: "MyVault"
      path: "D:\\Obsidian\\MyVault"
  
  IndexedDB (本地)
    DirectoryHandle → D:\\Obsidian\\MyVault

同步到设备 B:
  SettingsStorage (自动同步) ✅
    platforms.obsidian.devices[A]  # 显示设备A的配置
    platforms.obsidian.devices[B]  # 设备B自己的配置
  
  IndexedDB (不同步) ❌
    空（需要设备B用户重新选择）
```

**好处：**
- 用户可以在设备 B 看到设备 A 的配置
- 明确知道需要在设备 B 选择相应的目录
- 避免跨设备的 DirectoryHandle 冲突

### 3. 配置信息展示

**场景：** 在 Options 页面显示配置状态

```typescript
// 可以直接从 SettingsStorage 读取显示
const config = await settingsStorage.getCurrentDeviceConfig('obsidian')

if (config) {
  显示：
    目录名称: config.directoryName
    路径: config.path
    最后活跃: config.lastActive
    状态: hasDirectoryAccess() ? '已授权' : '需重新授权'
}
```

## 日志输出

### 首次选择目录

```
[FileSystemService] 请求用户选择目录
[FileSystemService] 用户已选择目录: MyVault
[FileSystemService] DirectoryHandle 已保存到 IndexedDB
[FileSystemService] 目录信息已保存到 SettingsStorage: MyVault
[ContentSettingsService] 保存目录信息: obsidian, MyVault
```

### 后续使用（加载）

```
[FileSystemService] 检查权限
[ContentSettingsService] 获取目录名称: obsidian
[FileSystemService] 从 IndexedDB 加载 DirectoryHandle
[FileSystemService] DirectoryHandle 验证通过: MyVault
[FileSystemService] 权限检查结果: {
  directoryName: "MyVault",
  handleName: "MyVault",
  permission: "granted",
  hasPermission: true
}
```

### 目录不匹配（需重新选择）

```
[FileSystemService] 从 IndexedDB 加载 DirectoryHandle
[ContentSettingsService] 获取目录名称: obsidian → "MyVault"
[FileSystemService] 目录名称不匹配: {
  saved: "MyVault",
  current: "OtherVault"
}
[FileSystemService] 清除旧 handle
[FileSystemService] 需要用户选择新目录
```

## 测试验证

### 测试用例 1：首次配置

**步骤：**
1. 用户首次点击导出
2. 选择目录 `MyVault`
3. 授予权限

**预期：**
- IndexedDB 中保存了 DirectoryHandle
- SettingsStorage 中保存了 `{ directoryName: "MyVault", path: "MyVault" }`
- 后续使用无需再选择

### 测试用例 2：刷新页面

**步骤：**
1. 刷新页面
2. 再次点击导出

**预期：**
- 自动从 IndexedDB 加载 DirectoryHandle
- 验证目录名称匹配
- 直接导出，无需再选择

### 测试用例 3：清除数据后

**步骤：**
1. 清除浏览器 IndexedDB
2. 点击导出

**预期：**
- `loadDirectoryHandle()` 返回 null
- SettingsStorage 中仍有配置（显示在 UI）
- 提示用户重新选择目录

### 测试用例 4：跨设备同步

**步骤：**
1. 设备 A 配置 Obsidian 路径
2. 在设备 B 打开扩展

**预期：**
- 设备 B 的 SettingsStorage 同步了设备 A 的配置
- 设备 B 可以看到设备 A 的目录名称
- 设备 B 需要自己选择目录（IndexedDB 不同步）

## 修改文件清单

### 1. SettingsStorage.ts
- ✅ 添加 `directoryName?` 字段到 `DeviceConfig` 接口

### 2. ContentSettingsService.ts
- ✅ 添加 `saveDirectoryInfo()` 方法
- ✅ 添加 `getDirectoryName()` 方法
- ✅ 添加 `getDeviceIcon()` 私有方法

### 3. FileSystemService.ts
- ✅ 导入 `contentSettingsService`
- ✅ 修改 `saveDirectoryHandle()` - 双写到 IndexedDB 和 SettingsStorage
- ✅ 修改 `loadDirectoryHandle()` - 加载并验证目录名称
- ✅ 修改 `hasDirectoryAccess()` - 双验证（SettingsStorage + IndexedDB）
- ✅ 保留 IndexedDB 逻辑（DirectoryHandle 必须存储在这里）

## 总结

### 核心改进

1. **数据分层存储**
   - IndexedDB：存储不可序列化的 DirectoryHandle 对象
   - SettingsStorage：存储可同步的目录元信息

2. **验证机制**
   - 加载时验证目录名称是否匹配
   - 不匹配时自动清除，避免使用错误的 handle

3. **跨设备支持**
   - 配置信息跨设备同步
   - 每个设备独立授权目录访问

### 最佳实践

✅ **DO：**
- 使用 IndexedDB 存储 DirectoryHandle
- 使用 SettingsStorage 存储元信息
- 加载时验证数据一致性
- 提供清晰的日志输出

❌ **DON'T：**
- 不要尝试将 DirectoryHandle 存储到 chrome.storage
- 不要跨设备共享 DirectoryHandle
- 不要忽略验证步骤

### 用户体验

**首次使用：**
```
选择目录 → 授予权限 → 保存配置 → 完成 ✅
```

**后续使用：**
```
自动加载 → 验证匹配 → 直接使用 ✅
```

**数据清除后：**
```
检测到无 handle → 提示重新选择 → 用户操作 ✅
```

现在系统既支持本地的 DirectoryHandle 持久化，又支持跨设备的配置信息同步！🎉
