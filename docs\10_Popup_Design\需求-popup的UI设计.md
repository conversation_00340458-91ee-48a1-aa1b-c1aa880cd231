# AI Sync Plugin - Popup UI 设计规范

## 1. 概述

### 1.1 产品定位
AI Sync Plugin 是一款智能AI对话捕获与知识管理插件，通过智能悬浮小球一键捕获任意AI平台对话，自动分类整理并沉淀为个人知识库。

### 1.2 设计目标
- **用户友好**：为普通用户提供简洁直观的操作界面
- **功能完整**：在popup中完成核心功能操作，减少页面跳转
- **商业导向**：清晰展示付费增值功能，引导用户升级
- **品牌一致**：与landing页面保持统一的视觉风格

### 1.3 技术约束
- Popup尺寸：400px × 600px（标准模式）
- 支持全屏模式：800px × 700px（可调整）
- 响应式设计，适配不同显示模式

## 2. 设计系统

### 2.1 色彩规范
基于landing页面的色彩系统，确保品牌一致性：

```css
/* 主色系 */
--blue-600: #2563eb;    /* 主品牌色 */
--blue-500: #3b82f6;    /* 主色变体 */
--blue-50: #eff6ff;     /* 浅色背景 */

/* 辅助色系 */
--purple-600: #7c3aed;  /* 紫色辅助 */
--purple-500: #8b5cf6;  /* 紫色变体 */

/* 背景色系 */
--bg-start: #eff6ff;    /* 渐变起始 */
--bg-end: #f5f3ff;      /* 渐变结束 */
--card-bg: #ffffff;     /* 卡片背景 */

/* 文字色系 */
--text-primary: #0f172a;  /* 主文字 */
--text-muted: #6b7280;    /* 次要文字 */

/* 功能色系 */
--glass: rgba(255,255,255,0.85);  /* 玻璃效果 */
--success: #10b981;     /* 成功状态 */
--warning: #f59e0b;     /* 警告状态 */
--error: #ef4444;       /* 错误状态 */
```

### 2.2 字体规范
```css
--font-family: "Inter", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial;
--font-size-xs: 0.75rem;    /* 12px */
--font-size-sm: 0.875rem;   /* 14px */
--font-size-base: 1rem;     /* 16px */
--font-size-lg: 1.125rem;   /* 18px */
--font-size-xl: 1.25rem;    /* 20px */
--font-weight-normal: 400;
--font-weight-medium: 600;
--font-weight-bold: 700;
--font-weight-extrabold: 800;
```

### 2.3 间距与圆角
```css
--spacing-xs: 4px;
--spacing-sm: 8px;
--spacing-md: 12px;
--spacing-lg: 16px;
--spacing-xl: 20px;
--spacing-2xl: 24px;

--radius-sm: 8px;
--radius-md: 12px;
--radius-lg: 16px;
--radius-full: 50%;
```

### 2.4 阴影系统
```css
--shadow-sm: 0 2px 8px rgba(15, 23, 42, 0.04);
--shadow-md: 0 6px 20px rgba(15, 23, 42, 0.06);
--shadow-lg: 0 10px 30px rgba(15, 23, 42, 0.08);
--shadow-xl: 0 18px 50px rgba(37, 99, 235, 0.12);
```

## 3. 布局架构

### 3.1 整体布局结构
采用侧边栏导航 + 主内容区的经典布局，确保功能清晰分层：

```
┌─────────────────────────────────────┐
│      [头像][状态标签]            [⛶] │  ← 全屏切换按钮
├──────────┬──────────────────────────┤
│          │                          │
│  左侧导航  │      主内容区域            │
│  (120px) │      (280px)            │
│          │                          │
│  [首页]   │  ┌─ 悬浮球控制 ─┐       │
│  [会员]   │  │ 🔮 状态开关    │       │
│  [设置]   │  │ 📊 同步进度   │       │
│  [历史]   │  └─────────────────┘       │
│  [账单]   │                          │
│  [支持]   │                          │
│  [手册]   │                          │
│          │  ┌─ 功能卡片 ─┐          │
│          │  │ • 快速操作   │          │
│          │  │ • 数据统计   │          │
│          │  └─────────────────┘          │
│          │                          │
│          │  ┌─ 状态信息 ─┐          │
│          │  │ 套餐状态     │          │
│          │  │ 设备信息     │          │
│          │  └─────────────────┘          │
└──────────┴──────────────────────────┘
```

### 3.2 响应式适配
支持两种显示模式，保持功能一致性：

```
Popup模式 (400×600):          全屏模式 (800×700):
┌─────────────────┐          ┌────────────────────────────┐
│ 左导航│主内容区域 │          │ 左导航 │    主内容区域       │
│(120) │ (280)   │          │ (200)  │    (600)          │
└─────────────────┘          └────────────────────────────┘
```

## 4. 用户状态系统

### 4.1 用户状态分层
基于landing页面的付费体系，设计5层用户状态：

| 状态 | 存储容量 | 设备同步 | 核心功能 | 月费 | 年费 |
|------|----------|----------|----------|------|------|
| **未登录** | - | - | 仅展示功能 | - | - |
| **Free** | 100条 | 2台设备 | 基础捕获+分类 | 免费 | 免费 |
| **Pro** | 3000条 | 无限设备 | AI分类+导出+报告 | $9.9 | $99 |
| **Plus** | 10000条 | 无限设备 | 知识图谱+高级分析 | $14.9 | $149 |
| **Max** | 50000条 | 无限设备 | 永久买断+本地存储 | - | $249 |

### 4.2 状态视觉标识
每种用户状态都有独特的视觉标识，在Header区域清晰展示：

```
┌─────────────────────────────────────┐
│      [头像][状态标签]            [⛶] │
└─────────────────────────────────────┘
```

#### 状态标签设计
| 用户类型 | 头像样式 | 状态标签 | 图标 | 主题色 |
|---------|----------|----------|------|--------|
| **未登录** | 默认灰色头像 | "未登录" | 🚫 | #9CA3AF |
| **Free** | 用户头像 | "FREE" | 🆓 | #10B981 |
| **Pro** | 用户头像+蓝色边框 | "PRO" | 👑 | #3B82F6 |
| **Plus** | 用户头像+紫色边框 | "PLUS" | 💎 | #8B5CF6 |
| **Max** | 用户头像+金色边框 | "MAX" | ⭐ | #F59E0B |

## 5. 核心功能模块

### 5.1 悬浮球控制模块
作为插件的核心功能，悬浮球控制需要在首页突出展示：

```
┌─────────────────────────────────────┐
│           🔮 悬浮球控制              │
├─────────────────────────────────────┤
│ 🔘 悬浮球开关    [一键开启/关闭]      │
│ 📋 当前状态: 已开启                 │
│ ⚡ 支持平台: ChatGPT、Claude、DeepSeek │
│ 🎯 今日捕获: 15条对话               │
└─────────────────────────────────────┘
```

### 5.2 同步进度模块
根据用户状态显示不同的同步功能：

#### Free用户视图
```
┌─────────────────────────────────────┐
│           📊 同步进度 🔒            │
├─────────────────────────────────────┤
│ ████████░░░░░░░░░░ 40/100条          │
│ 升级到Pro解锁实时同步 [升级按钮]      │
│ 🎁 7天Pro试用剩余: 3天              │
└─────────────────────────────────────┘
```

#### 付费用户视图
```
┌─────────────────────────────────────┐
│           📊 同步进度 ✅            │
├─────────────────────────────────────┤
│ ████████████████████ 2847/3000条    │
│ 最后同步: 2分钟前                   │
│ 同步状态: 所有平台已同步             │
│ [立即同步] [查看详情]               │
└─────────────────────────────────────┘
```

### 5.3 快捷操作模块
提供常用功能的快速访问：

```
┌─────────────────────────────────────┐
│           ⚡ 快捷操作               │
├─────────────────────────────────────┤
│ [🔄 一键同步] [📋 查看历史] [📊 统计] │
│ [📤 导出数据] [⚙️ 设置] [❓ 帮助]    │
└─────────────────────────────────────┘
```

### 5.4 升级引导模块
针对Free用户显示升级提示：

```
┌─────────────────────────────────────┐
│           🚀 升级到Pro              │
├─────────────────────────────────────┤
│ ✨ 首月仅需 $2.97（原价$9.9）       │
│ 🎯 解锁3000条存储 + AI智能分类       │
│ 📤 支持导出到Notion/Obsidian        │
│ 📊 获取详细分析报告                 │
│                                     │
│ [立即升级] [了解更多]               │
└─────────────────────────────────────┘
```

## 6. 页面设计详情

### 6.1 首页 (HomePage)
首页作为用户的主要操作界面，需要展示核心功能和状态信息：

```
┌─────────────────────────────────────┐
│ 🔮 悬浮球控制     [一键开启/关闭]    │
│ 📋 当前状态: 已开启                 │
│ 🎯 今日捕获: 15条对话               │
├─────────────────────────────────────┤
│ 📊 同步进度                         │
│ ████████████████████ 2847/3000条    │
│ 最后同步: 2分钟前                   │
├─────────────────────────────────────┤
│ 📈 今日统计                         │
│ • 新增对话: 15条                    │
│ • 活跃平台: ChatGPT, Claude         │
│ • 生成标签: 8个                     │
├─────────────────────────────────────┤
│ ⚡ 快捷操作                         │
│ [🔄 同步] [📋 历史] [📊 统计] [⚙️ 设置] │
└─────────────────────────────────────┘
```

### 6.2 会员页 (MembershipPage)
展示用户当前套餐状态和升级选项：

```
┌─────────────────────────────────────┐
│ 👑 当前套餐: Pro                    │
│ 📅 到期时间: 2025-02-15             │
│ 💾 存储使用: 2847/3000条 (94.9%)    │
├─────────────────────────────────────┤
│ 🚀 升级选项                         │
│ ┌─ Plus套餐 ─┐ ┌─ Max买断 ─┐      │
│ │ $14.9/月   │ │ $249一次性 │      │
│ │ 10000条存储│ │ 50000条存储│      │
│ │ 知识图谱   │ │ 永久使用   │      │
│ │ [升级]     │ │ [购买]     │      │
│ └───────────┘ └───────────┘      │
├─────────────────────────────────────┤
│ 📊 使用统计                         │
│ • 本月捕获: 456条                   │
│ • 生成标签: 89个                    │
│ • 导出次数: 12次                    │
└─────────────────────────────────────┘
```

### 6.3 设置页 (SettingsPage)
采用tab切换方式配置不同平台的导出路径：

```
┌─────────────────────────────────────┐
│ 🖥️ 当前设备信息                     │
│ Windows-DESKTOP-HAMMER-7F8A9B2C     │
├─────────────────────────────────────┤
│ 📂 平台导出配置                     │
│ ┌─────────────────────────────────┐ │
│ │ [🔵 Obsidian] [Notion] [Markdown] │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 💻 Windows-DESKTOP-HAMMER (当前)    │
│ 导出路径:                           │
│ [C:\Users\<USER>\Documents\Obsidian] │
│                          [浏览...]   │
│                                     │
│ 📱 iOS-iPhone-Pro                   │
│ 导出路径: [未配置] [设置]           │
│                                     │
│ 💻 macOS-MacBook-Air                │
│ 导出路径:                           │
│ [/Users/<USER>/Documents/Obsidian]    │
│                          [浏览...] ⚙️ │
│                                     │
│ 🖥️ Linux-Ubuntu                     │
│ 导出路径: [未配置] [设置]           │
└─────────────────────────────────────┘
```

#### 设计说明
- **Tab切换**：顶部横向tab选择平台（Obsidian、Notion、Markdown）
- **设备列表**：选择平台后，下方竖向显示所有设备配置
- **当前设备**：突出显示当前设备，可直接编辑
- **手动导出**：所有导出操作均为手动触发
- **路径独立**：每个平台在每个设备上都有独立的导出路径

### 6.4 历史页 (HistoryPage)
展示用户的对话历史和搜索功能：

```
┌─────────────────────────────────────┐
│ 🔍 [搜索对话内容...]               │
│ 🏷️ 标签筛选: [全部] [工作] [学习]    │
│ 📅 时间筛选: [今天] [本周] [本月]    │
├─────────────────────────────────────┤
│ 📋 对话列表                         │
│ ┌─ 今天 ─┐                         │
│ │ 🤖 ChatGPT - 15:30               │
│ │ "如何优化React性能..."            │
│ │ 🏷️ 前端开发 📊 已同步             │
│ └─────────────────────────────────┘ │
│ ┌─ 昨天 ─┐                         │
│ │ 🤖 Claude - 09:15                │
│ │ "写一个Python爬虫..."            │
│ │ 🏷️ Python 📊 已同步              │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 6.5 账单页 (BillingPage)
管理订阅、支付历史和发票下载：

```
┌─────────────────────────────────────┐
│ 💳 当前订阅                         │
│ Pro套餐 - $9.9/月                   │
│ 📅 下次扣费: 2025-02-15             │
│ 💳 支付方式: **** 1234              │
│                          [更改]     │
├─────────────────────────────────────┤
│ 📋 订阅管理                         │
│ [升级到Plus] [升级到Max] [取消订阅]  │
├─────────────────────────────────────┤
│ 🧾 支付历史                         │
│ ┌─ 2025-01-15 ─┐                   │
│ │ Pro套餐 - $9.9                   │
│ │ 支付成功 [下载发票]               │
│ └─────────────────────────────────┘ │
│ ┌─ 2024-12-15 ─┐                   │
│ │ Pro套餐 - $9.9                   │
│ │ 支付成功 [下载发票]               │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ 💳 支付方式管理                     │
│ 💳 Visa **** 1234 (默认)           │
│ 📅 过期时间: 12/27                  │
│ [编辑] [删除] [设为默认]            │
│                                     │
│ [+ 添加新的支付方式]                │
└─────────────────────────────────────┘
```

### 6.6 支持页 (SupportPage)
提供客户支持和反馈渠道：

```
┌─────────────────────────────────────┐
│ 📧 联系我们                         │
│ 客服邮箱: <EMAIL>        │
│ 工作时间: 周一至周五 9:00-18:00      │
│                                     │
│ [发送邮件] [查看帮助文档]            │
├─────────────────────────────────────┤
│ 💬 留言反馈                         │
│ ┌─ 反馈类型 ─┐                     │
│ │ [Bug报告] [功能建议] [使用问题]   │
│ └─────────────────────────────────┘ │
│                                     │
│ 📝 详细描述:                        │
│ ┌─────────────────────────────────┐ │
│ │                                 │ │
│ │ 请详细描述您遇到的问题或建议...   │ │
│ │                                 │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 📧 联系邮箱:                        │
│ [<EMAIL>]                  │
│                                     │
│ [提交反馈]                          │
├─────────────────────────────────────┤
│ 📚 快速帮助                         │
│ • 如何开始使用悬浮球？              │
│ • 同步失败怎么办？                  │
│ • 如何取消订阅？                    │
│ • 数据安全保障说明                  │
│                                     │
│ [查看完整帮助文档]                  │
└─────────────────────────────────────┘
```

### 6.7 手册页 (ManualPage)
提供使用指南和帮助信息：

```
┌─────────────────────────────────────┐
│ 📖 使用指南                         │
├─────────────────────────────────────┤
│ 🚀 快速开始                         │
│ • 安装插件后自动显示悬浮球           │
│ • 在AI平台点击悬浮球捕获对话         │
│ • 系统自动分类和同步                │
├─────────────────────────────────────┤
│ 🔮 悬浮球使用                       │
│ • 支持ChatGPT、Claude、DeepSeek     │
│ • 一键捕获完整对话内容              │
│ • 自动识别问题和回答                │
├─────────────────────────────────────┤
│ 📤 导出功能                         │
│ • 支持Obsidian、Notion、Markdown    │
│ • 自动生成标签和分类                │
│ • 可设置自动导出频率                │
├─────────────────────────────────────┤
│ 🆘 常见问题                         │
│ • 悬浮球不显示怎么办？              │
│ • 如何设置导出路径？                │
│ • 多设备同步如何配置？              │
└─────────────────────────────────────┘
```
## 7. 交互设计规范

### 7.1 按钮设计
基于landing页面的按钮样式，确保一致性：

```css
/* 主要按钮 */
.btn-primary {
  background: linear-gradient(90deg, var(--blue-600), var(--blue-500));
  color: white;
  padding: 10px 18px;
  border-radius: 12px;
  font-weight: 700;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.18);
}

/* 次要按钮 */
.btn-secondary {
  background: transparent;
  color: var(--blue-600);
  border: 1px solid rgba(37, 99, 235, 0.12);
  padding: 10px 18px;
  border-radius: 12px;
  font-weight: 600;
}

/* 紫色按钮（Plus功能） */
.btn-purple {
  background: linear-gradient(90deg, var(--purple-600), var(--purple-500));
  color: white;
  padding: 10px 18px;
  border-radius: 12px;
  font-weight: 700;
}
```

### 7.2 状态指示器
不同状态使用不同的视觉反馈：

```css
/* 成功状态 */
.status-success {
  color: var(--success);
  background: rgba(16, 185, 129, 0.1);
}

/* 警告状态 */
.status-warning {
  color: var(--warning);
  background: rgba(245, 158, 11, 0.1);
}

/* 错误状态 */
.status-error {
  color: var(--error);
  background: rgba(239, 68, 68, 0.1);
}
```

### 7.3 动画效果
为提升用户体验，添加适当的动画效果：

```css
/* 悬浮球动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* 进度条动画 */
@keyframes progress {
  0% { width: 0%; }
  100% { width: var(--progress-width); }
}

/* 淡入动画 */
@keyframes fadeIn {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}
```

## 8. 全屏模式设计

### 8.1 全屏切换功能
在Header右上角提供全屏切换按钮：

- **图标**：⛶ (展开) / ⛉ (收缩)
- **功能**：popup ↔ 全屏窗口切换
- **快捷键**：Ctrl+F (可选)

### 8.2 全屏布局适配
全屏模式下保持相同的功能布局，仅调整尺寸比例：

```
全屏模式 (100vw × 100vh):
┌────────────────────────────────────────────────────────────────┐
│      [头像][状态标签]                                        [⛉] │
├──────────────┬─────────────────────────────────────────────────┤
│              │                                                 │
│   左侧导航     │              主内容区域                          │
│   (200px)    │           (100vw - 200px)                      │
│              │                                                 │
│   [🏠 首页]   │   ┌─ 悬浮球控制 ─┐                             │
│   [👑 会员]   │   │ 🔮 状态开关    │                             │
│   [⚙️ 设置]   │   │ 📊 同步进度   │                             │
│   [📋 历史]   │   └─────────────────┘                             │
│   [💳 账单]   │                                                 │
│   [🆘 支持]   │   ┌─ 功能卡片 ─┐                               │
│   [📖 手册]   │   │ 充分利用屏幕空间 │                               │
│              │   │ 更舒适的操作体验 │                               │
│              │   └─────────────────┘                               │
└──────────────┴─────────────────────────────────────────────────┘
```

### 全屏模式特点
- **真正全屏**：100vw × 100vh，铺满整个浏览器窗口
- **固定定位**：position: fixed，覆盖整个页面
- **侧边栏扩展**：从120px扩展到200px，显示更多信息
- **主内容区最大化**：充分利用屏幕空间
- **无边框圆角**：全屏时移除圆角，完全贴合窗口边缘



