import { Singleton } from "../base/Singleton"
import { MessageType } from "../types/enums"
import { MessagingService } from "./MessagingService"

/**
 * 文件保存代理服务
 * 负责通过 Background Script 保存文件到本地
 */
export class SaveFileProxy extends Singleton<SaveFileProxy> {

  /**
   * 保存文件到本地路径
   * @param content 文件内容
   * @param filename 文件名
   * @param exportPath 导出路径
   * @returns 下载 ID
   */
  async saveFileToPath(
    content: string,
    filename: string,
    exportPath: string
  ): Promise<{ success: boolean; downloadId?: number; error?: string }> {
    try {
      console.log('[SaveFileProxy] 发送文件保存请求', {
        filename,
        exportPath,
        contentLength: content.length
      })

      const response = await MessagingService.sendToBackground(
        MessageType.OBSIDIAN_DOWNLOAD_FILE,
        {
          content,
          filename,
          exportPath
        }
      )

      if (response.success) {
        console.log('[SaveFileProxy] 文件保存成功', {
          downloadId: response.downloadId,
          filename
        })
      } else {
        console.error('[SaveFileProxy] 文件保存失败', response.error)
      }

      return response
    } catch (error) {
      console.error('[SaveFileProxy] 文件保存异常', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'File save failed'
      }
    }
  }
}

// 导出单例实例
export const saveFileProxy = SaveFileProxy.getInstance()
