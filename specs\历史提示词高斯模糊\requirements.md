# 需求文档

## 介绍

优化 HistoryBubble 组件的用户体验，将当前的悬浮气泡形式改为居中模态页形式，增加高斯模糊背景和复制功能，提升视觉效果和交互体验。

## 需求

### 需求 1 - 模态页显示

**用户故事：** 作为用户，当我鼠标悬浮在历史提示词小球上时，我希望看到一个居中的模态页，而不是当前的悬浮气泡，这样能更好地聚焦内容。

#### 验收标准

1. When 鼠标悬浮在历史提示词小球上时，系统应当显示一个居中的模态页，而不是当前的悬浮气泡形式。
2. When 模态页显示时，系统应当将页面背景进行高斯模糊处理，突出模态页内容。
3. When 模态页显示时，系统应当只显示模态页和历史提示词列表，其他页面内容应当被模糊处理。

### 需求 2 - 复制功能

**用户故事：** 作为用户，当我点击某个提示词时，我希望该提示词能自动复制到剪贴板，并在点击位置附近显示确认提示。

#### 验收标准

1. When 用户点击某个提示词时，系统应当将该提示词内容复制到剪贴板。
2. When 提示词复制成功后，系统应当在点击区域旁边弹出toast提示"已复制到剪贴板"。
3. When toast提示显示后，系统应当在3秒后自动隐藏toast提示。

### 需求 3 - 关闭交互

**用户故事：** 作为用户，我希望能够通过点击空白处或点击提示词来关闭历史提示词页面，提供灵活的退出方式。

#### 验收标准

1. When 用户点击模态页外的空白区域时，系统应当关闭历史提示词模态页。
2. When 用户点击某个提示词后，系统应当在复制完成后自动关闭历史提示词模态页。
3. When 模态页关闭时，系统应当移除背景高斯模糊效果，恢复正常页面显示。

### 需求 4 - 样式规范

**用户故事：** 作为开发者，我希望新的模态页组件遵循项目的UI规范，使用统一的技术栈和样式管理方式。

#### 验收标准

1. When 开发模态页组件时，系统应当使用 Tailwind CSS 3 + shadcn/ui 作为样式技术栈。
2. When 样式代码过多时，系统应当将CSS样式拆分到独立的 HistoryBubble.css 文件中。
3. When 实现组件时，系统应当遵守项目规则 `02-ui-components.md` 中的Content Script UI组件规范。
