import React, { useEffect, useRef } from 'react'
import { AlertTriangle, X } from 'lucide-react'

interface ConfirmDialogProps {
  isOpen: boolean
  title?: string
  message: string
  confirmText?: string
  cancelText?: string
  onConfirm: () => void
  onCancel: () => void
  position?: { x: number; y: number }
  variant?: 'danger' | 'warning' | 'info'
}

/**
 * 自定义确认对话框组件
 * 支持定位显示在指定位置附近
 */
export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  title = '确认操作',
  message,
  confirmText = '确认',
  cancelText = '取消',
  onConfirm,
  onCancel,
  position,
  variant = 'danger'
}) => {
  const dialogRef = useRef<HTMLDivElement>(null)

  // 处理ESC键关闭
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onCancel()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEsc)
      return () => document.removeEventListener('keydown', handleEsc)
    }
  }, [isOpen, onCancel])

  // 自动定位对话框
  useEffect(() => {
    if (isOpen && position && dialogRef.current) {
      const dialog = dialogRef.current
      const rect = dialog.getBoundingClientRect()
      
      // 计算最佳位置，避免超出视窗
      let left = position.x - rect.width / 2
      let top = position.y - rect.height - 10 // 显示在按钮上方

      // 边界检查
      const padding = 16
      if (left < padding) left = padding
      if (left + rect.width > window.innerWidth - padding) {
        left = window.innerWidth - rect.width - padding
      }
      if (top < padding) {
        top = position.y + 40 // 如果上方空间不够，显示在下方
      }

      dialog.style.left = `${left}px`
      dialog.style.top = `${top}px`
    }
  }, [isOpen, position])

  if (!isOpen) return null

  const variantStyles = {
    danger: {
      icon: 'text-red-500',
      confirmButton: 'bg-red-600 hover:bg-red-700 text-white',
      border: 'border-red-200'
    },
    warning: {
      icon: 'text-yellow-500',
      confirmButton: 'bg-yellow-600 hover:bg-yellow-700 text-white',
      border: 'border-yellow-200'
    },
    info: {
      icon: 'text-blue-500',
      confirmButton: 'bg-blue-600 hover:bg-blue-700 text-white',
      border: 'border-blue-200'
    }
  }

  const styles = variantStyles[variant]

  return (
    <>
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-25 z-40"
        onClick={onCancel}
      />
      
      {/* 对话框 */}
      <div
        ref={dialogRef}
        className={`
          fixed z-50 bg-white rounded-lg shadow-xl border-2 ${styles.border}
          min-w-80 max-w-md p-4
        `}
        style={position ? { position: 'fixed' } : undefined}
      >
        {/* 关闭按钮 */}
        <button
          onClick={onCancel}
          className="absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="w-4 h-4" />
        </button>

        {/* 内容 */}
        <div className="flex items-start gap-3">
          <AlertTriangle className={`w-6 h-6 flex-shrink-0 mt-0.5 ${styles.icon}`} />
          
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {title}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {message}
            </p>
            
            {/* 按钮组 */}
            <div className="flex items-center gap-2 justify-end">
              <button
                onClick={onCancel}
                className="
                  px-3 py-2 text-sm font-medium text-gray-700
                  bg-white border border-gray-300 rounded-md
                  hover:bg-gray-50 transition-colors
                "
              >
                {cancelText}
              </button>
              <button
                onClick={onConfirm}
                className={`
                  px-3 py-2 text-sm font-medium rounded-md
                  transition-colors ${styles.confirmButton}
                `}
              >
                {confirmText}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default ConfirmDialog
