# 简化KimiAnswerService逻辑 - 任务清单

## 项目概述
将Kimi答案捕获模块重构为Controller-Service分层架构，优化页面类型识别和内容提取机制。

## 任务进度总览
- **总任务数**: 18个
- **已完成**: 7个  
- **进行中**: 0个
- **待开始**: 11个

---

## 阶段一：项目准备和分析 (2个任务)

### 📋 T001: 分析现有代码结构
**状态**: ✅ 已完成  
**描述**: 深入分析现有KimiAnswerService及相关类的代码结构，了解当前实现细节  
**分析结果**: 
- **KimiAnswerService**: 604行代码，职责过重，包含页面监听、DOM操作、状态管理、错误处理等
- **KimiConversationManager**: 221行，负责对话数据管理，职责相对清晰
- **KimiPageDetector**: 33行，简单的页面检测，需要扩展URL识别
- **KimiSelectorManager**: 62行，选择器管理，功能完整
- **KimiClipboardManager**: 111行，剪贴板操作，需要集成新的内容提取机制
- **依赖关系**: KimiAnswerService依赖所有其他类，形成强耦合

### 📋 T002: 检查文件引用关系  
**状态**: ✅ 已完成  
**描述**: 查找所有引用Kimi相关类的文件，评估重命名影响范围  
**分析结果**:
- **主要影响文件**: `kimi.ts` - 引用了KimiAnswerService
- **内部依赖**: KimiAnswerService引用了所有其他Kimi类
- **无外部引用**: 其他模块没有直接引用Kimi具体类
- **重命名影响**: 低风险，主要在kimi模块内部

---

## 阶段二：文件重命名和基础重构 (5个任务)

### 📋 T003: 重命名KimiAnswerService为KimiAnswerController
**状态**: ✅ 已完成  
**描述**: 重命名主文件并调整类名，保持功能不变  
**完成情况**:
- 文件重命名: KimiAnswerService.ts → KimiAnswerController.ts ✅
- 类名更新: KimiAnswerService → KimiAnswerController ✅
- 日志和注释更新完成 ✅
- kimi.ts中的引用已更新 ✅

### 📋 T004: 重命名KimiConversationManager为KimiConversationService  
**状态**: ✅ 已完成  
**描述**: 重命名对话管理类  
**完成情况**:
- 文件重命名: KimiConversationManager.ts → KimiConversationService.ts ✅
- 类名更新: KimiConversationManager → KimiConversationService ✅
- KimiAnswerController中的引用和使用已更新 ✅
- 日志中的类名已更新 ✅

### 📋 T005: 重命名KimiPageDetector为KimiPageService
**状态**: ✅ 已完成  
**描述**: 重命名页面检测类并扩展URL识别功能  
**完成情况**:
- 文件重命名: KimiPageDetector.ts → KimiPageService.ts ✅
- 类名更新: KimiPageDetector → KimiPageService ✅
- 新增基于URL的页面类型识别方法 ✅
- 扩展PageState类型定义，添加chatId字段 ✅
- 实现了home页和chat页的URL模式匹配 ✅

### 📋 T006: 重命名KimiSelectorManager为KimiSelectorService
**状态**: ✅ 已完成  
**描述**: 重命名选择器管理类  
**完成情况**:
- 文件重命名: KimiSelectorManager.ts → KimiSelectorService.ts ✅
- 类名更新: KimiSelectorManager → KimiSelectorService ✅
- KimiAnswerController中的所有引用已更新 ✅
- 日志中的类名已更新 ✅

### 📋 T007: 重命名KimiClipboardManager为KimiClipboardService
**状态**: ✅ 已完成  
**描述**: 重命名剪贴板管理类并优化内容提取  
**完成情况**:
- 文件重命名: KimiClipboardManager.ts → KimiClipboardService.ts ✅
- 类名更新: KimiClipboardManager → KimiClipboardService ✅
- KimiAnswerController中的引用已更新 ✅
- 日志中的类名已更新 ✅

---

## 阶段三：Controller层重构 (4个任务)

### 📋 T008: 简化KimiAnswerController职责
**状态**: ⏳ 待开始  
**描述**: 将Controller改为流程编排角色，移除具体实现细节  
**验收标准**:
- Controller只保留流程控制逻辑
- 具体实现迁移到Service层
- 保持对外接口不变

### 📋 T009: 实现基于URL的页面类型判断
**状态**: ⏳ 待开始  
**描述**: 在Controller中集成URL判断逻辑，替换DOM检测  
**验收标准**:
- 正确识别home页和chat页
- URL模式匹配准确
- 保留DOM检测作为备选

### 📋 T010: 优化监听器生命周期管理
**状态**: ⏳ 待开始  
**描述**: 根据页面类型控制监听器的启动和停止  
**验收标准**:
- Home页不启动监听器
- Chat页正确启动所有监听
- 资源正确释放

### 📋 T011: 重构错误处理和日志机制
**状态**: ⏳ 待开始  
**描述**: 统一Controller层的错误处理和日志输出  
**验收标准**:
- 统一的错误处理策略
- 清晰的日志分级
- 便于调试的信息输出

---

## 阶段四：Service层实现优化 (5个任务)

### 📋 T012: 实现KimiAnswerService答案捕获逻辑
**状态**: ⏳ 待开始  
**描述**: 新建专门处理答案监听和捕获的Service类  
**验收标准**:
- MutationObserver管理
- 问答对匹配逻辑
- 答案完成状态检测

### 📋 T013: 优化KimiClipboardService内容提取
**状态**: ⏳ 待开始  
**描述**: 实现新的基于剪贴板的内容提取机制  
**验收标准**:
- segment-assistant-actions-content节点检测
- 模拟点击触发复制功能  
- 剪贴板内容获取和解析
- 异常处理和降级方案

### 📋 T014: 完善KimiConversationService数据管理
**状态**: ⏳ 待开始  
**描述**: 优化对话数据的组织和存储逻辑  
**验收标准**:
- 按对话标题分组
- 问答对正确关联
- 数据清理机制

### 📋 T015: 扩展KimiSelectorService选择器策略
**状态**: ⏳ 待开始  
**描述**: 添加新DOM结构的选择器支持  
**验收标准**:
- 支持新的问答DOM结构
- 答案完成状态检测选择器
- 多选择器兼容策略

### 📋 T016: 完善KimiPageService页面管理
**状态**: ⏳ 待开始  
**描述**: 实现完整的页面状态管理和URL解析  
**验收标准**:
- URL模式匹配
- 页面状态跟踪
- 页面跳转事件处理

---

## 阶段五：集成测试和优化 (2个任务)

### 📋 T017: 更新所有文件引用
**状态**: ⏳ 待开始  
**描述**: 更新项目中所有对重命名类的引用  
**验收标准**:
- 所有import语句正确更新
- 类型定义文件同步更新
- 无编译错误

### 📋 T018: 功能测试和性能验证
**状态**: ⏳ 待开始  
**描述**: 全面测试重构后的功能和性能表现  
**验收标准**:
- 所有原有功能正常工作
- Home页资源消耗降低
- Chat页监听功能正常
- 新的内容提取机制工作正常

---

## 任务状态说明
- ⏳ **待开始**: 任务尚未开始
- 🔄 **进行中**: 任务正在执行中  
- ✅ **已完成**: 任务已成功完成
- ❌ **失败**: 任务执行失败，需要重新处理
- ⚠️ **阻塞**: 任务被其他问题阻塞

## 风险和注意事项
1. **重命名风险**: 确保所有引用都正确更新
2. **功能兼容**: 重构过程中保持现有功能完整性
3. **测试覆盖**: 每个阶段完成后进行充分测试
4. **性能监控**: 关注重构对性能的影响

## 预计完成时间
- **总工期**: 4天
- **每日任务**: 4-5个任务
- **关键里程碑**: 每阶段完成后进行验证