---
type: "development_rules"
description: "Content Script模块总览和架构规则"
---

# Content Script总览

## 核心架构
- **BaseAIAdapter**: 平台配置载体
- **Capture**: 页面元素捕捉
- **Inject**: UI注入
- **Service**: 业务逻辑
- **Model**: 数据共享(单例)

## 目录结构
```
content/
├── adapters/    # 平台适配
├── capture/     # 页面捕捉
├── inject/      # UI注入
├── service/     # 业务服务
├── model/       # 数据模型
├── components/  # UI组件
└── index.ts     # 入口
```

## 核心原则(SOLID)
- **单一职责**: 每个模块专注一个功能
- **开闭原则**: 对扩展开放，对修改封闭
- **模块解耦**: 使用单例+事件通信
- **数据共享**: Model单例管理状态

## 开发规范
### 新增适配器
1. 继承BaseAIAdapter
2. 实现getSelectors()
3. 注册到Manager

### 新增模块
1. 确定类型(Capture/Inject/Service)
2. 使用单例模式
3. 实现生命周期方法
