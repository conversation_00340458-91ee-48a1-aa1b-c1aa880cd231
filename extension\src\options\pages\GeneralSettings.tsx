import React from 'react'
import { SettingSection } from '../components/SettingSection'
import { ToggleSwitch } from '../components/ToggleSwitch'
import { useSettings } from '../hooks/useSettings'

export const GeneralSettings: React.FC = () => {
  const { settings, updateSettings, loading } = useSettings()

  if (loading || !settings) {
    return <div className="animate-pulse">加载中...</div>
  }

  return (
    <div className="space-y-6">
      <SettingSection
        title="同步设置"
        description="配置提示词和对话的同步行为"
      >
        <ToggleSwitch
          label="启用同步"
          description="开启后将自动同步提示词到云端"
          checked={settings.syncEnabled}
          onChange={(checked) => updateSettings({ syncEnabled: checked })}
        />
        
        <ToggleSwitch
          label="自动同步"
          description="实时同步提示词变更"
          checked={settings.autoSync}
          onChange={(checked) => updateSettings({ autoSync: checked })}
          disabled={!settings.syncEnabled}
        />
      </SettingSection>

      <SettingSection
        title="数据管理"
        description="管理本地数据存储和历史记录"
      >
        <ToggleSwitch
          label="保存对话历史"
          description="在本地保存对话记录用于回顾"
          checked={settings.saveHistory}
          onChange={(checked) => updateSettings({ saveHistory: checked })}
        />
      </SettingSection>

      <SettingSection
        title="界面设置"
        description="个性化界面显示选项"
      >
        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-900">主题</label>
            <p className="text-sm text-gray-500">选择界面主题</p>
          </div>
          <select
            value={settings.theme}
            onChange={(e) => updateSettings({ theme: e.target.value as any })}
            className="mt-1 block w-32 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          >
            <option value="system">跟随系统</option>
            <option value="light">浅色</option>
            <option value="dark">深色</option>
          </select>
        </div>
      </SettingSection>
    </div>
  )
}