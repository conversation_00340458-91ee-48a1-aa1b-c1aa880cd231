import { chatPromptDao } from '../dao/ChatPromptDao'
import { chatHistoryDao } from '../dao/ChatHistoryDao'
import { platformDao } from '../dao/PlatformDao'
import { ChatPromptEntity, PlatformEntity } from '@/common/types/database_entity'
import { DatabaseResult, } from '@/common/types/comm_vo'
import { CreateChatPromptReq, CreateChatPromptResp, ChatPromptListResp, PlatformInfoVO } from '@/common/types/content_vo'
import { Singleton } from '../base'

/**
 * 提示词服务
 * 负责业务逻辑处理和Entity到DTO的转换
 */
export class ChatPromptService extends Singleton<ChatPromptService> {

  /**
   * 创建聊天记录（包含提示词和历史记录）
   */
  async createPrompt(input: CreateChatPromptReq): Promise<DatabaseResult<CreateChatPromptResp>> {
    try {
      const now = Date.now()
      let chatPrompt: ChatPromptEntity
      let promptUid: string = input.prompt_uid
      // 检查是否提供了prompt_uid
      if (!promptUid) {
        return { success: false, error: 'prompt_uid is required' }
      }

      // 检查prompt_uid是否存在
      const existingPrompt: ChatPromptEntity | null = await chatPromptDao.findByPromptUid(promptUid)
      if (existingPrompt) {
        chatPrompt = existingPrompt
        // 更新平台列表
        const platformIds = new Set(existingPrompt.platform_ids.split(','))
        platformIds.add(input.platform_id.toString())
        await chatPromptDao.update(existingPrompt.id!, {
          platform_ids: Array.from(platformIds).join(',')
        })
      } else {
        // 创建新的提示词记录，使用提供的prompt_uid
        chatPrompt = await chatPromptDao.create({
          chat_prompt: input.chat_prompt,
          prompt_uid: promptUid,
          platform_ids: input.platform_id.toString(),
          create_time: now,
          tags: input.tags,
          is_delete: 0,
          is_synced: 0
        })
      }


      return {
        success: true,
        data: {
          chatPrompt
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
    * 获得提示词列表(带平台标志)
    */
  async getChatPromptList(params: {
    limit?: number
    order_direction?: 'ASC' | 'DESC'
  } = {}): Promise<DatabaseResult<ChatPromptListResp[]>> {
    try {
      const { limit = 20, order_direction = 'DESC' } = params

      // 获取最近的提示词记录
      const prompts = await chatPromptDao.findAll({
        limit,
        orderDirection: order_direction
      })

      // 预加载所有平台信息到Map中，避免循环查询
      const allPlatforms = await platformDao.findAllActive()
      const platformMap = new Map<number, PlatformEntity>()
      allPlatforms.forEach(platform => {
        if (platform.id) {
          platformMap.set(platform.id, platform)
        }
      })

      // 转换为响应DTO
      const result: ChatPromptListResp[] = prompts.map(prompt => {
        const platformInfo: PlatformInfoVO[] = []

        // 解析platform_ids字段
        if (prompt.platform_ids) {
          const platformIds = prompt.platform_ids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))

          platformIds.forEach(platformId => {
            const platform = platformMap.get(platformId)
            if (platform) {
              platformInfo.push({
                platform_id: platform.id,
                platform_url: platform.url,
                platform_name: platform.name,
                platform_icon: platform.icon,
                platform_icon_base64: platform.icon_base64
              })
            }
          })
        }

        return {
          ...prompt,
          platform_info: platformInfo
        }
      })

      return {
        success: true,
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }




  /**
   * 删除提示词记录
   */
  async delete(id: number): Promise<DatabaseResult<boolean>> {
    try {
      const result = await chatPromptDao.softDelete(id)
      return {
        success: true,
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 检查提示词是否存在
   */
  async exists(prompt: string): Promise<DatabaseResult<boolean>> {
    try {
      const exists = await chatPromptDao.exists(prompt)
      return {
        success: true,
        data: exists
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取最新的提示词
   */
  async getLatest(limit: number = 10): Promise<DatabaseResult<ChatPromptEntity[]>> {
    try {
      const prompts = await chatPromptDao.findLatest(limit)
      return {
        success: true,
        data: prompts
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }


}

// 导出单例实例
export const chatPromptService = ChatPromptService.getInstance()
