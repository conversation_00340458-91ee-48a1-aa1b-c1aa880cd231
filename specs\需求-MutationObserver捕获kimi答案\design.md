# 设计文档 - <PERSON><PERSON>平台答案捕获功能

## 1. 设计概述

### 1.1 设计目标
在EchoAIExtention项目中为Kimi平台实现完整的问答捕获机制，包括：
- 实时监听用户问题输入
- 检测AI答案生成完成状态
- 提取完整的问答内容
- 按对话会话组织数据结构

### 1.2 技术方案概览
- **监听技术**: MutationObserver DOM变化监听
- **页面检测**: 基于CSS类名的页面状态识别
- **内容提取**: 模拟点击复制按钮获取Markdown格式内容
- **数据组织**: Map结构按对话标题分组存储

## 2. 系统架构设计

### 2.1 整体架构
```mermaid
graph TB
    subgraph "Kimi适配器模块"
        KA[KimiAdapter] --> KAS[KimiAnswerService]
        KAS --> PL[PageListener]
        KAS --> AL[AnswerListener]
        KAS --> QC[QuestionCapture]
    end
    
    subgraph "监听层"
        PL --> PO[PageObserver]
        AL --> AO[AnswerObserver]
        QC --> QO[QuestionObserver]
    end
    
    subgraph "数据层"
        KAS --> CM[ConversationMap]
        CM --> CD[ConversationData]
    end
    
    subgraph "工具层"
        KAS --> DU[DOMUtils]
        KAS --> CC[ClipboardCapture]
    end
```

### 2.2 核心组件关系
- **KimiAdapter**: 平台适配器入口，负责初始化KimiAnswerService
- **KimiAnswerService**: 核心服务类，管理所有监听器和数据存储
- **PageListener**: 页面状态监听器，检测页面跳转
- - **QuestionCapture**: 问题捕获器，监听用户问题输入
- **AnswerListener**: 答案监听器，检测AI回复完成


## 3. 详细设计

### 3.1 KimiAnswerService 核心类设计

#### 3.1.1 类结构
```typescript
export class KimiAnswerService extends Singleton<KimiAnswerService> {
    // 适配器引用
    private adapter: BaseAIAdapter | null = null;
    
    // 监听器管理
    private pageObserver: MutationObserver | null = null;
    private answerObserver: MutationObserver | null = null;
    private questionObserver: MutationObserver | null = null;
    
    // 数据存储
    private conversationMap: Map<string, ConversationData> = new Map();
    
    // 状态管理
    private currentTitle: string = '';
    private isAnswering: boolean = false;
    private currentQuestion: string = '';
    
    // 配置参数
    private readonly OBSERVER_CONFIG = { childList: true, subtree: true };
    private readonly MAX_RETRY_COUNT = 3;
    private readonly RETRY_DELAY = 1000;
}
```

#### 3.1.2 数据模型设计
```typescript
interface ConversationData {
    title: string;
    qaList: QuestionAnswerPair[];
    createdAt: number;
    updatedAt: number;
}

interface QuestionAnswerPair {
    question: string;
    answer: string;
    timestamp: number;
    questionId: string;  // 用于问答匹配
    answerId: string;    // 用于问答匹配
}

interface PageState {
    isWelcomePage: boolean;
    isChatPage: boolean;
    hasTransitioned: boolean;
}
```

### 3.2 页面状态检测设计

#### 3.2.1 页面形态识别器
```typescript
class PageStateDetector {
    /**
     * 检测欢迎页面
     * 标识: class="home-page"
     */
    public static isWelcomePage(): boolean {
        return document.querySelector('.home-page') !== null;
    }
    
    /**
     * 检测聊天页面  
     * 标识: class="chat-page chat"
     */
    public static isChatPage(): boolean {
        return document.querySelector('.chat-page.chat') !== null;
    }
    
    /**
     * 获取当前页面状态
     */
    public static getCurrentPageState(): PageState {
        return {
            isWelcomePage: this.isWelcomePage(),
            isChatPage: this.isChatPage(),
            hasTransitioned: false
        };
    }
}
```

#### 3.2.2 页面跳转监听逻辑
```typescript
private setupPageTransitionListener(): void {
    this.logInfo('启动页面跳转监听器');
    
    const targetNode = document.querySelector('.main') || document.body;
    
    this.pageObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                this.handlePageTransition();
            }
        });
    });
    
    this.pageObserver.observe(targetNode, this.OBSERVER_CONFIG);
    
    // 立即检查当前页面状态
    this.handlePageTransition();
}

private handlePageTransition(): void {
    const currentState = PageStateDetector.getCurrentPageState();
    
    if (currentState.isChatPage && !this.answerObserver) {
        this.logInfo('检测到聊天页面，启动答案监听器');
        this.setupAnswerListener();
        this.setupQuestionListener();
    } else if (currentState.isWelcomePage && this.answerObserver) {
        this.logInfo('返回欢迎页面，停止答案监听器');
        this.stopAnswerListener();
        this.stopQuestionListener();
    }
}
```

### 3.3 问题捕获机制设计

#### 3.3.1 问题监听器
```typescript
private setupQuestionListener(): void {
    this.logInfo('启动问题监听器');
    
    const chatContainer = document.querySelector('.chat-content-list');
    if (!chatContainer) {
        this.logWarning('未找到聊天容器，延迟启动问题监听器');
        setTimeout(() => this.setupQuestionListener(), 1000);
        return;
    }
    
    this.questionObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.handleNewQuestionElement(node as Element);
                    }
                });
            }
        });
    });
    
    this.questionObserver.observe(chatContainer, this.OBSERVER_CONFIG);
}

private handleNewQuestionElement(element: Element): void {
    // 检测是否为用户问题元素
    if (element.classList.contains('chat-content-item') && 
        element.classList.contains('chat-content-item-user')) {
        
        this.logInfo('检测到新的用户问题');
        this.extractQuestionContent(element);
    }
}

private extractQuestionContent(element: Element): void {
    const userContentElement = element.querySelector('.user-content');
    if (userContentElement) {
        const questionText = userContentElement.textContent?.trim() || '';
        if (questionText) {
            this.currentQuestion = questionText;
            this.logInfo('问题内容提取成功:', questionText);
            
            // 更新对话标题（如果是第一个问题）
            this.updateConversationTitle();
        }
    }
}
```

### 3.4 答案捕获机制设计

#### 3.4.1 答案监听器
```typescript
private setupAnswerListener(): void {
    this.logInfo('启动答案监听器');
    
    const chatContainer = document.querySelector('.chat-content-list');
    if (!chatContainer) {
        this.logWarning('未找到聊天容器，延迟启动答案监听器');
        setTimeout(() => this.setupAnswerListener(), 1000);
        return;
    }
    
    this.answerObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.handleNewAnswerElement(node as Element);
                    }
                });
            }
        });
    });
    
    this.answerObserver.observe(chatContainer, this.OBSERVER_CONFIG);
}

private handleNewAnswerElement(element: Element): void {
    // 检测是否为AI答案元素
    if (element.classList.contains('chat-content-item') && 
        element.classList.contains('chat-content-item-assistant')) {
        
        this.logInfo('检测到新的AI答案容器');
        this.isAnswering = true;
        
        // 监听答案完成状态
        this.monitorAnswerCompletion(element);
    }
}

private monitorAnswerCompletion(answerElement: Element): void {
    const completionObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.checkAnswerCompletionStatus(node as Element, completionObserver);
                    }
                });
            }
        });
    });
    
    completionObserver.observe(answerElement, this.OBSERVER_CONFIG);
}

private checkAnswerCompletionStatus(element: Element, observer: MutationObserver): void {
    // 检测答案完成标志
    const completionButton = element.querySelector('.segment-assistant-actions-content .simple-button.size-small');
    
    if (completionButton) {
        this.logInfo('检测到答案完成标志');
        observer.disconnect();
        
        // 延迟执行内容提取，确保DOM完全渲染
        setTimeout(() => {
            this.extractAnswerContent(completionButton);
        }, 500);
    }
}
```

#### 3.4.2 内容提取机制
```typescript
private async extractAnswerContent(buttonElement: Element): Promise<void> {
    try {
        const answerContent = await this.simulateClickAndGetContent(buttonElement);
        if (answerContent && this.currentQuestion) {
            await this.saveQuestionAnswerPair(this.currentQuestion, answerContent);
            this.resetCurrentState();
        }
    } catch (error) {
        this.logError('答案内容提取失败', error);
    }
}

private async simulateClickAndGetContent(buttonElement: Element): Promise<string> {
    try {
        // 模拟点击复制按钮
        (buttonElement as HTMLElement).click();
        this.logInfo('执行复制按钮点击');
        
        // 等待剪贴板操作完成
        await this.delay(200);
        
        // 获取剪贴板内容
        const clipboardContent = await this.getClipboardContent();
        this.logInfo('剪贴板内容获取成功');
        
        return clipboardContent;
    } catch (error) {
        this.logError('模拟点击和内容获取失败', error);
        return '';
    }
}

private async getClipboardContent(): Promise<string> {
    // 实现不需要权限的剪贴板读取
    // 这里需要进一步讨论具体实现方案
    return new Promise((resolve) => {
        // 临时实现方案，可能需要调整
        const textArea = document.createElement('textarea');
        textArea.style.position = 'fixed';
        textArea.style.top = '-9999px';
        document.body.appendChild(textArea);
        textArea.focus();
        
        document.execCommand('paste');
        const content = textArea.value;
        document.body.removeChild(textArea);
        
        resolve(content);
    });
}
```

### 3.5 数据存储设计

#### 3.5.1 对话数据管理
```typescript
private updateConversationTitle(): void {
    const titleElement = document.querySelector('.chat-header-content h2');
    if (titleElement) {
        const newTitle = titleElement.textContent?.trim() || `对话_${Date.now()}`;
        if (newTitle !== this.currentTitle) {
            this.currentTitle = newTitle;
            this.ensureConversationExists(newTitle);
        }
    }
}

private ensureConversationExists(title: string): void {
    if (!this.conversationMap.has(title)) {
        const conversationData: ConversationData = {
            title,
            qaList: [],
            createdAt: Date.now(),
            updatedAt: Date.now()
        };
        this.conversationMap.set(title, conversationData);
        this.logInfo(`创建新对话: ${title}`);
    }
}

private async saveQuestionAnswerPair(question: string, answer: string): Promise<void> {
    const conversation = this.conversationMap.get(this.currentTitle);
    if (!conversation) {
        this.logError('保存问答对失败：未找到当前对话', { title: this.currentTitle });
        return;
    }
    
    const qaPair: QuestionAnswerPair = {
        question,
        answer,
        timestamp: Date.now(),
        questionId: this.generateId(),
        answerId: this.generateId()
    };
    
    conversation.qaList.push(qaPair);
    conversation.updatedAt = Date.now();
    
    this.logInfo('问答对保存成功', { 
        title: this.currentTitle, 
        questionLength: question.length,
        answerLength: answer.length 
    });
    
    // 发送到后台存储
    await this.sendToBackground(conversation);
}

private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
```

## 4. 错误处理设计

### 4.1 错误分类和处理策略
```typescript
enum ErrorType {
    DOM_SELECTOR_FAILED = 'DOM_SELECTOR_FAILED',
    CLIPBOARD_ACCESS_FAILED = 'CLIPBOARD_ACCESS_FAILED',
    OBSERVER_INIT_FAILED = 'OBSERVER_INIT_FAILED',
    DATA_SAVE_FAILED = 'DATA_SAVE_FAILED'
}

class ErrorHandler {
    public static handle(errorType: ErrorType, error: any, context?: any): void {
        switch (errorType) {
            case ErrorType.DOM_SELECTOR_FAILED:
                console.warn(`[KimiAnswerService] DOM选择器失效:`, error, context);
                // 尝试备用选择器
                break;
            case ErrorType.CLIPBOARD_ACCESS_FAILED:
                console.error(`[KimiAnswerService] 剪贴板访问失败:`, error);
                // 降级到其他内容提取方案
                break;
            case ErrorType.OBSERVER_INIT_FAILED:
                console.error(`[KimiAnswerService] 监听器初始化失败:`, error);
                // 尝试重新初始化
                break;
            case ErrorType.DATA_SAVE_FAILED:
                console.error(`[KimiAnswerService] 数据保存失败:`, error, context);
                // 缓存到本地，稍后重试
                break;
        }
    }
}
```

### 4.2 重试机制设计
```typescript
private async retryOperation<T>(
    operation: () => Promise<T>, 
    maxRetries: number = this.MAX_RETRY_COUNT,
    delay: number = this.RETRY_DELAY
): Promise<T | null> {
    for (let i = 0; i <= maxRetries; i++) {
        try {
            return await operation();
        } catch (error) {
            if (i === maxRetries) {
                this.logError(`操作失败，已达到最大重试次数(${maxRetries})`, error);
                return null;
            }
            this.logWarning(`操作失败，${delay}ms后重试 (${i + 1}/${maxRetries})`, error);
            await this.delay(delay);
        }
    }
    return null;
}
```

## 5. 性能优化设计

### 5.1 监听器优化
- **延迟初始化**: 只在检测到聊天页面时启动答案监听
- **精确目标**: 监听器只绑定到必要的DOM容器
- **及时清理**: 页面切换时立即停止不需要的监听器

### 5.2 内存管理
- **Map大小限制**: 限制conversationMap最大存储数量
- **定期清理**: 清理过期的对话数据
- **弱引用**: 对DOM元素使用弱引用避免内存泄漏

## 6. 测试设计

### 6.1 单元测试覆盖
```typescript
describe('KimiAnswerService', () => {
    describe('页面状态检测', () => {
        it('应该正确识别欢迎页面');
        it('应该正确识别聊天页面');
        it('应该正确处理页面跳转');
    });
    
    describe('问题捕获', () => {
        it('应该捕获用户问题内容');
        it('应该处理空问题内容');
        it('应该处理多个连续问题');
    });
    
    describe('答案捕获', () => {
        it('应该检测答案完成状态');
        it('应该正确提取答案内容');
        it('应该处理流式答案生成');
    });
    
    describe('数据存储', () => {
        it('应该按对话标题组织数据');
        it('应该正确保存问答对');
        it('应该处理重复标题');
    });
});
```

## 7. 设计确认结果

### 7.1 剪贴板访问方案 ✅
**最终方案**: 申请剪贴板权限使用`navigator.clipboard.readText()`

**实现要点**:
- 在manifest.json中添加clipboardRead权限
- 使用现代clipboard API确保兼容性和安全性
- 添加权限检查和错误处理机制

### 7.2 监听器生命周期管理 ✅
**最终方案**: 简化的生命周期管理

**确认要点**:
- **跨标签页数据共享**: 不需要，每个标签页独立运行
- **页面刷新处理**: 不需要恢复状态，重新初始化监听器
- **实现策略**: 页面刷新时清理所有监听器，重新生成并监听DOM变化

### 7.3 数据持久化策略 ✅
**最终方案**: 纯内存存储

**确认要点**:
- **存储方式**: 数据只在内存中临时存储
- **页面刷新**: 不需要恢复数据，重新开始捕获
- **容量限制**: 不控制最大存储容量
- **数据生命周期**: 与页面会话绑定

### 7.4 答案完成检测的可靠性 ✅
**最终方案**: 多选择器策略 + 配置化扩展

**确认要点**:
- **备用检测方案**: 使用预定义的多个选择器来定位节点
- **兼容性处理**: 当前阶段使用预定义选择器，后续支持在线配置更新
- **扩展性**: 为未来的配置化选择器更新预留接口

## 8. 更新后的技术方案

### 8.1 权限配置更新
需要在manifest.json中添加剪贴板权限：
```json
{
  "permissions": [
    "clipboardRead",
    "activeTab"
  ]
}
```

### 8.2 简化的数据管理
移除持久化相关设计，专注于内存存储：
```typescript
// 移除持久化相关方法
// private async saveToStorage() - 删除
// private async loadFromStorage() - 删除
// 保留内存数据管理
private conversationMap: Map<string, ConversationData> = new Map();
```

### 8.3 多选择器检测策略
```typescript
class SelectorManager {
    // 预定义多个选择器确保兼容性
    private static readonly COMPLETION_SELECTORS = [
        '.segment-assistant-actions-content .simple-button.size-small',
        '.assistant-actions .copy-button',
        '.response-actions .action-button',
        '[data-testid="copy-button"]'
    ];
    
    public static findCompletionButton(container: Element): Element | null {
        for (const selector of this.COMPLETION_SELECTORS) {
            const element = container.querySelector(selector);
            if (element) return element;
        }
        return null;
    }
}
```

## 9. 最终实现优先级

**P0 (核心功能)**:
1. KimiAnswerService基础架构
2. 页面状态检测和跳转监听
3. 问题捕获机制
4. 答案完成检测和内容提取
5. 剪贴板权限和内容读取

**P1 (完善功能)**:
6. 数据存储和组织
7. 错误处理和日志记录
8. 监听器生命周期管理

**P2 (优化功能)**:
9. 性能优化
10. 多选择器兼容性
11. 单元测试

设计确认完成，可以开始实现阶段。