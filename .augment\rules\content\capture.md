---
type: "agent_requested"
description: "页面元素捕捉组件开发规则"
---
# 页面捕捉规则

## 核心职责
- **Capture**: 只观察页面，不修改DOM
- **位置**: `content/capture/`
- **命名**: `ComponentCapture.ts`

## 基础结构
```typescript
class ComponentCapture {
  captureElement(): void { /* 捕捉逻辑 */ }
  destroy(): void { /* 清理资源 */ }
}
```

## 核心规则
- 使用DOMUtils.findElement查找元素
- 事件命名: `echosync:`前缀
- 数据更新: 通过Model单例
- Service调用: 异步处理业务逻辑

## 检查清单
- [ ] 继承BaseCapture
- [ ] 实现captureElement()
- [ ] 实现destroy()
- [ ] 使用Model单例共享数据
- [ ] 文件≤300行
