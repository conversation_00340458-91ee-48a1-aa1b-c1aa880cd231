/**
 * 通用选择器配置
 * 包含跨平台通用的选择器模式
 */

import { SelectorConfig } from "./SelectorManager";

/**
 * 通用选择器配置类
 * 提供所有平台都可能使用的通用选择器
 */
export const CommonSelectors: SelectorConfig = {
  inputField: [
    // 通用输入框选择器
    'textarea[placeholder*="message" i]',
    'textarea[placeholder*="prompt" i]',
    'textarea[placeholder*="输入" i]',
    'textarea[placeholder*="请输入" i]',
    'div[contenteditable="true"]',
    'input[type="text"]',
    '[role="textbox"]',
    // 常见的输入框类名
    ".input-field",
    ".chat-input",
    ".message-input",
    ".prompt-input",
  ],
  sendButton: [
    // 通用发送按钮选择器
    'button[aria-label*="send" i]',
    'button[aria-label*="发送" i]',
    'button[data-testid*="send" i]',
    'button[type="submit"]',
    "button:has(svg)",
    '[role="button"]:has(svg)',
    // 常见的发送按钮类名
    ".send-button",
    ".submit-button",
    ".send-btn",
    ".chat-send",
    'button[type="submit"]',
    '.send-btn',
    '.submit-btn'
  ]
};
