import React from 'react'
import { ToggleSwitch, ProgressBar, StatusIndicator, UserStatusBadge, PlatformTabs } from './components/button'

// This is a test component to verify all our components work correctly
export function TestComponents() {
  const [toggleState, setToggleState] = React.useState(false)
  const [activeTab, setActiveTab] = React.useState('obsidian')

  const platforms = [
    { id: 'obsidian', label: 'Obsidian', icon: '🔵' },
    { id: 'notion', label: 'Notion', icon: '🟣' },
    { id: 'markdown', label: 'Markdown', icon: '📄' }
  ]

  return (
    <div style={{ padding: '20px', maxWidth: '600px' }}>
      <h2>Component Test Page</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Toggle Switch</h3>
        <ToggleSwitch 
          checked={toggleState}
          onChange={setToggleState}
        />
        <p>State: {toggleState ? 'ON' : 'OFF'}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Progress Bar</h3>
        <ProgressBar value={75} />
        <ProgressBar value={30} showAnimation={false} />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Status Indicators</h3>
        <StatusIndicator type="success">Success</StatusIndicator>
        <StatusIndicator type="warning">Warning</StatusIndicator>
        <StatusIndicator type="error">Error</StatusIndicator>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>User Status Badges</h3>
        <UserStatusBadge plan="free" />
        <UserStatusBadge plan="pro" />
        <UserStatusBadge plan="plus" />
        <UserStatusBadge plan="max" />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Platform Tabs</h3>
        <PlatformTabs
          tabs={platforms}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
        <p>Active: {activeTab}</p>
      </div>
    </div>
  )
}
