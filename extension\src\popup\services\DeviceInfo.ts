// 设备信息获取服务
export interface DeviceInfo {
  os: string           // 操作系统
  deviceName: string   // 设备名称
  deviceId: string     // 设备唯一标识（基于MAC地址或其他硬件信息）
  fullName: string     // 完整名称：操作系统-设备名-设备标识
}

class DeviceInfoService {
  private deviceInfo: DeviceInfo | null = null

  // 获取操作系统信息
  private getOperatingSystem(): string {
    const userAgent = navigator.userAgent
    const platform = navigator.platform

    if (userAgent.includes('Windows')) {
      return 'Windows'
    } else if (userAgent.includes('Mac')) {
      return 'macOS'
    } else if (userAgent.includes('Linux')) {
      return 'Linux'
    } else if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
      return 'iOS'
    } else if (userAgent.includes('Android')) {
      return 'Android'
    } else {
      return 'Unknown'
    }
  }

  // 获取设备名称（简化版）
  private getDeviceName(): string {
    const userAgent = navigator.userAgent
    
    // 尝试从用户代理字符串中提取设备信息
    if (userAgent.includes('Windows')) {
      // 对于Windows，使用通用名称
      return 'PC'
    } else if (userAgent.includes('Mac')) {
      if (userAgent.includes('MacBook')) {
        return 'MacBook'
      } else if (userAgent.includes('iMac')) {
        return 'iMac'
      } else {
        return 'Mac'
      }
    } else if (userAgent.includes('iPhone')) {
      return 'iPhone'
    } else if (userAgent.includes('iPad')) {
      return 'iPad'
    } else if (userAgent.includes('Android')) {
      return 'Android'
    } else {
      return 'Device'
    }
  }

  // 生成设备唯一标识
  private async generateDeviceId(): Promise<string> {
    try {
      // 使用多种信息生成设备指纹
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.textBaseline = 'top'
        ctx.font = '14px Arial'
        ctx.fillText('Device fingerprint', 2, 2)
      }
      
      const fingerprint = [
        navigator.userAgent,
        navigator.language,
        screen.width + 'x' + screen.height,
        new Date().getTimezoneOffset(),
        canvas.toDataURL()
      ].join('|')

      // 生成简短的哈希
      const hash = await this.simpleHash(fingerprint)
      return hash.substring(0, 8).toUpperCase()
    } catch (error) {
      console.warn('Failed to generate device ID, using fallback:', error)
      // 降级方案：使用时间戳和随机数
      return Date.now().toString(36).toUpperCase() + Math.random().toString(36).substring(2, 6).toUpperCase()
    }
  }

  // 简单哈希函数
  private async simpleHash(str: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(str)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  // 获取完整设备信息
  async getDeviceInfo(): Promise<DeviceInfo> {
    if (this.deviceInfo) {
      return this.deviceInfo
    }

    const os = this.getOperatingSystem()
    const deviceName = this.getDeviceName()
    const deviceId = await this.generateDeviceId()
    const fullName = `${os}-${deviceName}-${deviceId}`

    this.deviceInfo = {
      os,
      deviceName,
      deviceId,
      fullName
    }

    console.log('Generated device info:', this.deviceInfo)
    return this.deviceInfo
  }

  // 重置设备信息（用于测试）
  reset(): void {
    this.deviceInfo = null
  }
}

export const deviceInfoService = new DeviceInfoService()
