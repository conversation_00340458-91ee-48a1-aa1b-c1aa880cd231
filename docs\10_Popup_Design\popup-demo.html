<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EchoSync Popup UI Demo</title>
    <link rel="stylesheet" href="popup-demo.css">
</head>
<body>
    <div class="popup-container" id="popupContainer">
        <!-- Header -->
        <div class="header">
            <div class="user-status">
                <div class="user-avatar">U</div>
                <div class="status-badge">PRO 👑</div>
            </div>
            <button class="fullscreen-btn" onclick="toggleFullscreen()">⛶</button>
        </div>

        <!-- Main Layout -->
        <div class="main-layout">
            <!-- Sidebar Navigation -->
            <div class="sidebar">
                <div class="nav-item active" onclick="showPage('home')">🏠 首页</div>
                <div class="nav-item" onclick="showPage('membership')">👑 会员</div>
                <div class="nav-item" onclick="showPage('settings')">⚙️ 设置</div>
                <div class="nav-item" onclick="showPage('history')">📋 历史</div>
                <div class="nav-item" onclick="showPage('billing')">💳 账单</div>
                <div class="nav-item" onclick="showPage('support')">🆘 支持</div>
                <div class="nav-item" onclick="showPage('manual')">📖 手册</div>
            </div>

            <!-- Main Content Area -->
            <div class="main-content">
                <!-- Home Page -->
                <div class="page active" id="home">
                    <!-- Floating Ball Control -->
                    <div class="card fade-in">
                        <div class="card-title">🔮 悬浮球控制</div>
                        <div class="card-content">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <span style="font-weight: 600;">启用悬浮球</span>
                                <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                            </div>
                            <div style="font-size: 12px; color: var(--text-muted);">
                                悬浮球将在网页上显示，方便快速访问AI功能
                            </div>
                        </div>
                    </div>

                    <!-- Sync Progress -->
                    <div class="card fade-in">
                        <div class="card-title">📊 同步进度</div>
                        <div class="card-content">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <span style="font-weight: 600;">数据同步</span>
                                <span class="status-indicator status-success">✓ 已同步</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 85%;"></div>
                            </div>
                            <div style="font-size: 12px; color: var(--text-muted); margin-top: 8px;">
                                最后同步: 2分钟前 • 下次同步: 实时
                            </div>
                        </div>
                    </div>

                    <!-- Today's Stats -->
                    <div class="card fade-in">
                        <div class="card-title">📈 今日统计</div>
                        <div class="card-content">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value">23</div>
                                    <div class="stat-label">对话次数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">1.2k</div>
                                    <div class="stat-label">字符数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">5</div>
                                    <div class="stat-label">网站访问</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">12min</div>
                                    <div class="stat-label">使用时长</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card fade-in">
                        <div class="card-title">⚡ 快捷操作</div>
                        <div class="card-content">
                            <div class="action-grid">
                                <div class="action-item">
                                    <div class="action-icon">📝</div>
                                    <div class="action-label">新建对话</div>
                                </div>
                                <div class="action-item">
                                    <div class="action-icon">📋</div>
                                    <div class="action-label">查看历史</div>
                                </div>
                                <div class="action-item">
                                    <div class="action-icon">📤</div>
                                    <div class="action-label">导出数据</div>
                                </div>
                                <div class="action-item">
                                    <div class="action-icon">⚙️</div>
                                    <div class="action-label">设置</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Membership Page -->
                <div class="page" id="membership">
                    <!-- Current Plan -->
                    <div class="card fade-in">
                        <div class="card-title">👑 当前套餐</div>
                        <div class="card-content">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <div>
                                    <div style="font-weight: 600; font-size: 18px; color: var(--blue-600);">Pro 套餐</div>
                                    <div style="font-size: 12px; color: var(--text-muted);">$9.9/月 • 下次扣费: 2024-02-15</div>
                                </div>
                                <div class="status-indicator status-success">活跃</div>
                            </div>
                            <div style="margin: 12px 0;">
                                <div style="font-size: 14px; margin-bottom: 8px;">✓ 无限对话次数</div>
                                <div style="font-size: 14px; margin-bottom: 8px;">✓ 高级AI模型</div>
                                <div style="font-size: 14px; margin-bottom: 8px;">✓ 多设备同步</div>
                                <div style="font-size: 14px; margin-bottom: 8px;">✓ 优先客服支持</div>
                            </div>
                        </div>
                    </div>

                    <!-- Upgrade Options -->
                    <div class="card fade-in">
                        <div class="card-title">🚀 升级选项</div>
                        <div class="card-content">
                            <div style="border: 1px solid rgba(124, 58, 237, 0.2); border-radius: 12px; padding: 16px; margin-bottom: 12px; background: rgba(124, 58, 237, 0.02);">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <div style="font-weight: 600; color: var(--purple-600);">Plus 套餐</div>
                                    <div style="font-weight: 600; color: var(--purple-600);">$19.9/月</div>
                                </div>
                                <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 12px;">
                                    包含Pro所有功能，plus更多高级特性
                                </div>
                                <button class="btn btn-secondary" style="width: 100%; background: var(--purple-500); color: white; border: none;">升级到Plus</button>
                            </div>
                            <div style="border: 1px solid rgba(15, 23, 42, 0.12); border-radius: 12px; padding: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <div style="font-weight: 600;">Max 套餐</div>
                                    <div style="font-weight: 600;">$39.9/月</div>
                                </div>
                                <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 12px;">
                                    企业级功能，无限制使用
                                </div>
                                <button class="btn btn-secondary" style="width: 100%;">升级到Max</button>
                            </div>
                        </div>
                    </div>

                    <!-- Usage Stats -->
                    <div class="card fade-in">
                        <div class="card-title">📊 使用统计</div>
                        <div class="card-content">
                            <div style="margin-bottom: 16px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                    <span style="font-size: 14px;">本月对话</span>
                                    <span style="font-size: 14px; font-weight: 600;">847 / 无限</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 60%;"></div>
                                </div>
                            </div>
                            <div style="margin-bottom: 16px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                    <span style="font-size: 14px;">存储空间</span>
                                    <span style="font-size: 14px; font-weight: 600;">2.3GB / 10GB</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 23%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Page -->
                <div class="page" id="settings">
                    <!-- Device Info -->
                    <div class="card fade-in">
                        <div class="card-title">🖥️ 当前设备信息</div>
                        <div class="card-content">
                            <div style="font-weight: 600;">Windows-DESKTOP-HAMMER-7F8A9B2C</div>
                        </div>
                    </div>

                    <!-- Platform Export Config -->
                    <div class="card fade-in">
                        <div class="card-title">📂 平台导出配置</div>
                        <div class="card-content">
                            <!-- Platform Tabs -->
                            <div class="platform-tabs">
                                <button class="platform-tab active" onclick="switchPlatform('obsidian')">
                                    🔵 Obsidian
                                </button>
                                <button class="platform-tab" onclick="switchPlatform('notion')">
                                    🟣 Notion
                                </button>
                                <button class="platform-tab" onclick="switchPlatform('markdown')">
                                    📄 Markdown
                                </button>
                            </div>

                            <!-- Obsidian Content -->
                            <div class="platform-content active" id="obsidian-content">
                                <!-- Current Device -->
                                <div class="device-item current-device">
                                    <div class="device-header">
                                        <span>💻</span>
                                        <span>Windows-DESKTOP-HAMMER</span>
                                        <span class="status-indicator status-success">当前设备</span>
                                    </div>
                                    <div class="path-label">导出路径:</div>
                                    <div class="device-path">
                                        <input type="text" value="C:\Users\<USER>\Documents\Obsidian" placeholder="选择导出路径...">
                                        <button class="btn btn-secondary btn-small">浏览...</button>
                                    </div>
                                </div>
                                <!-- Other Devices -->
                                <div class="device-item">
                                    <div class="device-header">
                                        <span>📱</span>
                                        <span>iOS-iPhone-Pro</span>
                                        <span class="status-indicator status-warning">未配置</span>
                                    </div>
                                    <div class="path-label">导出路径:</div>
                                    <div class="device-path">
                                        <input type="text" placeholder="未配置导出路径" disabled>
                                        <button class="btn btn-secondary btn-small">设置</button>
                                    </div>
                                </div>
                                <div class="device-item">
                                    <div class="device-header">
                                        <span>💻</span>
                                        <span>macOS-MacBook-Air</span>
                                        <span class="status-indicator status-success">已配置</span>
                                    </div>
                                    <div class="path-label">导出路径:</div>
                                    <div class="device-path">
                                        <input type="text" value="/Users/<USER>/Documents/Obsidian" placeholder="选择导出路径...">
                                        <button class="btn btn-secondary btn-small">浏览...</button>
                                        <button style="background: none; border: none; cursor: pointer; padding: 4px;">⚙️</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Notion Content -->
                            <div class="platform-content" id="notion-content">
                                <div class="device-item current-device">
                                    <div class="device-header">
                                        <span>💻</span>
                                        <span>Windows-DESKTOP-HAMMER</span>
                                        <span class="status-indicator status-success">当前设备</span>
                                    </div>
                                    <div class="path-label">导出路径:</div>
                                    <div class="device-path">
                                        <input type="text" value="C:\Users\<USER>\Documents\Notion" placeholder="选择导出路径...">
                                        <button class="btn btn-secondary btn-small">浏览...</button>
                                    </div>
                                </div>
                                <div class="device-item">
                                    <div class="device-header">
                                        <span>📱</span>
                                        <span>iOS-iPhone-Pro</span>
                                        <span class="status-indicator status-warning">未配置</span>
                                    </div>
                                    <div class="path-label">导出路径:</div>
                                    <div class="device-path">
                                        <input type="text" placeholder="未配置导出路径" disabled>
                                        <button class="btn btn-secondary btn-small">设置</button>
                                    </div>
                                </div>
                                <div class="device-item">
                                    <div class="device-header">
                                        <span>💻</span>
                                        <span>macOS-MacBook-Air</span>
                                        <span class="status-indicator status-warning">未配置</span>
                                    </div>
                                    <div class="path-label">导出路径:</div>
                                    <div class="device-path">
                                        <input type="text" placeholder="未配置导出路径" disabled>
                                        <button class="btn btn-secondary btn-small">设置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Markdown Content -->
                            <div class="platform-content" id="markdown-content">
                                <div class="device-item current-device">
                                    <div class="device-header">
                                        <span>💻</span>
                                        <span>Windows-DESKTOP-HAMMER</span>
                                        <span class="status-indicator status-success">当前设备</span>
                                    </div>
                                    <div class="path-label">导出路径:</div>
                                    <div class="device-path">
                                        <input type="text" value="C:\Users\<USER>\Documents\Markdown" placeholder="选择导出路径...">
                                        <button class="btn btn-secondary btn-small">浏览...</button>
                                    </div>
                                </div>
                                <div class="device-item">
                                    <div class="device-header">
                                        <span>📱</span>
                                        <span>iOS-iPhone-Pro</span>
                                        <span class="status-indicator status-warning">未配置</span>
                                    </div>
                                    <div class="path-label">导出路径:</div>
                                    <div class="device-path">
                                        <input type="text" placeholder="未配置导出路径" disabled>
                                        <button class="btn btn-secondary btn-small">设置</button>
                                    </div>
                                </div>
                                <div class="device-item">
                                    <div class="device-header">
                                        <span>🖥️</span>
                                        <span>Linux-Ubuntu</span>
                                        <span class="status-indicator status-warning">未配置</span>
                                    </div>
                                    <div class="path-label">导出路径:</div>
                                    <div class="device-path">
                                        <input type="text" placeholder="未配置导出路径" disabled>
                                        <button class="btn btn-secondary btn-small">设置</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- History Page -->
                <div class="page" id="history">
                    <!-- Search and Filter -->
                    <div class="card fade-in">
                        <div class="card-title">🔍 搜索与筛选</div>
                        <div class="card-content">
                            <div style="display: flex; gap: 8px; margin-bottom: 12px;">
                                <input type="text" placeholder="搜索对话内容..."
                                       style="flex: 1; padding: 8px; border: 1px solid rgba(15, 23, 42, 0.12); border-radius: 8px; font-size: 14px;">
                                <button class="btn btn-primary btn-small">搜索</button>
                            </div>
                            <div style="display: flex; gap: 8px;">
                                <button class="btn btn-secondary btn-small">今天</button>
                                <button class="btn btn-secondary btn-small">本周</button>
                                <button class="btn btn-secondary btn-small">本月</button>
                                <button class="btn btn-secondary btn-small">全部</button>
                            </div>
                        </div>
                    </div>

                    <!-- Conversation List -->
                    <div class="card fade-in">
                        <div class="card-title">💬 对话记录</div>
                        <div class="card-content">
                            <div style="border-bottom: 1px solid rgba(15, 23, 42, 0.06); padding: 12px 0; margin-bottom: 12px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <div style="font-weight: 600;">关于React Hooks的讨论</div>
                                    <div style="font-size: 12px; color: var(--text-muted);">2小时前</div>
                                </div>
                                <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 8px;">
                                    讨论了useState和useEffect的使用场景...
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <span class="status-indicator status-success">已导出</span>
                                    <button class="btn btn-secondary btn-small">查看</button>
                                    <button class="btn btn-secondary btn-small">导出</button>
                                </div>
                            </div>
                            <div style="border-bottom: 1px solid rgba(15, 23, 42, 0.06); padding: 12px 0; margin-bottom: 12px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <div style="font-weight: 600;">Python数据分析问题</div>
                                    <div style="font-size: 12px; color: var(--text-muted);">昨天</div>
                                </div>
                                <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 8px;">
                                    如何使用pandas处理大型数据集...
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <span class="status-indicator status-warning">待导出</span>
                                    <button class="btn btn-secondary btn-small">查看</button>
                                    <button class="btn btn-secondary btn-small">导出</button>
                                </div>
                            </div>
                            <div style="padding: 12px 0;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <div style="font-weight: 600;">UI设计最佳实践</div>
                                    <div style="font-size: 12px; color: var(--text-muted);">3天前</div>
                                </div>
                                <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 8px;">
                                    探讨了现代UI设计的原则和方法...
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <span class="status-indicator status-success">已导出</span>
                                    <button class="btn btn-secondary btn-small">查看</button>
                                    <button class="btn btn-secondary btn-small">导出</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Billing Page -->
                <div class="page" id="billing">
                    <!-- Current Subscription -->
                    <div class="card fade-in">
                        <div class="card-title">💳 当前订阅</div>
                        <div class="card-content">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <div>
                                    <div style="font-weight: 600; font-size: 16px;">Pro 套餐</div>
                                    <div style="font-size: 12px; color: var(--text-muted);">$9.9/月</div>
                                </div>
                                <span class="status-indicator status-success">活跃</span>
                            </div>
                            <div style="margin-bottom: 12px;">
                                <div style="font-size: 14px; margin-bottom: 4px;">下次扣费: 2024-02-15</div>
                                <div style="font-size: 14px;">支付方式: **** **** **** 1234</div>
                            </div>
                        </div>
                    </div>

                    <!-- Subscription Management -->
                    <div class="card fade-in">
                        <div class="card-title">📋 订阅管理</div>
                        <div class="card-content">
                            <div style="display: flex; gap: 8px; margin-bottom: 12px;">
                                <button class="btn btn-secondary" style="background: var(--purple-500); color: white; border: none;">升级到Plus</button>
                                <button class="btn btn-secondary">升级到Max</button>
                            </div>
                            <button class="btn btn-secondary" style="color: var(--error); border-color: var(--error);">取消订阅</button>
                        </div>
                    </div>

                    <!-- Payment History -->
                    <div class="card fade-in">
                        <div class="card-title">🧾 支付历史</div>
                        <div class="card-content">
                            <div style="border-bottom: 1px solid rgba(15, 23, 42, 0.06); padding: 12px 0; margin-bottom: 12px;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-weight: 600;">Pro 套餐 - 1月</div>
                                        <div style="font-size: 12px; color: var(--text-muted);">2024-01-15</div>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="font-weight: 600;">$9.9</span>
                                        <span class="status-indicator status-success">已支付</span>
                                        <button class="btn btn-secondary btn-small">下载发票</button>
                                    </div>
                                </div>
                            </div>
                            <div style="border-bottom: 1px solid rgba(15, 23, 42, 0.06); padding: 12px 0; margin-bottom: 12px;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-weight: 600;">Pro 套餐 - 首月优惠</div>
                                        <div style="font-size: 12px; color: var(--text-muted);">2023-12-15</div>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="font-weight: 600;">$4.9</span>
                                        <span class="status-indicator status-success">已支付</span>
                                        <button class="btn btn-secondary btn-small">下载发票</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Methods -->
                    <div class="card fade-in">
                        <div class="card-title">💳 支付方式管理</div>
                        <div class="card-content">
                            <div style="border: 1px solid rgba(15, 23, 42, 0.12); border-radius: 8px; padding: 12px; margin-bottom: 12px;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-weight: 600;">**** **** **** 1234</div>
                                        <div style="font-size: 12px; color: var(--text-muted);">过期时间: 12/26</div>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span class="status-indicator status-success">默认</span>
                                        <button class="btn btn-secondary btn-small">编辑</button>
                                        <button class="btn btn-secondary btn-small">删除</button>
                                    </div>
                                </div>
                            </div>
                            <button class="btn btn-secondary">+ 添加新支付方式</button>
                        </div>
                    </div>
                </div>

                <!-- Support Page -->
                <div class="page" id="support">
                    <!-- Contact Info -->
                    <div class="card fade-in">
                        <div class="card-title">📧 联系我们</div>
                        <div class="card-content">
                            <div style="margin-bottom: 12px;">
                                <div style="font-weight: 600; margin-bottom: 4px;">客服邮箱</div>
                                <div style="font-size: 14px; color: var(--blue-600);"><EMAIL></div>
                            </div>
                            <div style="margin-bottom: 12px;">
                                <div style="font-weight: 600; margin-bottom: 4px;">工作时间</div>
                                <div style="font-size: 14px; color: var(--text-muted);">周一至周五 9:00-18:00 (UTC+8)</div>
                            </div>
                            <div style="display: flex; gap: 8px;">
                                <button class="btn btn-primary">发送邮件</button>
                                <button class="btn btn-secondary">查看帮助文档</button>
                            </div>
                        </div>
                    </div>

                    <!-- Feedback Form -->
                    <div class="card fade-in">
                        <div class="card-title">💬 留言反馈</div>
                        <div class="card-content">
                            <div style="margin-bottom: 12px;">
                                <label style="display: block; font-weight: 600; margin-bottom: 4px;">反馈类型</label>
                                <select style="width: 100%; padding: 8px; border: 1px solid rgba(15, 23, 42, 0.12); border-radius: 8px; font-size: 14px;">
                                    <option>Bug报告</option>
                                    <option>功能建议</option>
                                    <option>使用问题</option>
                                    <option>其他</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 12px;">
                                <label style="display: block; font-weight: 600; margin-bottom: 4px;">详细描述</label>
                                <textarea placeholder="请详细描述您遇到的问题或建议..."
                                          style="width: 100%; height: 80px; padding: 8px; border: 1px solid rgba(15, 23, 42, 0.12); border-radius: 8px; font-size: 14px; resize: vertical;"></textarea>
                            </div>
                            <div style="margin-bottom: 12px;">
                                <label style="display: block; font-weight: 600; margin-bottom: 4px;">联系邮箱</label>
                                <input type="email" placeholder="<EMAIL>"
                                       style="width: 100%; padding: 8px; border: 1px solid rgba(15, 23, 42, 0.12); border-radius: 8px; font-size: 14px;">
                            </div>
                            <button class="btn btn-primary" style="width: 100%;">提交反馈</button>
                        </div>
                    </div>

                    <!-- Quick Help -->
                    <div class="card fade-in">
                        <div class="card-title">📚 快速帮助</div>
                        <div class="card-content">
                            <div style="margin-bottom: 8px;">
                                <a href="#" style="display: block; padding: 8px 0; color: var(--blue-600); text-decoration: none; border-bottom: 1px solid rgba(15, 23, 42, 0.06);">
                                    如何开始使用EchoSync？
                                </a>
                            </div>
                            <div style="margin-bottom: 8px;">
                                <a href="#" style="display: block; padding: 8px 0; color: var(--blue-600); text-decoration: none; border-bottom: 1px solid rgba(15, 23, 42, 0.06);">
                                    如何导出对话记录？
                                </a>
                            </div>
                            <div style="margin-bottom: 8px;">
                                <a href="#" style="display: block; padding: 8px 0; color: var(--blue-600); text-decoration: none; border-bottom: 1px solid rgba(15, 23, 42, 0.06);">
                                    多设备同步设置
                                </a>
                            </div>
                            <div>
                                <a href="#" style="display: block; padding: 8px 0; color: var(--blue-600); text-decoration: none;">
                                    查看完整帮助文档
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Manual Page -->
                <div class="page" id="manual">
                    <!-- Getting Started -->
                    <div class="card fade-in">
                        <div class="card-title">🚀 快速开始</div>
                        <div class="card-content">
                            <div style="margin-bottom: 16px;">
                                <div style="font-weight: 600; margin-bottom: 8px;">1. 启用悬浮球</div>
                                <div style="font-size: 14px; color: var(--text-muted); margin-bottom: 12px;">
                                    在首页开启悬浮球功能，它会在网页上显示一个小球，方便快速访问AI功能。
                                </div>
                            </div>
                            <div style="margin-bottom: 16px;">
                                <div style="font-weight: 600; margin-bottom: 8px;">2. 开始对话</div>
                                <div style="font-size: 14px; color: var(--text-muted); margin-bottom: 12px;">
                                    点击悬浮球或使用快捷键开始与AI对话，支持文本输入和语音输入。
                                </div>
                            </div>
                            <div>
                                <div style="font-weight: 600; margin-bottom: 8px;">3. 管理记录</div>
                                <div style="font-size: 14px; color: var(--text-muted);">
                                    在历史页面查看所有对话记录，支持搜索、筛选和导出功能。
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="card fade-in">
                        <div class="card-title">✨ 主要功能</div>
                        <div class="card-content">
                            <div style="margin-bottom: 12px;">
                                <div style="font-weight: 600; margin-bottom: 4px;">🔮 智能悬浮球</div>
                                <div style="font-size: 14px; color: var(--text-muted);">在任何网页上快速访问AI助手</div>
                            </div>
                            <div style="margin-bottom: 12px;">
                                <div style="font-weight: 600; margin-bottom: 4px;">💬 对话管理</div>
                                <div style="font-size: 14px; color: var(--text-muted);">自动保存和同步所有对话记录</div>
                            </div>
                            <div style="margin-bottom: 12px;">
                                <div style="font-weight: 600; margin-bottom: 4px;">📤 多格式导出</div>
                                <div style="font-size: 14px; color: var(--text-muted);">支持导出到Obsidian、Notion、Markdown</div>
                            </div>
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">🔄 多设备同步</div>
                                <div style="font-size: 14px; color: var(--text-muted);">在所有设备间同步数据和设置</div>
                            </div>
                        </div>
                    </div>

                    <!-- Shortcuts -->
                    <div class="card fade-in">
                        <div class="card-title">⌨️ 快捷键</div>
                        <div class="card-content">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: 600;">打开/关闭悬浮球</span>
                                <span style="background: rgba(15, 23, 42, 0.06); padding: 2px 8px; border-radius: 4px; font-size: 12px;">Ctrl + Shift + A</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: 600;">快速对话</span>
                                <span style="background: rgba(15, 23, 42, 0.06); padding: 2px 8px; border-radius: 4px; font-size: 12px;">Ctrl + Shift + C</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: 600;">全屏模式</span>
                                <span style="background: rgba(15, 23, 42, 0.06); padding: 2px 8px; border-radius: 4px; font-size: 12px;">Ctrl + F</span>
                            </div>
                            <div style="display: flex; justify-content: space-between;">
                                <span style="font-weight: 600;">打开设置</span>
                                <span style="background: rgba(15, 23, 42, 0.06); padding: 2px 8px; border-radius: 4px; font-size: 12px;">Ctrl + ,</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="popup-demo.js"></script>
</body>
</html>
