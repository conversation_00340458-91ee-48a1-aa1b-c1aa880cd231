import { Singleton } from "@/common/base";
import { BaseAIAdapter } from "../core/BaseAIAdapter";
import { ErrorType, ErrorHandler } from "../../types/ErrorTypes";
import { ConversationData} from "../../types/KimiTypes";
import { KimiSelectorService } from "./KimiSelectorService";
import { KimiPageService } from "./KimiPageService";
import { KimiClipboardService } from "./KimiClipboardService";
import { KimiAnswerService } from "./KimiAnswerService";
import { logDebug, logError } from "@/content/utils/logUtils";
import { DOMUtils } from "@/content/utils/DOMUtils";
import { AnswerModel } from "@/content/model/AnswerModel";
import SelectorManager from "../../configs/SelectorManager";
import { ObsidianExportInject } from "@/content/inject/ObsidianExportInject";


/**
 * Kimi平台答案捕获控制器
 * 负责流程编排和协调各Service层的工作
 */
export class KimiAnswerController {
    // 适配器引用
    private adapter: BaseAIAdapter | null = null;

    // 服务层组件
    private answerService: KimiAnswerService | null = null;

    // 注入器组件
    private obsidianExportInject: ObsidianExportInject | null = null;

    // 状态管理
    private currentPageType: 'home' | 'chat' | 'unknown' = 'unknown';

   
    constructor() {
        this.answerService = new KimiAnswerService();
        this.obsidianExportInject = new ObsidianExportInject();
    }

    /**
     * 初始化监听
     * @param adapter BaseAIAdapter实例
     */
    public init(adapter: BaseAIAdapter): void {
        this.adapter = adapter;
        console.info('KimiAnswerController 初始化开始: ');
        AnswerModel.getInstance().initPlatform(adapter.getCurrentPlatform());

        // 立即检查当前页面类型并启动相应服务
        this.checkPageTypeAndInitServices();

        console.info('KimiAnswerController 初始化完成');
    }

    /**
     * 检查页面类型并初始化服务
     */
    private checkPageTypeAndInitServices(): void {
        try {
            // 使用URL判断页面类型
            const urlResult = KimiPageService.getPageTypeFromUrl();

            let newPageType: 'home' | 'chat' | 'unknown';
            if (urlResult.isHome) {
                newPageType = 'home';
            } else if (urlResult.isChat) {
                newPageType = 'chat';
            } else {
                newPageType = 'unknown';
            }

            console.info('页面类型检测', {
                url: window.location.href,
                pageType: newPageType,
                chatId: urlResult.chatId
            });

            this.currentPageType = newPageType;
            switch (newPageType) {
                case 'home':
                    console.info('进入Home页，停止所有监听器');
                    this.stopAllListeners();
                    break;

                case 'chat':
                    AnswerModel.getInstance().setChatId(urlResult.chatId);
                    console.info(`进入Chat页，启动监听器 (chatId: ${urlResult.chatId})`);
                    this.startChatListeners();
                    // 启动 Obsidian 导出注入器
                    if (this.obsidianExportInject) {
                        this.obsidianExportInject.start();
                    }
                    break;

                case 'unknown':
                    console.info('未知页面类型，停止所有监听器');
                    this.stopAllListeners();
                    break;
            }

        } catch (error) {
            logError('页面类型检测失败', error);
        }
    }


    /**
     * 启动聊天页监听器
     * 异步启动监听器，即关心的节点出现后，才启动监听器
     */
    private async startChatListeners(): Promise<void> {
        try {
            // 1. 等待并处理聊天标题
            await this.handleChatHeader();
            
            // 2. 等待聊天列表节点并启动监听
            await this.handleChatList();
            
        } catch (error) {
            logError('启动聊天页监听器失败', error);
        }
    }

    /**
     * 处理聊天标题
     */
    private async handleChatHeader(): Promise<void> {
            const selectors = SelectorManager.getSelector();
        const el_header = await DOMUtils.asyncSelectElement(selectors.headerContent);
        console.info('已找到header节点', el_header);
        
        if (el_header == null) {
            logError('未找到header节点');
            return;
        }
        
        // 提取并更新对话标题
        const h2Element = DOMUtils.findElementInContainer(el_header, selectors.headerValue);
        if (h2Element) { 
            const observer = new MutationObserver(() => {
                    const title = h2Element.textContent?.trim() || '';
                    console.log("更新后获得标题文本：", title);
                     // 更新数据模型中的标题
                    AnswerModel.getInstance().setCurrentTitle(title);
                    observer.disconnect();
                });

            observer.observe(h2Element, { childList: true, characterData: true, subtree: true });
        } else {
            logError('未找到h2标题元素');
        }
    }

    /**
     * 处理聊天列表并启动监听
     */
    private async handleChatList(): Promise<void> {
        const selectors = SelectorManager.getSelector();
        if (!selectors?.chatContentList) {
            logError('未找到聊天内容列表选择器配置');
            return;
        }

        const el_chat_list = await DOMUtils.asyncSelectElement(selectors.chatContentList);
        console.info('已找到聊天列表节点', el_chat_list);

        if (el_chat_list == null) {
            logError('未找到聊天列表节点');
            return;
        }

        // 将聊天列表节点传递给 KimiAnswerService 并启动监听
        await this.answerService.startListeningOnChatList(el_chat_list);
        console.info('KimiAnswerController 流程控制完成，业务监听已交给 KimiAnswerService');
    }

    /**
     * 停止所有监听器
     */
    private stopAllListeners(): void {
        this.answerService.stopAnswerListener();
        // 停止 Obsidian 导出注入器
        if (this.obsidianExportInject) {
            this.obsidianExportInject.stop();
        }
    }


    /**
     * 销毁服务，清理所有资源
     */
    public destroy(): void {
        try {
            console.info('开始销毁 KimiAnswerController');

            // 清理答案服务
            this.answerService.destroy();

            // 清理 Obsidian 导出注入器
            if (this.obsidianExportInject) {
                this.obsidianExportInject.destroy();
                this.obsidianExportInject = null;
            }

            // 重置状态
            this.currentPageType = 'unknown';

            // 清理适配器引用
            this.adapter = null;

            // 清理数据模型
            AnswerModel.getInstance().destroy();
            console.info('KimiAnswerController 销毁完成');
            this.answerService = null

        } catch (error) {
            console.error('[KimiAnswerController] 销毁过程中发生错误:', error);
        }
    }

    /**
     * 手动刷新页面状态（当URL变化时调用）
     */
    public refreshPageState(): void {
        console.info('手动刷新页面状态');
        this.checkPageTypeAndInitServices();

    }

}