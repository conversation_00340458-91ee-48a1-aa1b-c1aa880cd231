import React, { useEffect } from 'react'
import { useAppStore } from './stores/app-store'
import { useAnimations, usePageTransition } from './hooks/useAnimations'
import { Header } from './components/Header'
import { Sidebar } from './components/Sidebar'
import { HomePage } from './pages/HomePage'
import { MembershipPage } from './pages/MembershipPage'
import { SettingsPage } from './pages/SettingsPage'
import { HistoryPage } from './pages/HistoryPage'
import { BillingPage } from './pages/BillingPage'
import { SupportPage } from './pages/SupportPage'
import { ManualPage } from './pages/ManualPage'

function App() {
  const initialize = useAppStore(state => state.initialize)
  const isLoading = useAppStore(state => state.isLoading)
  const popupUI = useAppStore(state => state.popupUI)
  const setFullscreen = useAppStore(state => state.setFullscreen)
  const setActivePage = useAppStore(state => state.setActivePage)

  // Use animation hooks
  useAnimations()
  usePageTransition(popupUI.activePage)

  useEffect(() => {
    initialize()
  }, [initialize])

  const handleToggleFullscreen = () => {
    setFullscreen(!popupUI.isFullscreen)
  }

  const handlePageChange = (pageId: string) => {
    setActivePage(pageId)
  }

  const renderPage = () => {
    switch (popupUI.activePage) {
      case 'home':
        return <HomePage />
      case 'membership':
        return <MembershipPage />
      case 'settings':
        return <SettingsPage />
      case 'history':
        return <HistoryPage />
      case 'billing':
        return <BillingPage />
      case 'support':
        return <SupportPage />
      case 'manual':
        return <ManualPage />
      default:
        return <HomePage />
    }
  }

  if (isLoading) {
    return (
      <div className="popup-container flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className={`popup-container ${popupUI.isFullscreen ? 'fullscreen' : ''}`}>
      <Header
        isFullscreen={popupUI.isFullscreen}
        onToggleFullscreen={handleToggleFullscreen}
        userPlan={popupUI.userPlan}
        userName={popupUI.userName}
      />

      <div className="main-layout">
        <Sidebar
          activeItem={popupUI.activePage}
          onItemClick={handlePageChange}
          isFullscreen={popupUI.isFullscreen}
        />

        <div className="main-content">
          <div className={`page active`}>
            {renderPage()}
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
