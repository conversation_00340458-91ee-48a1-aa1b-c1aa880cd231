/**
 * Favicon管理纯函数
 */

import { platformDatabaseProxy } from '@/common/service/PlatformDatabaseProxy'
import type { PlatformEntity } from '@/common/types/database_entity'

export interface FaviconResult {
  success: boolean
  base64?: string
  error?: string
}

/**
 * 检查并更新平台favicon（纯函数）
 */
export async function checkAndUpdateFavicon(platform: PlatformEntity): Promise<boolean> {
  // 检查是否需要更新
  if (platform.icon_base64 && platform.icon_base64.startsWith('data:image/')) {
    return true // 已有有效的favicon
  }

  // 获取当前页面favicon
  const faviconResult = await getCurrentPageFavicon()
  if (!faviconResult.success || !faviconResult.base64) {
    return false
  }

  // 更新平台favicon
  platform.icon_base64 = faviconResult.base64

  // 更新数据库
  const response = await platformDatabaseProxy.updatePlatformFavicon(
    platform.id,
    faviconResult.base64
  )

  return response.success
}

/**
 * 获取当前页面favicon
 */
async function getCurrentPageFavicon(): Promise<FaviconResult> {
  // 从head查找
  const headFavicon = findFaviconFromHead()
  if (headFavicon) {
    const result = await fetchFavicon(headFavicon)
    if (result.success) return result
  }

  // 尝试常见路径
  const baseUrl = `${window.location.protocol}//${window.location.host}`
  const paths = ['/favicon.ico', '/favicon.png']
  
  for (const path of paths) {
    const result = await fetchFavicon(baseUrl + path)
    if (result.success) return result
  }

  return { success: false, error: 'No favicon found' }
}

/**
 * 从head中获取favicon链接，如果没有则返回null。
 * 该函数会查找<link rel="icon" type="image/svg+xml">、<link rel="icon" type="image/png">、<link rel="shortcut icon">和<link rel="apple-touch-icon">。
 * 
 * @returns 
 */
function findFaviconFromHead(): string | null {
  const link = document.querySelector('link[rel*="icon"]') as HTMLLinkElement
  return link?.href || null
}

/**
 * 下载并转换为base64格式的favicon。
 * 
 * @param url 
 * @returns 
 */
async function fetchFavicon(url: string): Promise<FaviconResult> {
  try {
    const response = await fetch(url)
    if (!response.ok) return { success: false, error: `HTTP ${response.status}` }

    const blob = await response.blob()
    if (blob.size === 0 || blob.size > 1024 * 1024) {
      return { success: false, error: 'Invalid size' }
    }

    const base64 = await blobToBase64(blob)
    return { success: true, base64 }
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : 'Fetch failed' }
  }
}

/**
 * 将Blob转换为base64格式的字符串。
 * 
 * @param blob 
 * @returns 
 */
function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = () => reject(new Error('Failed to convert blob'))
    reader.readAsDataURL(blob)
  })
}
