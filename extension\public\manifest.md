# Chrome Extension Manifest 配置说明

本文档详细说明了 `manifest.json` 文件中各个属性的作用和配置。

## 基本信息

### manifest_version
- **值**: `3`
- **说明**: 指定使用 Manifest V3 版本，这是 Chrome 扩展的最新标准
- **必需**: 是

### name
- **值**: `"EchoAI Extension"`
- **说明**: 扩展的显示名称，会在 Chrome 扩展管理页面和商店中显示
- **必需**: 是

### version
- **值**: `"1.0.0"`
- **说明**: 扩展的版本号，遵循语义化版本规范 (SemVer)
- **必需**: 是

### description
- **值**: `"AI助手扩展，帮助用户更好地使用各种AI平台"`
- **说明**: 扩展的简短描述，会在扩展商店和管理页面中显示
- **必需**: 是

## 权限配置

### permissions
- **值**: `["storage", "activeTab", "scripting"]`
- **说明**: 扩展所需的基本权限
  - `storage`: 允许使用 chrome.storage API 进行数据存储
  - `activeTab`: 允许访问当前活动标签页
  - `scripting`: 允许向页面注入脚本
  - `background`: 允许在后台运行脚本
  - `tabs`: 允许操作标签页
- **必需**: 根据功能需求

### host_permissions
- **值**: 包含多个网站的 URL 模式
- **说明**: 指定扩展可以访问的网站域名
  - `"https://chat.openai.com/*"`: ChatGPT官方网站
  - `"https://claude.ai/*"`: Claude AI官方网站
  - `"https://bard.google.com/*"`: Google Bard官方网站
  - `"https://www.bing.com/*"`: 必应搜索（包含Copilot）
  - `"https://poe.com/*"`: Poe AI平台
  - `"https://character.ai/*"`: Character.AI平台
  - `"https://huggingface.co/*"`: Hugging Face平台
  - `"https://replicate.com/*"`: Replicate AI平台
  - `"https://cohere.ai/*"`: Cohere AI平台
  - `"https://www.perplexity.ai/*"`: Perplexity AI搜索
- **必需**: 根据需要访问的网站

## 脚本配置

### background
- **值**: `{"service_worker": "background.js"}`
- **说明**: 配置后台服务工作者（Service Worker），用于处理扩展的后台逻辑
- **注意**: Manifest V3 使用 Service Worker 替代了 V2 的 Background Scripts

### content_scripts
- **说明**: 配置内容脚本，用于在指定网页中注入脚本。或者叫 tabs
- **配置项**:
  - `matches`: 匹配的网页 URL 模式（与 host_permissions 对应）
  - `js`: ` "js": ["src/content/index.ts"],`要注入的 JavaScript 文件列表。即在这些页面，要注入脚本
  - `css`: 要注入的 CSS 文件列表（可选）
  - `run_at`: 脚本运行时机，`"document_end"` 表示在 DOM 构建完成后运行

## 用户界面配置

### action
- **说明**: 配置扩展的工具栏按钮（popup）
- **配置项**:
  - `default_popup`: 点击扩展图标时显示的弹窗页面
  - `default_title`: 鼠标悬停时显示的提示文本
  - `default_icon`: 工具栏中显示的图标

### options_page
- **值**: `"options.html"`
- **说明**: 扩展的选项设置页面，用户可以通过右键菜单访问。或者在popup页面，显示一个齿轮，点击进入设置页。

### icons
- **说明**: 扩展在不同场景下使用的图标
- **配置项**:
  - `"16"`: 16x16 像素图标（扩展页面）
  - `"48"`: 48x48 像素图标（扩展管理页面）
  - `"128"`: 128x128 像素图标（Chrome 网上应用店）

## 资源和安全配置

### web_accessible_resources
- **说明**: 定义可以被网页访问的扩展资源
- **配置项**:
  - `resources`: 可访问的资源文件列表
  - `matches`: 允许访问这些资源的网页 URL 模式

### content_security_policy
- **说明**: 内容安全策略，用于防止 XSS 攻击
- **配置项**:
  - `extension_pages`: 适用于扩展页面的 CSP 规则
  - 当前配置允许内联脚本和样式，以及从 'unsafe-eval' 执行代码

### commands
- **说明**: 定义键盘快捷键命令
- **配置项**:
  - `toggle_extension`: 切换扩展开关的快捷键
    - `suggested_key`: 建议的快捷键组合
    - `description`: 命令的描述文本

## 开发注意事项

1. **权限最小化原则**: 只申请扩展功能必需的权限
2. **安全性**: 定期更新 CSP 策略，避免安全漏洞
3. **兼容性**: 确保配置符合 Manifest V3 标准
4. **性能**: 合理配置 content_scripts 的运行时机
5. **用户体验**: 提供清晰的权限说明和使用指南

## 版本更新

当需要更新扩展时，记得：
1. 更新 `version` 字段
2. 如果添加了新的权限，需要在描述中说明
3. 测试所有配置的网站是否正常工作
4. 确保图标和资源文件路径正确