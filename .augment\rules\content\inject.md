---
type: "development_rules"
description: "UI注入组件开发规则"
---

# UI注入规则

## 核心职责
- **Inject**: UI创建、注入、交互
- **Component**: UI渲染、样式
- **位置**: `content/inject/`
- **命名**: `ComponentInject.ts`

## 基础结构
```typescript
class ComponentInject {
  inject(): void { /* 注入DOM */ }
  destroy(): void { /* 清理资源 */ }
}
```

## 核心规则
- 非侵入性: 不影响原页面
- 最高z-index: 确保显示层级
- 样式前缀: `echosync-`避免冲突
- 数据获取: 通过Model单例
- 业务逻辑: 复杂逻辑使用Service

## 检查清单
- [ ] 实现inject()和destroy()
- [ ] 创建对应Component类
- [ ] 使用Model单例获取数据
- [ ] 复杂逻辑使用Service
- [ ] 文件≤300行

