import { dexieDatabase } from '../database/dexie'
import { ChatHistoryEntity } from '@/common/types/database_entity'

/**
 * 聊天历史数据访问对象
 * 只负责数据库CRUD操作，不包含业务逻辑
 */
export class ChatHistoryDao {

  private static instance: ChatHistoryDao

  public static getInstance(): ChatHistoryDao {
    if (!ChatHistoryDao.instance) {
      ChatHistoryDao.instance = new ChatHistoryDao()
    }
    return ChatHistoryDao.instance
  }

  /**
   * 创建聊天历史记录
   */
  async create(chatHistory: Omit<ChatHistoryEntity, 'id'>): Promise<ChatHistoryEntity> {
    await dexieDatabase.initialize()
    
    const id = await dexieDatabase.chatHistory.add({
      ...chatHistory,
      create_time: chatHistory.create_time || Date.now(),
      is_synced: chatHistory.is_synced || 0,
      is_answered: chatHistory.is_answered || 0,
      is_delete: 0
    })
    
    const created = await dexieDatabase.chatHistory.get(id)
    if (!created) {
      throw new Error('Failed to create chat history')
    }
    
    return created
  }

  /**
   * 根据ID查找聊天历史记录
   */
  // async findById(id: number): Promise<ChatHistoryEntity | null> {
  //   await dexieDatabase.initialize()
    
  //   const chatHistory = await dexieDatabase.chatHistory.get(id)
  //   return chatHistory || null
  // }

  /**
   * 根据prompt_uid查找聊天历史记录
   */
  async findByPromptUid(promptUid: string): Promise<ChatHistoryEntity[]> {
    await dexieDatabase.initialize()
    
    return await dexieDatabase.chatHistory
      .where('prompt_uid')
      .equals(promptUid)
      .and(item => item.is_delete === 0)
      .toArray()
  }


  /**
   * 查询唯一的历史记录
   * @param chat_group_id 
   * @param platform_id 
   * @param chat_sort 
   */
  async findUniqueHistory(chat_group_id: string, platform_id: number, chat_sort: number): Promise<ChatHistoryEntity | null>{
    await dexieDatabase.initialize()

    const record = await dexieDatabase.chatHistory
      .where('[chat_group_id+platform_id+chat_sort]')
      .equals([chat_group_id, platform_id, chat_sort])
      .and(item => item.is_delete === 0)
      .first()

    return record || null
  }


  /**
   * 根据平台ID查找聊天历史记录
   */
  // async findByPlatformId(platformId: number, options: {
  //   limit?: number
  //   offset?: number
  //   orderBy?: 'create_time' | 'id'
  //   orderDirection?: 'ASC' | 'DESC'
  // } = {}): Promise<ChatHistoryEntity[]> {
  //   await dexieDatabase.initialize()
    
  //   const { limit = 50, offset = 0 } = options
    
  //   let query = dexieDatabase.chatHistory
  //     .where('platform_id')
  //     .equals(platformId)
  //     .and(item => item.is_delete === 0)
    
  //   return await query
  //     .offset(offset)
  //     .limit(limit)
  //     .reverse() // 默认按时间倒序
  //     .toArray()
  // }

  /**
   * 查找所有聊天历史记录
   */
  async findAll(options: {
    limit?: number
    offset?: number
    platformId?: number
    orderBy?: 'create_time' | 'id'
    orderDirection?: 'ASC' | 'DESC'
  } = {}): Promise<ChatHistoryEntity[]> {
    await dexieDatabase.initialize()
    
    const { limit = 50, offset = 0, platformId } = options
    
    let query = dexieDatabase.chatHistory.where('is_delete').equals(0)
    
    if (platformId) {
      query = query.and(item => item.platform_id === platformId)
    }
    
    return await query
      .offset(offset)
      .limit(limit)
      .reverse() // 默认按时间倒序
      .toArray()
  }

  /**
   * 更新聊天历史记录
   */
  async update(id: number, updates: Partial<ChatHistoryEntity>): Promise<ChatHistoryEntity> {
    await dexieDatabase.initialize()
    
    const existing = await dexieDatabase.chatHistory.get(id)
    if (!existing) {
      throw new Error(`Chat history with id ${id} not found`)
    }
    
    await dexieDatabase.chatHistory.update(id, updates)
    
    const updated = await dexieDatabase.chatHistory.get(id)
    if (!updated) {
      throw new Error('Failed to update chat history')
    }
    
    return updated
  }

  /**
   * 软删除聊天历史记录
   */
  async softDelete(id: number): Promise<boolean> {
    await dexieDatabase.initialize()
    
    const existing = await dexieDatabase.chatHistory.get(id)
    if (!existing) {
      return false
    }
    
    await dexieDatabase.chatHistory.update(id, { is_delete: 1 })
    return true
  }

  /**
   * 硬删除聊天历史记录
   */
  async delete(id: number): Promise<boolean> {
    await dexieDatabase.initialize()
    
    await dexieDatabase.chatHistory.delete(id)
    return true
  }

  /**
   * 根据prompt_uid软删除所有相关记录
   */
  async softDeleteByPromptUid(promptUid: string): Promise<number> {
    await dexieDatabase.initialize()
    
    const records = await this.findByPromptUid(promptUid)
    
    for (const record of records) {
      await dexieDatabase.chatHistory.update(record.id!, { is_delete: 1 })
    }
    
    return records.length
  }

  /**
   * 统计聊天历史记录数量
   */
  async count(options: {
    platformId?: number
    chatUid?: string
  } = {}): Promise<number> {
    await dexieDatabase.initialize()
    
    const { platformId, chatUid } = options
    
    let query = dexieDatabase.chatHistory.where('is_delete').equals(0)
    
    if (platformId) {
      query = query.and(item => item.platform_id === platformId)
    }
    
    if (chatUid) {
      query = query.and(item => item.prompt_uid === chatUid)
    }
    
    return await query.count()
  }

  /**
   * 批量创建聊天历史记录
   */
  async bulkCreate(chatHistories: Omit<ChatHistoryEntity, 'id'>[]): Promise<ChatHistoryEntity[]> {
    await dexieDatabase.initialize()
    
    const now = Date.now()
    const records = chatHistories.map(history => ({
      ...history,
      create_time: history.create_time || now,
      is_synced: history.is_synced || 0,
      is_answered: history.is_answered || 0,
      is_delete: 0
    }))
    
    const ids = await dexieDatabase.chatHistory.bulkAdd(records, { allKeys: true })
    
    const created = await dexieDatabase.chatHistory
      .where('id')
      .anyOf(ids as number[])
      .toArray()
    
    return created
  }

  /**
   * 搜索聊天历史记录（按答案内容）
   */
  async searchByAnswer(searchTerm: string, options: {
    limit?: number
    platformId?: number
  } = {}): Promise<ChatHistoryEntity[]> {
    await dexieDatabase.initialize()

    const { limit = 50, platformId } = options

    let query = dexieDatabase.chatHistory.where('is_delete').equals(0)

    if (platformId) {
      query = query.and(item => item.platform_id === platformId)
    }

    return await query
      .filter(item =>
        item.chat_answer &&
        item.chat_answer.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .limit(limit)
      .toArray()
  }

  /**
   * 清空表中所有数据（硬删除）
   */
  async clear(): Promise<number> {
    await dexieDatabase.initialize()

    const count = await dexieDatabase.chatHistory.count()
    await dexieDatabase.chatHistory.clear()
    return count
  }
}

// 导出单例实例
export const chatHistoryDao = ChatHistoryDao.getInstance()
