import React from 'react'
import { cn } from '@/common/utils'

interface Tab {
  id: string
  label: string
  icon?: string
}

interface PlatformTabsProps {
  tabs: Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
  className?: string
}

export const PlatformTabs: React.FC<PlatformTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className
}) => {
  return (
    <div className={cn('platform-tabs', className)}>
      {tabs.map((tab) => (
        <button
          key={tab.id}
          className={cn('platform-tab', activeTab === tab.id && 'active')}
          onClick={() => onTabChange(tab.id)}
        >
          {tab.icon && <span className="tab-icon">{tab.icon}</span>}
          <span className="tab-label">{tab.label}</span>
        </button>
      ))}
    </div>
  )
}
