# 简化KimiAnswerService逻辑.md

基于现在的设计，页面不需要处理跳转逻辑了，因为现在是销毁逻辑，每次新页面，重新绑定监听，只需要判断是home页还是chat页即可。因此请帮我对`KimiAnswerService.ts`,`KimiConversationManager.ts`,`KimiPageDetector.ts`，`KimiSelectorManager.ts`.`KimiClipboardManager.ts`做出如下调整：
1. KimiAnswerService更名为KimiAnswerController，只保留流程逻辑，实现细节都迁移到其他类里，其他实现类都更名为Service。这样形成Controler流程编排，Service实现业务的架构
2. 基于url区分是home页还是chat页。chat也得典型特点是`https://www.kimi.com/chat/d2qr08fkgnun211bap2g`chat后面的编码是本聊天列表的唯一id,而home是典型的`https://www.kimi.com`这样的特点
3. home不需要开启监听，只打印即可 
4. chat页面才需要开启监听，监听问题和答案。监听的逻辑不变，先监听到class="main"，然后获得下面的class="chat-page chat"，class="chat-header"为头部，可以获得聊天列表的标题，class="chat-content-container"为聊天列表区域
5. 尽量简化代码，日志就使用简单的console.log,去掉冗余的逻辑。
  
请先分析我的需求，生成需求文档存放在specs/需求-简化KimiAnswerService逻辑/requirement.md中。等我确认完文档后再做进一步的处理。