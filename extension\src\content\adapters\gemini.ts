import { BaseAIAdapter } from './core/BaseAIAdapter'
import { GeminiConfig } from '../types/Consts'
// import { SelectorConfig } from '../types/PlatformConfigType'

export class GeminiAdapter extends BaseAIAdapter {
  // constructor() {
  //   super(GeminiConfig)
  // }

  // /**
  //  * 获取 Gemini 平台特定的选择器配置
  //  */
  // getSelectors(): SelectorConfig {
  //   return {
  //     inputField: [
  //       'rich-textarea[placeholder*="Enter a prompt"]',
  //       'textarea[aria-label*="Message"]'
  //     ],
  //     sendButton: [
  //       'button[aria-label*="Send"]',
  //       'button[data-testid="send-button"]'
  //     ]
  //   }
  // }

}
