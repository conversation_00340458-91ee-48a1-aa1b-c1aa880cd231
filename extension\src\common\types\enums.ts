
// 消息类型
export enum MessageType {
    // 设置相关消息
    GET_SETTINGS = 'GET_SETTINGS',
    // 保存设置
    UPDATE_SETTINGS = 'UPDATE_SETTINGS',
  
    // 更新Favicon
    UPDATE_PLATFORM_FAVICON = 'UPDATE_PLATFORM_FAVICON',
  
    // 数据库操作消息类型
    // 新建提示词 
    DB_CHAT_PROMPT_CREATE = 'DB_CHAT_PROMPT_CREATE',
    // 获得提示词列表
    DB_CHAT_PROMPT_LIST_GET = 'DB_CHAT_PROMPT_LIST_GET',

    // 聊天历史相关
    DB_CHAT_HISTORY_CREATE = 'DB_CHAT_HISTORY_CREATE',
    DB_CHAT_HISTORY_UPDATE = 'DB_CHAT_HISTORY_UPDATE',

    // 获取所有平台列表
    DB_PLATFORM_GET_LIST = 'DB_PLATFORM_GET_LIST',

    // 数据库查看相关消息类型
    DB_GET_ALL_TABLES = 'DB_GET_ALL_TABLES',
    DB_GET_TABLE_DATA = 'DB_GET_TABLE_DATA',
    DB_GET_TABLE_COUNT = 'DB_GET_TABLE_COUNT',
    DB_DELETE_RECORD = 'DB_DELETE_RECORD',
    DB_CLEAR_TABLE = 'DB_CLEAR_TABLE',

    // Obsidian 导出相关
    OBSIDIAN_DOWNLOAD_FILE = 'OBSIDIAN_DOWNLOAD_FILE'
  }