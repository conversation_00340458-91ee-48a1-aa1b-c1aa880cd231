
## 问题1：popup应该与官网和欢迎页是什么关系？如何设计登陆系统？

### 关系设计
- **popup作为入口中心**：popup应该是用户与整个EchoSync生态系统的主要交互入口
- **官网作为营销和注册中心**：官网主要负责产品展示、用户注册、付费订阅管理
- **数据同步桥梁**：popup和官网通过统一的后端API（Supabase）实现用户数据同步

### 登录系统设计
```
登录流程：
1. 用户在popup点击登录 → 打开官网登录页面
2. 官网完成身份验证（支持Google/GitHub OAuth + 邮箱登录）
3. 登录成功后返回token给extension
4. Extension存储token并同步用户配置到本地
5. Popup显示登录状态和用户信息
```

### 技术实现
- 使用Supabase Auth统一管理用户身份
- Extension通过chrome.runtime.sendMessage与website通信
- Token存储在chrome.storage.sync中，支持跨设备同步

---