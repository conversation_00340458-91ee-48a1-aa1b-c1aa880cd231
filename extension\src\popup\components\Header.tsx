import React from 'react'
import { UserStatusBadge } from './button'

interface HeaderProps {
  isFullscreen?: boolean
  onToggleFullscreen?: () => void
  userPlan?: 'free' | 'pro' | 'plus' | 'max'
  userName?: string
}

export function Header({
  isFullscreen = false,
  onToggleFullscreen,
  userPlan = 'pro',
  userName = 'U'
}: HeaderProps) {
  return (
    <div className="header">
      <div className="user-status">
        <div className="user-avatar">
          {userName.charAt(0).toUpperCase()}
        </div>
        <UserStatusBadge plan={userPlan} />
      </div>

      <button
        className="fullscreen-btn"
        onClick={onToggleFullscreen}
        title={isFullscreen ? '退出全屏' : '全屏模式'}
      >
        {isFullscreen ? '⛉' : '⛶'}
      </button>
    </div>
  )
}
