import { Singleton } from "../base/Singleton";
import { DatabaseResult } from "../types/comm_vo";
import { ChatPromptListResp, CreateChatPromptReq, CreateChatPromptResp } from "../types/content_vo";
import { MessageType } from "../types/enums";
import { MessagingService } from "./MessagingService";

export class ChatPromptDatabaseProxy extends Singleton<ChatPromptDatabaseProxy> {
  /**
   * 创建提示词
   */
  async createChatPrompt(input: CreateChatPromptReq): Promise<DatabaseResult<CreateChatPromptResp>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CHAT_PROMPT_CREATE, input)
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Create chat prompt failed'
      }
    }
  }

  /**
   * 获得提示词列表
   */
  async getChatPromptList(params?: { limit?: number; order_direction?: 'ASC' | 'DESC' }): Promise<DatabaseResult<ChatPromptListResp[]>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CHAT_PROMPT_LIST_GET, params)
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get chat prompt list failed'
      }
    }
  }
}

// 导出单例实例
export const chatPromptDatabaseProxy = ChatPromptDatabaseProxy.getInstance()