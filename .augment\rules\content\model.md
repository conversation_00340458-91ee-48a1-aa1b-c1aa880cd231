---
type: "agent_requested"
description: "Content Script数据模型开发规则"
---
# 数据模型规则

## 核心职责
- **数据共享**: 管理模块间共享状态
- **位置**: `content/model/`
- **命名**: `ModelName.ts`

## 基础结构
```typescript
class ModelName extends Singleton<ModelName> {
  private data: DataType;

  public getData(): DataType { return this.data; }
  public setData(data: DataType): void { this.data = data; }
}
```

## 核心规则
- 继承Singleton基类
- 通过getInstance()获取实例
- Capture负责数据更新
- Inject负责数据消费
- 提供明确的访问接口

